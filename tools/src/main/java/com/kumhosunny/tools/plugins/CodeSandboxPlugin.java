package com.kumhosunny.tools.plugins;

import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.RestClientException;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import com.microsoft.semantickernel.semanticfunctions.annotations.DefineKernelFunction;
import com.microsoft.semantickernel.semanticfunctions.annotations.KernelFunctionParameter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.kumhosunny.common.entity.CodeLanguage;
import com.kumhosunny.common.entity.CodeExecutionRequest;
import com.kumhosunny.common.entity.CodeExecutionResponse;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.time.LocalDateTime;

/**
 * 沙盒代码执行插件
 * 基于Judge0 API的安全代码执行环境
 * 
 * <AUTHOR>
 */
@Component
public class CodeSandboxPlugin {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final String judge0BaseUrl;

    // 常用语言ID映射 - 根据实际服务器支持的语言更新
    private static final Map<String, Integer> LANGUAGE_MAP = new HashMap<>();
    static {
        // C/C++ 语言
        LANGUAGE_MAP.put("c", 1); // C (Clang 10.0.1)
        LANGUAGE_MAP.put("cpp", 2); // C++ (Clang 10.0.1)
        LANGUAGE_MAP.put("c++", 2); // C++ (Clang 10.0.1)

        // C# 语言
        LANGUAGE_MAP.put("csharp", 22); // C# (Mono 6.12.0.122)
        LANGUAGE_MAP.put("c#", 22); // C# (Mono 6.12.0.122)
        LANGUAGE_MAP.put("dotnet", 21); // C# (.NET Core SDK 3.1.406)

        // Java 语言
        LANGUAGE_MAP.put("java", 4); // Java (OpenJDK 14.0.1)

        // Python 语言
        LANGUAGE_MAP.put("python", 10); // Python for ML (3.7.7)
        LANGUAGE_MAP.put("python3", 10); // Python for ML (3.7.7)
        LANGUAGE_MAP.put("py", 10); // Python for ML (3.7.7)

        // F# 语言
        LANGUAGE_MAP.put("fsharp", 24); // F# (.NET Core SDK 3.1.406)
        LANGUAGE_MAP.put("f#", 24); // F# (.NET Core SDK 3.1.406)

        // Visual Basic
        LANGUAGE_MAP.put("vb", 20); // Visual Basic.Net
        LANGUAGE_MAP.put("vbnet", 20); // Visual Basic.Net

        // Nim 语言
        LANGUAGE_MAP.put("nim", 9); // Nim (stable)

        // Bosque 语言
        LANGUAGE_MAP.put("bosque", 11); // Bosque (latest)

        // C3 语言
        LANGUAGE_MAP.put("c3", 3); // C3 (latest)
    }

    public CodeSandboxPlugin() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
        this.judge0BaseUrl = "http://**************:2358";
    }

    /**
     * 获取支持的编程语言列表
     * 
     * @return 支持的语言列表
     */
    @DefineKernelFunction(name = "getSupportedLanguages", description = "获取代码执行环境支持的所有编程语言")
    public String getSupportedLanguages() {
        try {
            String url = judge0BaseUrl + "/languages";

            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getBody() == null) {
                return "获取语言列表失败：响应为空";
            }

            List<CodeLanguage> languages = objectMapper.readValue(
                    response.getBody(),
                    new TypeReference<List<CodeLanguage>>() {
                    });

            if (languages == null || languages.isEmpty()) {
                return "未找到支持的编程语言";
            }

            StringBuilder result = new StringBuilder();
            result.append("支持的编程语言列表：\n\n");

            for (CodeLanguage language : languages) {
                result.append(String.format("ID: %d - %s\n", language.getId(), language.getName()));
            }

            result.append(String.format("\n总计：%d 种语言", languages.size()));
            result.append(String.format("\n查询时间：%s", LocalDateTime.now()));

            return result.toString();

        } catch (RestClientException e) {
            return String.format("获取语言列表失败：网络连接错误 - %s", e.getMessage());
        } catch (Exception e) {
            return String.format("获取语言列表失败：%s", e.getMessage());
        }
    }

    /**
     * 获取常用语言映射
     * 
     * @return 常用语言映射列表
     */
    @DefineKernelFunction(name = "getCommonLanguages", description = "获取常用编程语言名称及其对应的ID映射")
    public String getCommonLanguages() {
        StringBuilder result = new StringBuilder();
        result.append("常用编程语言映射：\n\n");

        result.append("🐍 Python: python, python3, py (ID: 10)\n");
        result.append("☕ Java: java (ID: 4)\n");
        result.append("🔧 C语言: c (ID: 1)\n");
        result.append("⚡ C++: cpp, c++ (ID: 2)\n");
        result.append("🔷 C#: csharp, c# (ID: 22), dotnet (ID: 21)\n");
        result.append("📐 F#: fsharp, f# (ID: 24)\n");
        result.append("📘 Visual Basic: vb, vbnet (ID: 20)\n");
        result.append("👑 Nim: nim (ID: 9)\n");
        result.append("🌟 Bosque: bosque (ID: 11)\n");
        result.append("🔥 C3: c3 (ID: 3)\n");

        result.append(String.format("\n查询时间：%s", LocalDateTime.now()));
        result.append("\n\n提示：使用 getSupportedLanguages() 获取完整的语言列表");

        return result.toString();
    }

    /**
     * 执行代码（同步模式）
     * 
     * @param languageId 编程语言ID
     * @param sourceCode 源代码
     * @param stdin      标准输入（可选）
     * @return 执行结果
     */
    @DefineKernelFunction(name = "executeCode", description = "在沙盒环境中执行代码")
    public String executeCode(
            @KernelFunctionParameter(name = "languageId", description = "编程语言ID") Integer languageId,
            @KernelFunctionParameter(name = "sourceCode", description = "要执行的源代码") String sourceCode,
            @KernelFunctionParameter(name = "stdin", description = "标准输入数据（可选）") String stdin) {

        try {
            // 创建执行请求
            CodeExecutionRequest request = new CodeExecutionRequest(languageId, sourceCode, stdin);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<CodeExecutionRequest> entity = new HttpEntity<>(request, headers);

            // 同步执行
            String url = judge0BaseUrl + "/submissions?base64_encoded=false&wait=true";

            ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.POST, entity, String.class);

            if (response.getBody() == null) {
                return "代码执行失败：响应为空";
            }

            CodeExecutionResponse executionResult = objectMapper.readValue(
                    response.getBody(), CodeExecutionResponse.class);

            return formatExecutionResult(executionResult);

        } catch (RestClientException e) {
            return String.format("代码执行失败：网络连接错误 - %s", e.getMessage());
        } catch (Exception e) {
            return String.format("代码执行失败：%s", e.getMessage());
        }
    }

    /**
     * 执行代码（通过语言名称）
     * 
     * @param languageName 编程语言名称
     * @param sourceCode   源代码
     * @param stdin        标准输入（可选）
     * @return 执行结果
     */
    @DefineKernelFunction(name = "executeCodeByLanguage", description = "通过语言名称在沙盒环境中执行代码")
    public String executeCodeByLanguage(
            @KernelFunctionParameter(name = "languageName", description = "编程语言名称（如: python, java, c, cpp等）") String languageName,
            @KernelFunctionParameter(name = "sourceCode", description = "要执行的源代码") String sourceCode,
            @KernelFunctionParameter(name = "stdin", description = "标准输入数据（可选）") String stdin) {

        try {
            // 获取语言ID
            Integer languageId = getLanguageIdByName(languageName.toLowerCase());
            if (languageId == null) {
                StringBuilder errorMsg = new StringBuilder();
                errorMsg.append(String.format("❌ 不支持的编程语言：%s\n\n", languageName));
                errorMsg.append("✅ 支持的语言别名：\n");
                errorMsg.append("• Python: python, python3, py\n");
                errorMsg.append("• Java: java\n");
                errorMsg.append("• C: c\n");
                errorMsg.append("• C++: cpp, c++\n");
                errorMsg.append("• C#: csharp, c#, dotnet\n");
                errorMsg.append("• F#: fsharp, f#\n");
                errorMsg.append("• Visual Basic: vb, vbnet\n");
                errorMsg.append("• Nim: nim\n");
                errorMsg.append("• Bosque: bosque\n");
                errorMsg.append("• C3: c3\n");
                errorMsg.append("\n💡 使用 getCommonLanguages() 查看详细映射");
                return errorMsg.toString();
            }

            return executeCode(languageId, sourceCode, stdin);

        } catch (Exception e) {
            return String.format("代码执行失败：%s", e.getMessage());
        }
    }

    /**
     * 异步执行代码
     * 
     * @param languageId 编程语言ID
     * @param sourceCode 源代码
     * @param stdin      标准输入（可选）
     * @return 执行令牌
     */
    @DefineKernelFunction(name = "executeCodeAsync", description = "异步执行代码，返回执行令牌")
    public String executeCodeAsync(
            @KernelFunctionParameter(name = "languageId", description = "编程语言ID") Integer languageId,
            @KernelFunctionParameter(name = "sourceCode", description = "要执行的源代码") String sourceCode,
            @KernelFunctionParameter(name = "stdin", description = "标准输入数据（可选）") String stdin) {

        try {
            // 创建执行请求
            CodeExecutionRequest request = new CodeExecutionRequest(languageId, sourceCode, stdin);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<CodeExecutionRequest> entity = new HttpEntity<>(request, headers);

            // 异步执行
            String url = judge0BaseUrl + "/submissions?base64_encoded=false";

            ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.POST, entity, String.class);

            if (response.getBody() == null) {
                return "异步代码执行失败：响应为空";
            }

            CodeExecutionResponse executionResult = objectMapper.readValue(
                    response.getBody(), CodeExecutionResponse.class);

            return String.format("异步执行已提交\n执行令牌：%s\n提交时间：%s\n" +
                    "请使用 getExecutionResult 函数查询执行结果",
                    executionResult.getToken(), LocalDateTime.now());

        } catch (RestClientException e) {
            return String.format("异步代码执行失败：网络连接错误 - %s", e.getMessage());
        } catch (Exception e) {
            return String.format("异步代码执行失败：%s", e.getMessage());
        }
    }

    /**
     * 获取异步执行结果
     * 
     * @param token 执行令牌
     * @return 执行结果
     */
    @DefineKernelFunction(name = "getExecutionResult", description = "通过令牌获取异步执行结果")
    public String getExecutionResult(
            @KernelFunctionParameter(name = "token", description = "执行令牌") String token) {

        try {
            String url = judge0BaseUrl + "/submissions/" + token + "?base64_encoded=false";

            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getBody() == null) {
                return "获取执行结果失败：响应为空";
            }

            CodeExecutionResponse executionResult = objectMapper.readValue(
                    response.getBody(), CodeExecutionResponse.class);

            return formatExecutionResult(executionResult);

        } catch (RestClientException e) {
            return String.format("获取执行结果失败：网络连接错误 - %s", e.getMessage());
        } catch (Exception e) {
            return String.format("获取执行结果失败：%s", e.getMessage());
        }
    }

    /**
     * 根据语言名称获取语言ID
     */
    private Integer getLanguageIdByName(String languageName) {
        return LANGUAGE_MAP.get(languageName);
    }

    /**
     * 格式化执行结果
     */
    private String formatExecutionResult(CodeExecutionResponse result) {
        StringBuilder output = new StringBuilder();

        output.append("=== 代码执行结果 ===\n");

        if (result.getStatus() != null) {
            output.append(String.format("执行状态：%s\n", result.getStatus().getDescription()));
        }

        if (result.getStdout() != null && !result.getStdout().isEmpty()) {
            output.append(String.format("标准输出：\n%s\n", result.getStdout()));
        }

        if (result.getStderr() != null && !result.getStderr().isEmpty()) {
            output.append(String.format("错误输出：\n%s\n", result.getStderr()));
        }

        if (result.getCompileOutput() != null && !result.getCompileOutput().isEmpty()) {
            output.append(String.format("编译输出：\n%s\n", result.getCompileOutput()));
        }

        if (result.getTime() != null) {
            output.append(String.format("执行时间：%s 秒\n", result.getTime()));
        }

        if (result.getMemory() != null) {
            output.append(String.format("内存使用：%s KB\n", result.getMemory()));
        }

        if (result.getExitCode() != null) {
            output.append(String.format("退出代码：%d\n", result.getExitCode()));
        }

        if (result.getToken() != null) {
            output.append(String.format("执行令牌：%s\n", result.getToken()));
        }

        output.append(String.format("查询时间：%s", LocalDateTime.now()));

        return output.toString();
    }
}