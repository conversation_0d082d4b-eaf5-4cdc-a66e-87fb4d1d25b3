package com.kumhosunny.tools.plugins;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.RestClientException;

import com.microsoft.semantickernel.semanticfunctions.annotations.DefineKernelFunction;
import com.microsoft.semantickernel.semanticfunctions.annotations.KernelFunctionParameter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.kumhosunny.common.entity.SearchResponse;
import com.kumhosunny.common.config.SearchProperties;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.time.LocalDateTime;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 搜索插件
 * 提供智能网络搜索功能，返回结构化数据供AI分析处理
 * 
 * <AUTHOR>
 */
@Component
public class SearchPlugin {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final SearchProperties searchProperties;

    /**
     * 默认构造函数
     */
    public SearchPlugin() {
        this(null);
    }

    /**
     * 主构造函数
     */
    @Autowired(required = false)
    public SearchPlugin(SearchProperties searchProperties) {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
        this.searchProperties = searchProperties != null ? searchProperties : createDefaultProperties();
    }

    /**
     * 网络搜索
     * 
     * @param query 搜索查询
     * @return JSON格式的搜索结果数据
     */
    @DefineKernelFunction(name = "webSearch", description = "执行网络搜索并返回JSON格式的结构化搜索结果。返回的数据包含查询词、总结果数、具体搜索结果列表等信息，需要AI解析并以用户友好的方式呈现给用户")
    public String webSearch(
            @KernelFunctionParameter(name = "query", description = "搜索查询字符串") String query) {

        try {
            String response = performSearch(query);
            if (response == null || response.isEmpty()) {
                return createErrorResponse("未获取到有效响应", query);
            }

            SearchResponse searchResponse = objectMapper.readValue(response, SearchResponse.class);
            return buildSearchResultJson(searchResponse, query);

        } catch (RestClientException e) {
            return createErrorResponse("网络连接错误 - " + e.getMessage(), query);
        } catch (Exception e) {
            return createErrorResponse("搜索异常 - " + e.getMessage(), query);
        }
    }

    /**
     * 获取搜索建议
     * 
     * @param topic 主题
     * @return 搜索建议
     */
    @DefineKernelFunction(name = "getSearchSuggestions", description = "根据主题获取搜索建议")
    public String getSearchSuggestions(
            @KernelFunctionParameter(name = "topic", description = "搜索主题") String topic) {

        try {
            String searchResult = webSearch(topic);
            return searchResult.contains("\"error\":")
                    ? generateFallbackSuggestions(topic)
                    : generateSearchSuggestions(topic);
        } catch (Exception e) {
            return generateFallbackSuggestions(topic);
        }
    }

    /**
     * 执行搜索请求
     */
    private String performSearch(String query) {
        String apiUrl = searchProperties.getDuckduckgo().getApiUrl();
        String encodedQuery = URLEncoder.encode(query, StandardCharsets.UTF_8);
        String url = String.format("%s/search?q=%s&format=json", apiUrl, encodedQuery);

        return restTemplate.getForObject(url, String.class);
    }

    /**
     * 构建搜索结果JSON
     */
    private String buildSearchResultJson(SearchResponse searchResponse, String query)
            throws JsonProcessingException {

        if (searchResponse.getResults() == null || searchResponse.getResults().isEmpty()) {
            return createErrorResponse("未找到相关结果", query);
        }

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("query", query);
        resultMap.put("total_results", searchResponse.getNumberOfResults());

        List<SearchResponse.SearchResult> results = searchResponse.getResults();
        int maxResults = searchProperties.getDuckduckgo().getMaxResults();
        List<SearchResponse.SearchResult> limitedResults = results.subList(0, Math.min(results.size(), maxResults));

        resultMap.put("returned_results", limitedResults.size());
        List<Map<String, String>> list = new ArrayList<>();
        for (SearchResponse.SearchResult limitedResult : limitedResults) {
            Map<String, String> stringStringMap = mapSearchResult(limitedResult);
            list.add(stringStringMap);
        }
        resultMap.put("results", list);
        resultMap.put("search_time", LocalDateTime.now().toString());

        return objectMapper.writeValueAsString(resultMap);
    }

    /**
     * 映射搜索结果对象
     */
    private Map<String, String> mapSearchResult(SearchResponse.SearchResult result) {
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("title", getValueOrDefault(result.getTitle(), "无标题"));
        resultMap.put("url", getValueOrDefault(result.getUrl(), ""));
        resultMap.put("content", getValueOrDefault(result.getContent(), ""));
        resultMap.put("engine", getValueOrDefault(result.getEngine(), ""));
        resultMap.put("published_date", getValueOrDefault(result.getPublishedDate(), ""));
        return resultMap;
    }

    /**
     * 创建错误响应
     */
    private String createErrorResponse(String error, String query) {
        try {
            Map<String, String> errorMap = new HashMap<>();
            errorMap.put("error", error);
            errorMap.put("query", query);
            return objectMapper.writeValueAsString(errorMap);
        } catch (JsonProcessingException e) {
            return String.format("{\"error\": \"%s\", \"query\": \"%s\"}", error, query);
        }
    }

    /**
     * 获取值或默认值
     */
    private String getValueOrDefault(String value, String defaultValue) {
        return value != null ? value : defaultValue;
    }

    /**
     * 生成搜索建议
     */
    private String generateSearchSuggestions(String topic) {
        String[] suggestionTemplates = {
                "%s 最新消息", "%s 详细介绍", "%s 使用方法", "%s 相关新闻",
                "%s 发展趋势", "%s 深度分析", "%s 专家观点", "%s 实用指南"
        };

        return buildSuggestionsResponse(topic, suggestionTemplates);
    }

    /**
     * 生成备用搜索建议
     */
    private String generateFallbackSuggestions(String topic) {
        String[] fallbackTemplates = {
                "%s 是什么", "%s 怎么用", "%s 最佳实践", "%s 常见问题",
                "%s 替代方案", "%s 最新版本", "%s 性能优化", "%s 安全注意事项"
        };

        return buildSuggestionsResponse(topic, fallbackTemplates);
    }

    /**
     * 构建建议响应
     */
    private String buildSuggestionsResponse(String topic, String[] templates) {
        StringBuilder suggestions = new StringBuilder();
        suggestions.append(String.format("基于主题 '%s' 的搜索建议：\n", topic));

        for (int i = 0; i < templates.length; i++) {
            suggestions.append(String.format("%d. %s\n", i + 1, String.format(templates[i], topic)));
        }

        return suggestions.toString();
    }

    /**
     * 创建默认配置
     */
    private SearchProperties createDefaultProperties() {
        SearchProperties properties = new SearchProperties();
        SearchProperties.DuckDuckGo duckduckgo = new SearchProperties.DuckDuckGo();
        duckduckgo.setApiUrl("http://192.168.170.66:8888");
        duckduckgo.setTimeout(30000);
        duckduckgo.setMaxResults(10);
        duckduckgo.setSummaryMaxLength(200);
        duckduckgo.setRegion("zh-CN");
        properties.setDuckduckgo(duckduckgo);
        return properties;
    }
}