package com.kumhosunny.tools.plugins;

import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;

import com.microsoft.semantickernel.semanticfunctions.annotations.DefineKernelFunction;
import com.microsoft.semantickernel.semanticfunctions.annotations.KernelFunctionParameter;

// 新增：HTTP 客户端与 JSON 处理
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.HttpClientErrorException;

import java.time.Duration;
import org.springframework.boot.web.client.RestTemplateBuilder;
import javax.annotation.PostConstruct;

import java.util.List;
import java.util.Map;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.ArrayList;
import java.util.HashMap;
import org.springframework.web.util.UriComponentsBuilder;
import java.net.URI;

// 导入 common 模块中的实体类
import com.kumhosunny.common.entity.WorkflowInfo;
import com.kumhosunny.common.entity.WorkflowListResponse;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.core.JsonProcessingException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * N8n 工作流插件
 * 提供工作流调用、列表和邮件发送功能
 */
@Component
public class N8nPlugin {

    private static final Logger log = LoggerFactory.getLogger(N8nPlugin.class);

    /**
     * N8n Public API 访问令牌 (Bearer Token)
     * 支持从配置文件或环境变量读取
     */
    @Value("${n8n.api-key:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkMzYxMzZhOC0zM2U1LTQzMWUtODdiMS1jMjY1Y2Q4MjZiMzgiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUwMDU4NTE0fQ.5VUlnq-nnkMaZDk9Uklbz5Zf_W1pMG8OllI6WYNkcms}")
    private String n8nApiKey;

    /**
     * N8n 实例地址，支持配置文件设置
     */
    @Value("${n8n.base-url:http://**************:15678}")
    private String n8nBaseUrl;

    /**
     * HTTP 请求超时设置（秒）
     */
    @Value("${n8n.timeout:120}")
    private int timeoutSeconds;

    private RestTemplate restTemplate;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @PostConstruct
    public void init() {
        // 在依赖注入完成后初始化 RestTemplate
        this.restTemplate = new RestTemplateBuilder()
                .setConnectTimeout(Duration.ofSeconds(10))
                .setReadTimeout(Duration.ofSeconds(timeoutSeconds))
                .build();
    }

    /**
     * 确保 RestTemplate 已初始化
     */
    private void ensureInitialized() {
        if (restTemplate == null) {
            int timeout = (timeoutSeconds > 0) ? timeoutSeconds : 120;
            this.restTemplate = new RestTemplateBuilder()
                    .setConnectTimeout(Duration.ofSeconds(10))
                    .setReadTimeout(Duration.ofSeconds(timeout))
                    .build();
        }
    }

    /**
     * 标准化 URL，确保正确的路径
     */
    private String normalizeUrl(String path) {
        // 处理 n8nBaseUrl 为 null 的情况
        String baseUrlToUse = (n8nBaseUrl != null) ? n8nBaseUrl : "http://**************:15678";
        String baseUrl = baseUrlToUse.endsWith("/") ? baseUrlToUse.substring(0, baseUrlToUse.length() - 1)
                : baseUrlToUse;
        String apiPath = path.startsWith("/") ? path : "/" + path;
        return baseUrl + apiPath;
    }

    /**
     * 创建标准的 HTTP 请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        // 处理 n8nApiKey 为 null 的情况
        String apiKeyToUse = (n8nApiKey != null) ? n8nApiKey
                : "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkMzYxMzZhOC0zM2U1LTQzMWUtODdiMS1jMjY1Y2Q4MjZiMzgiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUwMDU4NTE0fQ.5VUlnq-nnkMaZDk9Uklbz5Zf_W1pMG8OllI6WYNkcms";
        headers.set("X-N8N-API-KEY", apiKeyToUse);
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(java.util.Collections.singletonList(MediaType.APPLICATION_JSON));
        return headers;
    }

    /**
     * 根据工作流ID调用N8n工作流。
     * <p>
     * 该方法会根据提供的工作流ID，查找其对应的Webhook地址，然后发起POST请求来执行工作流。
     *
     * @param workflowId 要执行的N8n工作流的ID。
     * @param input      传递给工作流的输入数据。
     * @param user       调用用户的标识 (可选)。
     * @return 执行结果的格式化字符串。
     */
    @DefineKernelFunction(name = "callWorkflow", description = "根据工作流ID调用N8n工作流。")
    public String callWorkflow(
            @KernelFunctionParameter(name = "workflowId", description = "要执行的N8n工作流的ID", required = true) String workflowId,
            @KernelFunctionParameter(name = "input", description = "传递给工作流的输入数据", required = true) String input,
            @KernelFunctionParameter(name = "user", description = "调用用户的标识", required = false) String user,
            @KernelFunctionParameter(name = "user", description = "调用用户的标识", required = false) String webhook
            ) {


        ensureInitialized();

        if (workflowId == null || workflowId.trim().isEmpty()) {
            // 如果 workflowId 为空，直接将 input 赋给它，以触发后续的名称匹配逻辑
            workflowId = input;
        }

        // 1. 优先通过 ID 查找工作流
        WorkflowListResponse workflowListResponse = getWorkflows();
        final String effectiveWorkflowId = workflowId; // 创建一个 final 变量
        WorkflowInfo workflow = workflowListResponse.getWorkflows().stream()
                .filter(w -> effectiveWorkflowId.equals(w.getId()))
                .findFirst()
                .orElse(null);

        // 2. 如果通过 ID 找不到，启动回退机制：通过名称模糊匹配
        if (workflow == null) {
            log.warn("无法通过 ID '{}' 找到工作流，启动按名称匹配的回退机制。", effectiveWorkflowId);
            workflow = workflowListResponse.getWorkflows().stream()
                    .filter(w -> w.getName() != null && w.getName().contains(effectiveWorkflowId))
                    .findFirst()
                    .orElse(null);

            if (workflow != null) {
                log.info("回退机制成功：通过名称 '{}' 匹配到工作流 '{}' (ID: {})", effectiveWorkflowId, workflow.getName(),
                        workflow.getId());
            }
        }

        if (workflow == null) {
            return formatGenericError("Webhook (by ID)", "workflowId: " + effectiveWorkflowId,
                    new Exception("找不到ID为 '" + effectiveWorkflowId + "' 的工作流，且按名称搜索也无匹配项。"));
        }


        String webhookUrl = workflow.getWebhookUrl();
        if(null == webhookUrl || webhookUrl.isEmpty()){
            webhookUrl = webhook;
        }
        if (webhookUrl == null || webhookUrl.isEmpty()) {
            return formatGenericError("Webhook (by ID)", "workflowId: " + effectiveWorkflowId,
                    new Exception("工作流 '" + workflow.getName() + "' 不支持 webhook 调用。"));

        }

        // 2. 统一的调用逻辑
        try {
            // 强制使用 POST 方法并准备请求体
            HttpMethod method = HttpMethod.POST;

            HttpHeaders headers = new HttpHeaders();
            MediaType mediaType = new MediaType("application", "json", StandardCharsets.UTF_8);
            headers.setContentType(mediaType);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

            Map<String, String> bodyMap = new HashMap<>();
            bodyMap.put("input", input);
            bodyMap.put("user", (user != null && !user.isEmpty()) ? user : "default-user");

            String jsonBody = objectMapper.writeValueAsString(bodyMap);
            HttpEntity<String> entity = new HttpEntity<>(jsonBody, headers);

            ResponseEntity<String> response = restTemplate.exchange(webhookUrl, method, entity, String.class);
            return formatSuccessResponse(method.name(), webhookUrl, input, response);

        } catch (HttpClientErrorException e) {
            return formatErrorResponse(effectiveWorkflowId, "Webhook (by ID)", e);
        } catch (ResourceAccessException e) {
            return formatConnectionError("Webhook (by ID)", effectiveWorkflowId, e);
        } catch (Exception e) {
            return formatGenericError("Webhook (by ID)", effectiveWorkflowId, e);
        }
    }

    // --- 辅助方法，用于格式化响应 ---

    private String formatSuccessResponse(String method, String url, String input, ResponseEntity<String> response) {
        if (response.getStatusCode().is2xxSuccessful()) {
            return String.format("工作流调用成功。响应: %s", response.getBody());
        } else {
            return String.format("工作流调用失败。状态: %d, 响应: %s", response.getStatusCodeValue(), response.getBody());
        }
    }

    private String formatErrorResponse(String config, String method, HttpClientErrorException e) {
        if (e.getStatusCode().value() == 404) {
            return String.format("工作流调用失败：找不到工作流或端点(404)。请检查工作流ID '%s' 是否正确，并确认其已激活。", config);
        }
        return String.format("工作流调用失败：客户端错误(%d)。详情：%s", e.getStatusCode().value(), e.getResponseBodyAsString());
    }

    private String formatConnectionError(String method, String config, ResourceAccessException e) {
        return String.format("工作流调用失败：网络连接失败。请检查N8n服务是否可达。详情：%s", e.getMessage());
    }

    private String formatGenericError(String method, String config, Exception e) {
        return String.format("工作流调用时发生未知异常。详情：%s", e.getMessage());
    }

    /**
     * 获取可用的工作流列表
     *
     * @return 工作流列表对象
     */
    @DefineKernelFunction(name = "getWorkflows", description = "获取所有可用的 N8n 工作流")
    public WorkflowListResponse getWorkflows() {
        ensureInitialized();
        String url = normalizeUrl("/api/v1/workflows");

        HttpEntity<Void> entity = new HttpEntity<>(createHeaders());

        try {
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            if (response.getStatusCode().is2xxSuccessful()) {
                // 解析工作流数据并创建对象列表
                JsonNode root = objectMapper.readTree(response.getBody());
                JsonNode data = root.path("data");

                if (data.isArray()) {
                    List<WorkflowInfo> workflowList = new ArrayList<>();

                    for (JsonNode wf : data) {
                        String id = wf.path("id").asText("N/A");
                        String name = wf.path("name").asText("未命名工作流");
                        boolean active = wf.path("active").asBoolean(false);
                        String createdAt = wf.path("createdAt").asText("N/A");
                        String updatedAt = wf.path("updatedAt").asText("N/A");
                        int triggerCount = wf.path("triggerCount").asInt(0);
                        boolean isArchived = wf.path("isArchived").asBoolean(false);

                        // 提取 Webhook URL
                        String webhookUrl = null;
                        JsonNode nodes = wf.path("nodes");
                        if (nodes.isArray() && nodes.size() > 0) {
                            JsonNode firstNode = nodes.get(0);
                            if (firstNode.path("type").asText().equals("n8n-nodes-base.webhook")) {
                                String webhookId = firstNode.path("webhookId").asText();
                                if (webhookId != null && !webhookId.isEmpty()) {
                                    webhookUrl = normalizeUrl("/webhook/" + webhookId);
                                }
                            }
                        }

                        WorkflowInfo info = new WorkflowInfo(id, name, active, createdAt, updatedAt, triggerCount,
                                isArchived, webhookUrl);
                        workflowList.add(info);
                    }

                    return new WorkflowListResponse(workflowList);
                } else {
                    // 返回空列表而不是抛出异常
                    return new WorkflowListResponse(new ArrayList<>());
                }
            }
            // 非 200 状态码，返回空列表
            return new WorkflowListResponse(new ArrayList<>());
        } catch (HttpClientErrorException e) {
            // 异常情况返回空列表
            return new WorkflowListResponse(new ArrayList<>());
        } catch (ResourceAccessException e) {
            // 网络异常返回空列表
            return new WorkflowListResponse(new ArrayList<>());
        } catch (Exception e) {
            // 其他异常返回空列表
            return new WorkflowListResponse(new ArrayList<>());
        }
    }

    /**
     * 格式化日期时间
     *
     * @param dateTime 日期时间字符串
     * @return 格式化后的日期时间字符串
     */
    private String formatDateTime(String dateTime) {
        if (dateTime == null || dateTime.isEmpty()) {
            return "未知";
        }
        try {
            // 解析ISO 8601格式
            java.time.OffsetDateTime odt = java.time.OffsetDateTime.parse(dateTime);
            // 转换为本地时间
            java.time.LocalDateTime ldt = odt.toLocalDateTime();
            // 格式化输出
            return ldt.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } catch (java.time.format.DateTimeParseException e) {
            return dateTime; // 解析失败则返回原始字符串
        }
    }

    /**
     * 获取格式化的工作流列表字符串（用于显示）
     *
     * @return 格式化的工作流列表字符串
     */
    @DefineKernelFunction(name = "getWorkflowsFormatted", description = "获取格式化显示的 N8n 工作流列表")
    public String getWorkflowsFormatted() {
        WorkflowListResponse response = getWorkflows();
        List<WorkflowInfo> workflows = response.getWorkflows();

        if (workflows.isEmpty()) {
            return "暂无可用工作流。";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("可用工作流列表:\n");

        for (int i = 0; i < workflows.size(); i++) {
            WorkflowInfo wf = workflows.get(i);
            sb.append(String.format("%d. 名称: %s (ID: %s, 状态: %s)\n",
                    i + 1,
                    wf.getName(),
                    wf.getId(),
                    wf.isActive() ? "激活" : "停用"));
        }
        return sb.toString();
    }
}