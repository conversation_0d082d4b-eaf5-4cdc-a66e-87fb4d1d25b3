package com.kumhosunny.tools.service;

import java.util.Map;

/**
 * 工具服务接口
 * 
 * <AUTHOR>
 */
public interface ToolService {

    /**
     * 执行代码
     */
    String executeCode(String language, String code);

    /**
     * 搜索网络
     */
    String searchWeb(String query);

    /**
     * 调用N8N工作流
     */
    String callN8nWorkflow(String workflowId, Map<String, Object> params);

    /**
     * 获取工具列表
     */
    Map<String, Object> getToolList();

    /**
     * 调用工具
     */
    String callTool(String toolName, Map<String, Object> params);
}