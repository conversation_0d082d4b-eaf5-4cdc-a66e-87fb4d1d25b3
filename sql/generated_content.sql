-- ----------------------------
-- 1. AI 生成内容表
-- 存储用户通过AI生成的图片或视频等内容的核心信息
-- ----------------------------
CREATE TABLE `ai_generated` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `content_id` VARCHAR(64) NOT NULL COMMENT '内容的公开唯一ID，用于分享和API访问 (例如UUID)',
  `user_id` BIGINT NOT NULL COMMENT '创建者用户ID',
  `content_type` VARCHAR(16) NOT NULL COMMENT '内容类型: IMAGE, VIDEO',
  `generation_status` VARCHAR(32) NOT NULL DEFAULT 'COMPLETED' COMMENT '生成状态: PENDING, PROCESSING, COMPLETED, FAILED',
  `prompt` TEXT NOT NULL COMMENT '原始用户提示词',
  `revised_prompt` TEXT COMMENT '模型优化后的提示词 (例如DALL-E 3)',
  `model_used` VARCHAR(128) COMMENT '使用的模型ID, e.g., dall-e-3',
  `size` VARCHAR(32) COMMENT '内容尺寸，例如: 1024x1024',
  `style` VARCHAR(32) COMMENT '风格: vivid, natural',
  `quality` VARCHAR(32) COMMENT '质量: standard, hd',
  `oss_url` VARCHAR(512) NOT NULL COMMENT '内容在OSS中的存储URL',
  `thumbnail_url` VARCHAR(512) COMMENT '缩略图URL (主要用于视频)',
  `response_format` VARCHAR(16) COMMENT '原始响应格式: url, b64_json',
  `is_public` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否公开分享, true表示已分享',
  `is_featured` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否由管理员推荐',
  `view_count` BIGINT NOT NULL DEFAULT 0 COMMENT '查看次数 (冗余字段，提高查询性能)',
  `like_count` BIGINT NOT NULL DEFAULT 0 COMMENT '点赞次数 (冗余字段，提高查询性能)',
  `error_message` TEXT COMMENT '生成失败时的错误信息',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_content_id` (`content_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_content_type` (`content_type`),
  KEY `idx_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI生成内容（图片/视频）表';
