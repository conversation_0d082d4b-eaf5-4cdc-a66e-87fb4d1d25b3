-- KumhoSunny AI Chat 简化模型配置数据库表结构

-- 1. AI服务提供商表
CREATE TABLE `ai_providers` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `provider_code` VARCHAR(32) NOT NULL COMMENT '提供商编码，如：openai、anthropic、google',
    `provider_name` VARCHAR(64) NOT NULL COMMENT '提供商名称',
    `base_url` VARCHAR(256) COMMENT '基础URL',
    `api_key` VARCHAR(256) COMMENT 'API密钥',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_provider_code` (`provider_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI服务提供商配置表';

-- 2. AI模型定义表
CREATE TABLE `ai_models` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `model_code` VARCHAR(64) NOT NULL COMMENT '模型编码，如：gpt-3.5-turbo',
    `model_name` VARCHAR(128) NOT NULL COMMENT '模型显示名称',
    `model_type` VARCHAR(32) NOT NULL DEFAULT 'CHAT' COMMENT '模型类型：CHAT-对话模型，EMBEDDING-向量模型，IMAGE-图像模型，AUDIO-音频模型',
    `provider_id` BIGINT NOT NULL COMMENT '所属提供商ID',
    `context_window` INT COMMENT '上下文窗口大小',
    `max_output_tokens` INT COMMENT '最大输出token数',
    `supports_streaming` TINYINT NOT NULL DEFAULT 1 COMMENT '是否支持流式输出',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_model_code` (`model_code`),
    KEY `idx_provider_status` (`provider_id`, `status`),
    KEY `idx_model_type` (`model_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI模型定义表';

-- 3. 模型路由配置表
CREATE TABLE `ai_model_routes` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `request_model` VARCHAR(64) NOT NULL COMMENT '请求中的模型名',
    `target_model_id` BIGINT NOT NULL COMMENT '目标模型ID',
    `priority` INT NOT NULL DEFAULT 0 COMMENT '优先级（数值越大优先级越高）',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_request_model` (`request_model`),
    KEY `idx_status_priority` (`status`, `priority` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模型路由配置表';

-- 插入初始数据
INSERT INTO `ai_providers` (`provider_code`, `provider_name`, `base_url`, `api_key`) VALUES
('openai', 'OpenAI', 'https://api.openai.com/v1', 'your-openai-api-key'),
('anthropic', 'Anthropic', 'https://api.anthropic.com/v1', 'your-anthropic-api-key'),
('google', 'Google AI', 'https://generativelanguage.googleapis.com/v1', 'your-google-api-key'),
('ollama', 'Ollama', 'http://localhost:11434', NULL),
('deepseek', 'DeepSeek', 'https://api.deepseek.com/v1', 'your-deepseek-api-key');

INSERT INTO `ai_models` (`model_code`, `model_name`, `model_type`, `provider_id`, `context_window`, `max_output_tokens`) VALUES
-- OpenAI 对话模型
('gpt-3.5-turbo', 'GPT-3.5 Turbo', 'CHAT', 1, 16384, 4096),
('gpt-4', 'GPT-4', 'CHAT', 1, 8192, 4096),
('gpt-4-turbo', 'GPT-4 Turbo', 'CHAT', 1, 128000, 4096),
('gpt-4o', 'GPT-4o', 'CHAT', 1, 128000, 4096),
('gpt-4o-mini', 'GPT-4o Mini', 'CHAT', 1, 128000, 16384),

-- OpenAI 向量模型
('text-embedding-3-small', 'Text Embedding 3 Small', 'EMBEDDING', 1, NULL, NULL),
('text-embedding-3-large', 'Text Embedding 3 Large', 'EMBEDDING', 1, NULL, NULL),
('text-embedding-ada-002', 'Text Embedding Ada 002', 'EMBEDDING', 1, NULL, NULL),

-- OpenAI 图像模型
('dall-e-3', 'DALL-E 3', 'IMAGE', 1, NULL, NULL),
('dall-e-2', 'DALL-E 2', 'IMAGE', 1, NULL, NULL),

-- Anthropic 对话模型
('claude-3-sonnet', 'Claude 3 Sonnet', 'CHAT', 2, 200000, 4096),
('claude-3-opus', 'Claude 3 Opus', 'CHAT', 2, 200000, 4096),
('claude-3-haiku', 'Claude 3 Haiku', 'CHAT', 2, 200000, 4096),
('claude-3-5-sonnet', 'Claude 3.5 Sonnet', 'CHAT', 2, 200000, 8192),

-- Google 对话模型
('gemini-pro', 'Gemini Pro', 'CHAT', 3, 32768, 8192),
('gemini-1.5-pro', 'Gemini 1.5 Pro', 'CHAT', 3, 1048576, 8192),
('gemini-1.5-flash', 'Gemini 1.5 Flash', 'CHAT', 3, 1048576, 8192),

-- Google 向量模型
('text-embedding-004', 'Text Embedding 004', 'EMBEDDING', 3, NULL, NULL),

-- Ollama 本地模型
('llama3', 'Llama 3', 'CHAT', 4, 8192, 4096),
('llama3-70b', 'Llama 3 70B', 'CHAT', 4, 8192, 4096),
('qwen', 'Qwen', 'CHAT', 4, 32768, 2048),
('nomic-embed-text', 'Nomic Embed Text', 'EMBEDDING', 4, NULL, NULL),

-- DeepSeek 对话模型
('deepseek-chat', 'DeepSeek Chat', 'CHAT', 5, 32768, 4096),
('deepseek-coder', 'DeepSeek Coder', 'CHAT', 5, 16384, 4096),
('deepseek-v2', 'DeepSeek V2', 'CHAT', 5, 32768, 4096),
('deepseek-v2.5', 'DeepSeek V2.5', 'CHAT', 5, 32768, 8192);

-- 插入默认路由配置
INSERT INTO `ai_model_routes` (`request_model`, `target_model_id`, `priority`) VALUES
-- 对话模型路由配置
('gpt-3.5-turbo', 1, 100),
('gpt-4', 2, 100),
('gpt-4-turbo', 3, 100),
('gpt-4o', 4, 100),
('gpt-4o-mini', 5, 100),

-- 向量模型路由配置
('text-embedding-3-small', 6, 100),
('text-embedding-3-large', 7, 100),
('text-embedding-ada-002', 8, 100),

-- 图像模型路由配置
('dall-e-3', 9, 100),
('dall-e-2', 10, 100),

-- Anthropic 对话模型路由
('claude-3-sonnet', 11, 100),
('claude-3-opus', 12, 100),
('claude-3-haiku', 13, 100),
('claude-3-5-sonnet', 14, 100),

-- Google 模型路由
('gemini-pro', 15, 100),
('gemini-1.5-pro', 16, 100),
('gemini-1.5-flash', 17, 100),
('text-embedding-004', 18, 100),

-- Ollama 本地模型路由
('llama3', 19, 100),
('llama3-70b', 20, 100),
('qwen', 21, 100),
('nomic-embed-text', 22, 100),

-- DeepSeek 模型路由
('deepseek-chat', 23, 100),
('deepseek-coder', 24, 100),
('deepseek-v2', 25, 100),
('deepseek-v2.5', 26, 100);