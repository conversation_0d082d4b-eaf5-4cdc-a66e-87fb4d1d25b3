create table t_employee
(
    employee_id        bigint auto_increment comment '主键'
        primary key,
    union_id           varchar(255)                               null comment '钉钉唯一id',
    login_name         varchar(30) charset utf8mb3                not null comment '登录帐号',
    login_pwd          varchar(100) charset utf8mb3               null comment '登录密码',
    actual_name        varchar(30) charset utf8mb3                not null comment '员工名称',
    avatar             varchar(200)                               null,
    gender             tinyint(1)       default 0                 not null comment '性别',
    phone              varchar(15) charset utf8mb3                null comment '手机号码',
    department_id      bigint                                     not null comment '部门id',
    position_id        bigint                                     null comment '职务ID',
    email              varchar(100)                               null comment '邮箱',
    disabled_flag      tinyint unsigned default '0'               not null comment '是否被禁用 0否1是',
    deleted_flag       tinyint unsigned default '0'               not null comment '是否删除0否 1是',
    administrator_flag tinyint          default 0                 not null comment '是否为超级管理员: 0 不是，1是',
    remark             varchar(200)                               null comment '备注',
    update_time        datetime         default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    create_time        datetime         default CURRENT_TIMESTAMP not null comment '创建时间'
)
    comment '员工表' collate = utf8mb4_unicode_ci
                     row_format = DYNAMIC;




CREATE TABLE chat_sessions (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL,
  session_title VARCHAR(255),
  model_used VARCHAR(64),  -- 记录模型配置，如 openai:gpt-4
  status ENUM('active', 'archived') DEFAULT 'active',
  metadata JSON,           -- 会话配置（如启用插件、预览状态等）
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES t_employee(employee_id) ON DELETE CASCADE
);

CREATE TABLE chat_messages (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  session_id BIGINT NOT NULL,
  sender ENUM('user', 'assistant', 'agent') NOT NULL,
  content TEXT,
  content_type ENUM('text', 'code', 'file', 'agent_call') DEFAULT 'text',
  parent_message_id BIGINT NULL,     -- 支持多轮 threading
  token_count INT DEFAULT 0,         -- 可选：用于计费或窗口控制
  metadata JSON,                     -- 附件、Agent调用、代码执行、预览引用等
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE
);

CREATE INDEX idx_chat_sessions_user ON chat_sessions(user_id);
CREATE INDEX idx_chat_messages_session ON chat_messages(session_id);
CREATE INDEX idx_chat_messages_parent ON chat_messages(parent_message_id);


CREATE TABLE ai_apps (
                         id INT AUTO_INCREMENT PRIMARY KEY,
                         name VARCHAR(255) NOT NULL,
                         description TEXT,
                         category_id INT,
                         created_by INT,
                         is_public BOOLEAN DEFAULT FALSE,
                         created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                         FOREIGN KEY (category_id) REFERENCES ai_app_categories(id)
);

CREATE TABLE ai_app_settings (
                                 id INT AUTO_INCREMENT PRIMARY KEY,
                                 app_id INT NOT NULL,
                                 model VARCHAR(100),
                                 system_prompt TEXT,
                                 temperature FLOAT DEFAULT 0.7,
                                 FOREIGN KEY (app_id) REFERENCES ai_apps(id) ON DELETE CASCADE
);

CREATE TABLE ai_app_tools (
                              id INT AUTO_INCREMENT PRIMARY KEY,
                              app_id INT NOT NULL,
                              tool_name VARCHAR(100),
                              tool_type VARCHAR(50), -- e.g., 'api', 'function', 'n8n'
                              config JSON,
                              FOREIGN KEY (app_id) REFERENCES ai_apps(id) ON DELETE CASCADE
);

CREATE TABLE ai_app_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    sort_order INT DEFAULT 0, -- 用于排序
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Sample data for categories
INSERT INTO `ai_app_categories` (`id`, `name`, `description`, `sort_order`)
VALUES
    (1, '办公效率', '提升日常办公效率的工具', 10),
    (2, '创意设计', '辅助创意和设计工作的应用', 20),
    (3, '开发工具', '为开发者提供的提效工具', 30);

-- 1. 应用信息
INSERT INTO ai_apps (id, name, description, created_by, is_public, category_id)
VALUES (1, '文档翻译', '将任意文档翻译成目标语言，支持调用n8n工作流', 1, true, 1);

-- 2. 应用设置
INSERT INTO ai_app_settings (app_id, model, system_prompt, temperature)
VALUES (
           1,
           'deepseek-chat',
           '你是一个专业文档翻译助手，请将用户输入的文本翻译为目标语言，并保持语义准确与格式一致。',
           0.2
       );

-- 3. 工具配置（调用 n8n 的文档翻译工作流）
INSERT INTO ai_app_tools (app_id, tool_name, tool_type, config)
VALUES (
           1,
           'n8n_translate',
           'api',
           '{
             "n8n_id":"none",
             "input_key": "text",
             "output_key": "translated_text"
           }'
       );
