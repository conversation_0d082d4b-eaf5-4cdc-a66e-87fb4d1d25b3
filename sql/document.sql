create table documents
(
    id            bigint auto_increment comment '文档唯一ID'
        primary key,
    file_id       int                                        null comment '文件id',
    title         varchar(255)                               not null comment '文档标题',
    content       longtext                                   null comment '文档原始全文内容（可选保留）',
    content_hash  char(64)                                   null comment '文档内容的SHA-256哈希，用于去重',
    type          enum ('personal', 'department', 'company') not null comment '文档类型：个人/部门/公司',
    path          varchar(128)                               null,
    owner_id      bigint                                     not null comment '文档创建者（用于个人文档权限）',
    department_id bigint                                     null comment '所属部门ID（用于部门文档权限）',
    is_public     tinyint(1) default 0                       null comment '是否公司公开文档',
    created_at    datetime   default CURRENT_TIMESTAMP       null comment '创建时间',
    updated_at    datetime   default CURRENT_TIMESTAMP       null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint content_hash
        unique (content_hash)
)
    comment '文档元数据表';


CREATE TABLE document_chunks (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '文档片段唯一ID',
  document_id BIGINT NOT NULL COMMENT '关联的文档ID',
  chunk_index INT NOT NULL COMMENT '段落编号（从0开始）',
  content TEXT NOT NULL COMMENT '该段文本原文内容',
  vector_id VARCHAR(128) COMMENT '该段在向量库中的ID（如 Qdrant 向量ID）',
  embedding_status ENUM('pending', 'done', 'failed') DEFAULT 'pending' COMMENT '向量嵌入状态',
  embedding_updated_at DATETIME COMMENT '向量更新时间',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  UNIQUE KEY uniq_document_chunk (document_id, chunk_index)
) COMMENT='文档拆分片段（chunk）记录表';


CREATE TABLE document_permissions (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '权限记录ID',
  document_id BIGINT NOT NULL COMMENT '目标文档ID',
  user_id BIGINT COMMENT '具体授权的用户ID（可空）',
  department_id BIGINT COMMENT '具体授权的部门ID（可空）',
  permission ENUM('read', 'write', 'admin') NOT NULL COMMENT '权限类型：只读/编辑/管理员',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间'
) COMMENT='文档访问权限配置表';
