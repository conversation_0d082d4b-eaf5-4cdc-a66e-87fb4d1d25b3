package com.kumhosunny.chat.service.provider.impl;

import com.google.cloud.aiplatform.v1.ModelName;
import com.google.cloud.aiplatform.v1.PredictRequest; // <-- Add this import
import com.google.cloud.aiplatform.v1.PredictResponse;
import com.google.cloud.aiplatform.v1.PredictionServiceClient;
import com.google.cloud.aiplatform.v1.PredictionServiceSettings;
import com.google.protobuf.Value;
import com.google.protobuf.util.JsonFormat;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.oauth2.ServiceAccountCredentials;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class GeminiProExplicitAuthCall {

    public static void main(String[] args) {
        String projectId = "just-duality-464717-v2"; // Replace with your project ID
        String location = "global"; // Use the global endpoint
        String modelId = "gemini-2.5-pro"; // Gemini 2.5 Pro model ID

        String serviceAccountKeyPath = "/Users/<USER>/Desktop/just-duality-464717-v2-51f7ac6d867c.json"; // Replace with your actual path

        GoogleCredentials credentials;
        try (FileInputStream serviceAccountStream = new FileInputStream(serviceAccountKeyPath)) {
            credentials = ServiceAccountCredentials.fromStream(serviceAccountStream);
        } catch (IOException e) {
            System.err.println("Error: Failed to load service account credentials. Check key file path and existence.");
            System.err.println("Error message: " + e.getMessage());
            e.printStackTrace();
            return;
        }

        try {
            ModelName modelResourceName = ModelName.newBuilder()
                    .setProject(projectId)
                    .setLocation(location)
//                    .setPublisher("google")
                    .setModel(modelId)
                    .build();

            PredictionServiceSettings predictionServiceSettings;
            if ("global".equals(location)) {
                predictionServiceSettings = PredictionServiceSettings.newBuilder()
                        .setEndpoint("aiplatform.googleapis.com:443")
                        .setCredentialsProvider(() -> credentials)
                        .build();
            } else {
                predictionServiceSettings = PredictionServiceSettings.newBuilder()
                        .setEndpoint(location + "-aiplatform.googleapis.com:443")
                        .setCredentialsProvider(() -> credentials)
                        .build();
            }

            try (PredictionServiceClient client = PredictionServiceClient.create(predictionServiceSettings)) {

                String requestJson = "{\n" +
                        "\t\\\"contents\\\": [{\n" +
                        "\t\t\\\"role\\\": \\\"user\\\",\n" +
                        "\t\t\\\"parts\\\": [{\n" +
                        "\t\t\t\\\"text\\\": \\\"Hello, Gemini!\\\"\n" +
                        "\t\t}]\n" +
                        "\t}]\n" +
                        "}";

                Value.Builder valueBuilder = Value.newBuilder();
                JsonFormat.parser().merge(requestJson, valueBuilder);
                Value instance = valueBuilder.build();

                List<Value> instances = new ArrayList<>();
                instances.add(instance);

                // --- IMPORTANT CHANGE STARTS HERE ---

                // 1. Build the PredictRequest object
                // The `predict` method of PredictionServiceClient expects a PredictRequest object
                // for public models (like Gemini) when you specify the model directly in the request.
                PredictRequest predictRequest = PredictRequest.newBuilder()
                        .setEndpoint(modelResourceName.toString()) // Set the resource name string as the endpoint
                        .addAllInstances(instances)                  // Add your content instances
//                        .setParameters(Value.newBuilder().build())   // No additional parameters needed here as they are in the instance
                        .build();

                // 2. Call the predict method with the PredictRequest object
                PredictResponse response = client.predict(predictRequest);

                // --- IMPORTANT CHANGE ENDS HERE ---

                System.out.println("Predicted content:");
                for (Value prediction : response.getPredictionsList()) {
                    System.out.println(prediction.toString());

                    if (prediction.getStructValue().containsFields("candidates")) {
                        Value candidates = prediction.getStructValue().getFieldsMap().get("candidates");
                        for (Value candidate : candidates.getListValue().getValuesList()) {
                            if (candidate.getStructValue().containsFields("content")) {
                                Value content = candidate.getStructValue().getFieldsMap().get("content");
                                if (content.getStructValue().containsFields("parts")) {
                                    Value parts = content.getStructValue().getFieldsMap().get("parts");
                                    for (Value part : parts.getListValue().getValuesList()) {
                                        if (part.getStructValue().containsFields("text")) {
                                            System.out.println("  " + part.getStructValue().getFieldsMap().get("text").getStringValue());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

            }
        } catch (IOException e) {
            System.err.println("Error: IO error occurred during client creation or prediction.");
            System.err.println("Error message: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("An unexpected error occurred.");
            System.err.println("Error message: " + e.getMessage());
            e.printStackTrace();
        }
    }
}