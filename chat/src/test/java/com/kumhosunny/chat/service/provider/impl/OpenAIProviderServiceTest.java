//package com.kumhosunny.chat.service.provider.impl;
//
//import com.kumhosunny.chat.dto.ChatCompletionRequest;
//import com.kumhosunny.chat.dto.ChatCompletionResponse;
//import com.kumhosunny.chat.dto.ChatMessage;
//import io.qdrant.client.QdrantClient;
//import io.qdrant.client.grpc.Points;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.DisplayName;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.lang.reflect.Field;
//import java.util.Arrays;
//import java.util.List;
//import java.util.Map;
//import java.util.concurrent.ExecutionException;
//
//import static io.qdrant.client.PointIdFactory.id;
//import static io.qdrant.client.ValueFactory.value;
//import static io.qdrant.client.VectorsFactory.vectors;
//import static org.junit.jupiter.api.Assertions.*;
//
///**
// * OpenAI 提供商服务测试
// * 测试OpenAI服务的基本功能
// */
//class OpenAIProviderServiceTest {
//
//    private OpenAIProviderService openAIProviderService;
//
//    @Autowired
//    private QdrantClient client;
//
//    @BeforeEach
//    void setUp() {
//        openAIProviderService = new OpenAIProviderService();
//    }
//
//    /**
//     * 设置私有字段值的工具方法
//     */
//    private void setField(Object target, String fieldName, Object value) {
//        try {
//            Field field = target.getClass().getDeclaredField(fieldName);
//            field.setAccessible(true);
//            field.set(target, value);
//        } catch (Exception e) {
//            throw new RuntimeException("Failed to set field: " + fieldName, e);
//        }
//    }
//
//    @Test
//    @DisplayName("测试提供商名称")
//    void testGetProviderName() {
//        assertEquals("OpenAI", openAIProviderService.getProviderName());
//    }
//
//    @Test
//    @DisplayName("测试服务可用性检查 - API Key已配置")
//    void testIsAvailable_WithApiKey() {
//        // API Key已配置时应该可用
//        setField(openAIProviderService, "apiKey", "test-api-key");
//        assertTrue(openAIProviderService.isAvailable());
//    }
//
//    @Test
//    @DisplayName("测试服务可用性检查 - API Key未配置")
//    void testIsAvailable_WithoutApiKey() {
//        // API Key未配置时应该不可用
//        setField(openAIProviderService, "apiKey", "");
//        assertFalse(openAIProviderService.isAvailable());
//
//        // API Key为null时应该不可用
//        setField(openAIProviderService, "apiKey", null);
//        assertFalse(openAIProviderService.isAvailable());
//
//        // API Key为空白字符串时应该不可用
//        setField(openAIProviderService, "apiKey", "   ");
//        assertFalse(openAIProviderService.isAvailable());
//    }
//
//    @Test
//    @DisplayName("测试聊天完成API - API Key未配置时的错误处理")
//    void testChatCompletion_NoApiKey() {
//        // 设置空API Key
//        setField(openAIProviderService, "apiKey", "");
//
//        ChatCompletionRequest request = new ChatCompletionRequest();
//        request.setModel("gpt-3.5-turbo");
//        request.setMessages(Arrays.asList(
//                new ChatMessage("user", "Hello!")));
//
//        // 执行测试并验证抛出异常
//        assertThrows(IllegalStateException.class, () -> {
//            openAIProviderService.chatCompletion(request).block();
//        });
//    }
//
//    @Test
//    @DisplayName("测试聊天完成API请求参数验证")
//    void testChatCompletion_RequestValidation() {
//        // 设置有效的API Key
//        setField(openAIProviderService, "apiKey", "test-api-key");
//
//        // 测试有效请求的构建
//        ChatCompletionRequest request = new ChatCompletionRequest();
//        request.setModel("gpt-3.5-turbo");
//        request.setMessages(Arrays.asList(
//                new ChatMessage("user", "你好，请介绍一下你自己"),
//                new ChatMessage("assistant", "你好！我是一个AI助手。"),
//                new ChatMessage("user", "你能做什么？")));
//        request.setMaxTokens(1000);
//        request.setTemperature(0.7);
//        request.setTopP(0.9);
//        request.setFrequencyPenalty(0.1);
//        request.setPresencePenalty(0.1);
//        request.setStop(Arrays.asList("\n", "END"));
//        request.setStream(false);
//        request.setUser("test-user-123");
//
//        // 验证请求对象构建正确
//        assertNotNull(request);
//        assertEquals("gpt-3.5-turbo", request.getModel());
//        assertEquals(3, request.getMessages().size());
//        assertEquals(1000, request.getMaxTokens());
//        assertEquals(0.7, request.getTemperature());
//        assertEquals(0.9, request.getTopP());
//        assertEquals(0.1, request.getFrequencyPenalty());
//        assertEquals(0.1, request.getPresencePenalty());
//        assertEquals(2, request.getStop().size());
//        assertFalse(request.getStream());
//        assertEquals("test-user-123", request.getUser());
//
//        // 验证消息内容
//        ChatMessage firstMessage = request.getMessages().get(0);
//        assertEquals("user", firstMessage.getRole());
//        assertEquals("你好，请介绍一下你自己", firstMessage.getContent());
//
//        ChatMessage secondMessage = request.getMessages().get(1);
//        assertEquals("assistant", secondMessage.getRole());
//        assertEquals("你好！我是一个AI助手。", secondMessage.getContent());
//
//        ChatMessage thirdMessage = request.getMessages().get(2);
//        assertEquals("user", thirdMessage.getRole());
//        assertEquals("你能做什么？", thirdMessage.getContent());
//    }
//
//    @Test
//    @DisplayName("测试聊天响应对象构建")
//    void testChatCompletionResponse() {
//        // 构建测试响应对象
//        ChatCompletionResponse response = new ChatCompletionResponse();
//        response.setId("chatcmpl-test-123");
//        response.setModel("gpt-3.5-turbo");
//        response.setCreated(System.currentTimeMillis() / 1000);
//
//        // 构建选择项
//        ChatMessage assistantMessage = new ChatMessage("assistant",
//                "你好！我是ChatGPT，一个由OpenAI开发的大型语言模型。我可以帮助你回答问题、提供信息、协助写作、解决问题等。有什么我可以帮助你的吗？");
//        ChatCompletionResponse.Choice choice = new ChatCompletionResponse.Choice(0, assistantMessage, "stop");
//        response.setChoices(Arrays.asList(choice));
//
//        // 构建使用情况
//        ChatCompletionResponse.Usage usage = new ChatCompletionResponse.Usage(25, 55, 80);
//        response.setUsage(usage);
//
//        // 验证响应对象
//        assertNotNull(response);
//        assertEquals("chatcmpl-test-123", response.getId());
//        assertEquals("gpt-3.5-turbo", response.getModel());
//        assertEquals("chat.completion", response.getObject());
//        assertNotNull(response.getChoices());
//        assertEquals(1, response.getChoices().size());
//
//        // 验证选择项
//        ChatCompletionResponse.Choice testChoice = response.getChoices().get(0);
//        assertEquals(0, testChoice.getIndex());
//        assertEquals("assistant", testChoice.getMessage().getRole());
//        assertTrue(testChoice.getMessage().getContent().contains("ChatGPT"));
//        assertEquals("stop", testChoice.getFinishReason());
//
//        // 验证使用情况
//        assertNotNull(response.getUsage());
//        assertEquals(25, response.getUsage().getPromptTokens());
//        assertEquals(55, response.getUsage().getCompletionTokens());
//        assertEquals(80, response.getUsage().getTotalTokens());
//    }
//
//    @Test
//    @DisplayName("测试聊天消息构建")
//    void testChatMessage() {
//        // 测试基本构造函数
//        ChatMessage userMessage = new ChatMessage("user", "Hello, how are you?");
//        assertEquals("user", userMessage.getRole());
//        assertEquals("Hello, how are you?", userMessage.getContent());
//        assertNull(userMessage.getName());
//
//        // 测试带名称的构造函数
//        ChatMessage namedMessage = new ChatMessage("user", "Hello", "Alice");
//        assertEquals("user", namedMessage.getRole());
//        assertEquals("Hello", namedMessage.getContent());
//        assertEquals("Alice", namedMessage.getName());
//
//        // 测试系统消息
//        ChatMessage systemMessage = new ChatMessage("system", "You are a helpful assistant.");
//        assertEquals("system", systemMessage.getRole());
//        assertEquals("You are a helpful assistant.", systemMessage.getContent());
//
//        // 测试助手消息
//        ChatMessage assistantMessage = new ChatMessage("assistant", "I'm doing well, thank you!");
//        assertEquals("assistant", assistantMessage.getRole());
//        assertEquals("I'm doing well, thank you!", assistantMessage.getContent());
//    }
//
//    @Test
//    @DisplayName("测试OpenAI配置验证")
//    void testConfiguration() {
//        // 测试默认配置
//        OpenAIProviderService defaultService = new OpenAIProviderService();
//        assertEquals("OpenAI", defaultService.getProviderName());
//
//        // 测试API Key配置后的可用性
//        setField(defaultService, "apiKey", "sk-test123456789");
//        assertTrue(defaultService.isAvailable());
//
//        // 测试Base URL配置
//        setField(defaultService, "baseUrl", "https://api.openai.com/v1");
//        // Base URL配置不影响可用性，主要取决于API Key
//        assertTrue(defaultService.isAvailable());
//    }
//
//    @Test
//    @DisplayName("测试错误场景处理")
//    void testErrorHandling() {
//        // 测试null API Key
//        setField(openAIProviderService, "apiKey", null);
//        assertFalse(openAIProviderService.isAvailable());
//
//        // 测试空白API Key
//        setField(openAIProviderService, "apiKey", "  ");
//        assertFalse(openAIProviderService.isAvailable());
//
//        // 测试空字符串API Key
//        setField(openAIProviderService, "apiKey", "");
//        assertFalse(openAIProviderService.isAvailable());
//
//        // 验证在API Key未配置时调用聊天API会抛出异常
//        ChatCompletionRequest request = new ChatCompletionRequest();
//        request.setModel("gpt-3.5-turbo");
//        request.setMessages(Arrays.asList(new ChatMessage("user", "test")));
//
//        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> {
//            openAIProviderService.chatCompletion(request).block();
//        });
//
//        assertTrue(exception.getMessage().contains("OpenAI API key not configured"));
//    }
//
//
//}