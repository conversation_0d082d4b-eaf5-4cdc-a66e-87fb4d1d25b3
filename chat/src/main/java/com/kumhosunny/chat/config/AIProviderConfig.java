package com.kumhosunny.chat.config;

import com.kumhosunny.chat.service.provider.AIProviderService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI 提供商配置类
 * 用于管理和注册不同的 AI 服务提供商
 */
@Configuration
public class AIProviderConfig {

    /**
     * 创建提供商服务映射
     * 将所有 AIProviderService 实现类注册到一个 Map 中
     */
    @Bean
    public Map<String, AIProviderService> providerServices(List<AIProviderService> services) {
        Map<String, AIProviderService> serviceMap = new HashMap<>();

        for (AIProviderService service : services) {
            // 使用服务的Bean名称作为key，如果没有则使用类名
            String serviceName = getServiceName(service);
            serviceMap.put(serviceName, service);
        }

        return serviceMap;
    }

    /**
     * 获取服务名称
     * 优先使用 @Service 注解中的 value，否则使用类名
     */
    private String getServiceName(AIProviderService service) {
        // 检查类上的 @Service 注解
        org.springframework.stereotype.Service serviceAnnotation = service.getClass()
                .getAnnotation(org.springframework.stereotype.Service.class);

        if (serviceAnnotation != null && !serviceAnnotation.value().isEmpty()) {
            return serviceAnnotation.value();
        }

        // 使用类名作为默认值（转换为小写）
        String className = service.getClass().getSimpleName();
        if (className.endsWith("ProviderService")) {
            className = className.substring(0, className.length() - "ProviderService".length());
        }
        return className.toLowerCase();
    }
}