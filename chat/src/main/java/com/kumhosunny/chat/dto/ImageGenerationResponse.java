package com.kumhosunny.chat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 图像生成响应 DTO
 * 兼容 OpenAI Images API 格式
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImageGenerationResponse {

    /**
     * 生成时间戳
     */
    private Long created;

    /**
     * 图像数据列表
     */
    private List<ImageData> data;

    /**
     * 内部类：图像数据
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImageData {

        /**
         * 图像URL（当 response_format 为 "url" 时）
         */
        private String url;

        /**
         * Base64编码的图像数据（当 response_format 为 "b64_json" 时）
         */
        @JsonProperty("b64_json")
        private String b64Json;

        /**
         * 修改后的提示词（DALL·E 3 会自动优化提示词）
         */
        @JsonProperty("revised_prompt")
        private String revisedPrompt;

        /**
         * 便捷构造函数 - 仅包含URL
         */
        public ImageData(String url) {
            this.url = url;
        }

        /**
         * 便捷构造函数 - 包含URL和修改后的提示词
         */
        public ImageData(String url, String revisedPrompt) {
            this.url = url;
            this.revisedPrompt = revisedPrompt;
        }
    }
}