package com.kumhosunny.chat.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 模型列表响应 DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ModelsResponse {

    private String object = "list";
    private List<ModelInfo> data;

    // 内部类：模型信息
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ModelInfo {
        private String id;
        private String object = "model";
        private Long created;
        private String ownedBy;

        public ModelInfo(String id, String ownedBy) {
            this.id = id;
            this.ownedBy = ownedBy;
            this.created = System.currentTimeMillis() / 1000;
        }
    }

    public ModelsResponse(List<ModelInfo> data) {
        this.data = data;
    }
}