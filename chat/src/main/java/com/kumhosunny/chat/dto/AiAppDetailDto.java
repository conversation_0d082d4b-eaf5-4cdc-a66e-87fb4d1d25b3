package com.kumhosunny.chat.dto;

import com.kumhosunny.common.entity.AiApp;
import com.kumhosunny.common.entity.AiAppSettings;
import com.kumhosunny.common.entity.AiAppTools;
import lombok.Data;

import java.util.List;

@Data
public class AiAppDetailDto {

    private Integer id;
    private String name;
    private String description;
    private String avatar;
    private Integer categoryId;
    private AiAppSettings settings;
    private List<AiAppTools> tools;

    public static AiAppDetailDto fromEntity(AiApp app, AiAppSettings settings, List<AiAppTools> tools) {
        if (app == null) {
            return null;
        }
        AiAppDetailDto dto = new AiAppDetailDto();
        dto.setId(app.getId());
        dto.setName(app.getName());
        dto.setDescription(app.getDescription());
        dto.setAvatar(app.getAvatar());
        dto.setCategoryId(app.getCategoryId());
        dto.setSettings(settings);
        dto.setTools(tools);
        return dto;
    }
}