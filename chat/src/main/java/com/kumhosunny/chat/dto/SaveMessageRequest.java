package com.kumhosunny.chat.dto;

import com.kumhosunny.common.entity.ChatMessage;
import lombok.Data;

/**
 * 保存聊天消息请求DTO
 */
@Data
public class SaveMessageRequest {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 消息发送者
     */
    private ChatMessage.MessageSender sender;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 内容类型
     */
    private ChatMessage.ContentType contentType = ChatMessage.ContentType.text;

    /**
     * 父消息ID（支持多轮threading）
     */
    private Long parentMessageId;

    /**
     * Token数量
     */
    private Integer tokenCount = 0;

    /**
     * 元数据，存储附件、Agent调用、代码执行、预览引用等
     * 示例格式：
     * {
     * "attachments": [{"type": "image", "url": "image.jpg"}],
     * "agent_call": {"name": "search", "parameters": {"query": "test"}},
     * "code_execution": {"language": "python", "output": "result"}
     * }
     */
    private String metadata;

}