package com.kumhosunny.chat.dto;

import com.kumhosunny.common.entity.AiAppCategory;
import lombok.Data;

import java.util.List;

@Data
public class AiAppCategoryWithAppsDto {
    private Integer id;
    private String name;
    private String description;
    private Integer sortOrder;
    private List<AiAppDto> apps;

    public static AiAppCategoryWithAppsDto fromEntity(AiAppCategory category, List<AiAppDto> apps) {
        if (category == null) {
            return null;
        }
        AiAppCategoryWithAppsDto dto = new AiAppCategoryWithAppsDto();
        dto.setId(category.getId());
        dto.setName(category.getName());
        dto.setDescription(category.getDescription());
        dto.setSortOrder(category.getSortOrder());
        dto.setApps(apps);
        return dto;
    }
}