package com.kumhosunny.chat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 聊天完成流式响应块 DTO
 * 用于处理 Server-Sent Events 格式的流式响应
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatCompletionChunk {

    private String id;
    private String object = "chat.completion.chunk";
    private Long created;
    private String model;
    private List<ChunkChoice> choices;
    private Usage usage;

    @JsonProperty("system_fingerprint")
    private String systemFingerprint;

    // 内部类：流式选择项
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChunkChoice {
        private Integer index;
        private Delta delta;
        private String logprobs;
        @JsonProperty("finish_reason")
        private String finishReason;
    }

    // 内部类：增量内容
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Delta {
        private String role;
        private String content;
        private String name;
        @JsonProperty("tool_calls")
        private List<Object> toolCalls;
    }

    // 内部类：使用情况
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Usage {
        @JsonProperty("prompt_tokens")
        private Integer promptTokens;
        @JsonProperty("completion_tokens")
        private Integer completionTokens;
        @JsonProperty("total_tokens")
        private Integer totalTokens;
        @JsonProperty("prompt_tokens_details")
        private Object promptTokensDetails;
        @JsonProperty("prompt_cache_hit_tokens")
        private Integer promptCacheHitTokens;
        @JsonProperty("prompt_cache_miss_tokens")
        private Integer promptCacheMissTokens;
    }
}