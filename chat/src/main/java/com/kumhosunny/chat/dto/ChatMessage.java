package com.kumhosunny.chat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 聊天消息 DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatMessage {

    /**
     * 消息角色: system, user, assistant, tool
     */
    private String role;

    /**
     * 消息内容 - 支持多模态格式
     * 可以是 String（纯文本）或 List<Object>（多模态内容）
     */
    private Object content;

    /**
     * 消息名称 (可选)
     */
    private String name;

    /**
     * 工具调用 (仅限assistant消息)
     */
    @JsonProperty("tool_calls")
    private List<Map<String, Object>> toolCalls;

    /**
     * 工具调用ID (仅限tool消息)
     */
    @JsonProperty("tool_call_id")
    private String toolCallId;

    // 便捷构造函数
    public ChatMessage(String role, Object content) {
        this.role = role;
        this.content = content;
    }

    public ChatMessage(String role, Object content, String name) {
        this.role = role;
        this.content = content;
        this.name = name;
    }

    // 为了向后兼容，保留字符串构造函数
    public ChatMessage(String role, String content) {
        this.role = role;
        this.content = content;
    }
}