package com.kumhosunny.chat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 聊天完成请求 DTO
 * 兼容 OpenAI Chat Completions API 格式
 */
@Data
public class ChatCompletionRequest {

    /**
     * 要使用的模型ID
     * 例如: "gpt-3.5-turbo", "claude-3-sonnet", "gemini-pro", "local-llama3"
     */
    private String model;

    /**
     * 消息列表
     */
    private List<ChatMessage> messages;

    /**
     * 生成的最大token数
     */
    @JsonProperty("max_tokens")
    private Integer maxTokens;

    /**
     * 温度参数 (0-2)
     */
    private Double temperature;

    /**
     * 核采样参数 (0-1)
     */
    @JsonProperty("top_p")
    private Double topP;

    /**
     * 频率惩罚 (-2.0 to 2.0)
     */
    @JsonProperty("frequency_penalty")
    private Double frequencyPenalty;

    /**
     * 存在惩罚 (-2.0 to 2.0)
     */
    @JsonProperty("presence_penalty")
    private Double presencePenalty;

    /**
     * 停止序列
     */
    private List<String> stop;

    /**
     * 是否流式响应
     */
    private Boolean stream = false;

    /**
     * 用户标识
     */
    private String user;

    /**
     * 工具函数定义
     */
    private List<Map<String, Object>> tools;

    /**
     * 工具选择策略
     */
    @JsonProperty("tool_choice")
    private Object toolChoice;

    /**
     * 响应格式
     */
    @JsonProperty("response_format")
    private Map<String, Object> responseFormat;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 知识库是否开启
     */
    private Boolean knowledgeEnabled;

}