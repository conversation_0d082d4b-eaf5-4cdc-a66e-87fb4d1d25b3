package com.kumhosunny.chat.dto;

import com.kumhosunny.common.entity.AiModelRoute;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 模型路由完整信息响应 DTO
 * 包含ai_model_routes表的所有字段以及关联的模型和提供商信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ModelRouteResponse {

    private String object = "list";
    private List<ModelRouteInfo> data;

    /**
     * 内部类：模型路由信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ModelRouteInfo {
        // ai_model_routes 表字段
        private Long id;
        private String requestModel;
        private Long targetModelId;
        private Integer priority;
        private Integer status;
        private LocalDateTime createdTime;
        private LocalDateTime updatedTime;

        // 关联的目标模型信息
        private TargetModelInfo targetModel;

        /**
         * 从实体转换为DTO
         */
        public static ModelRouteInfo fromEntity(AiModelRoute route) {
            if (route == null) {
                return null;
            }

            ModelRouteInfo info = new ModelRouteInfo();
            info.id = route.getId();
            info.requestModel = route.getRequestModel();
            info.targetModelId = route.getTargetModelId();
            info.priority = route.getPriority();
            info.status = route.getStatus();
            info.createdTime = route.getCreatedTime();
            info.updatedTime = route.getUpdatedTime();

            // 转换关联的目标模型信息
            if (route.getTargetModel() != null) {
                info.targetModel = TargetModelInfo.fromEntity(route.getTargetModel());
            }

            return info;
        }
    }

    /**
     * 内部类：目标模型信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TargetModelInfo {
        // ai_models 表字段
        private Long id;
        private String modelCode;
        private String modelName;
        private String modelType;
        private Long providerId;
        private Integer contextWindow;
        private Integer maxOutputTokens;
        private Boolean supportsStreaming;
        private Integer status;
        private LocalDateTime createdTime;
        private LocalDateTime updatedTime;

        // 关联的提供商信息
        private ProviderInfo provider;

        /**
         * 从实体转换为DTO
         */
        public static TargetModelInfo fromEntity(com.kumhosunny.common.entity.AiModel model) {
            if (model == null) {
                return null;
            }

            TargetModelInfo info = new TargetModelInfo();
            info.id = model.getId();
            info.modelCode = model.getModelCode();
            info.modelName = model.getModelName();
            info.modelType = model.getModelType() != null ? model.getModelType().name() : null;
            info.providerId = model.getProviderId();
            info.contextWindow = model.getContextWindow();
            info.maxOutputTokens = model.getMaxOutputTokens();
            info.supportsStreaming = model.getSupportsStreaming();
            info.status = model.getStatus();
            info.createdTime = model.getCreatedTime();
            info.updatedTime = model.getUpdatedTime();

            // 转换关联的提供商信息
            if (model.getProvider() != null) {
                info.provider = ProviderInfo.fromEntity(model.getProvider());
            }

            return info;
        }
    }

    /**
     * 内部类：提供商信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProviderInfo {
        // ai_providers 表字段
        private Long id;
        private String providerCode;
        private String providerName;
        private String baseUrl;
        private Integer status;
        private LocalDateTime createdTime;
        private LocalDateTime updatedTime;
        // 注意：不包含 apiKey 以保护敏感信息

        /**
         * 从实体转换为DTO
         */
        public static ProviderInfo fromEntity(com.kumhosunny.common.entity.AiProvider provider) {
            if (provider == null) {
                return null;
            }

            ProviderInfo info = new ProviderInfo();
            info.id = provider.getId();
            info.providerCode = provider.getProviderCode();
            info.providerName = provider.getProviderName();
            info.baseUrl = provider.getBaseUrl();
            info.status = provider.getStatus();
            info.createdTime = provider.getCreatedTime();
            info.updatedTime = provider.getUpdatedTime();
            // 不设置 apiKey 以保护敏感信息

            return info;
        }
    }

    public ModelRouteResponse(List<ModelRouteInfo> data) {
        this.data = data;
    }
}