package com.kumhosunny.chat.dto;

import lombok.Data;

/**
 * 创建聊天会话请求DTO
 */
@Data
public class CreateSessionRequest {

    /**
     * 会话标题
     */
    private String sessionTitle;

    /**
     * 使用的模型
     */
    private String modelUsed;

    /**
     * 元数据，存储会话配置信息（如附件、需要调用的Agent等）
     * 示例格式：
     * {
     * "attachments": [{"type": "file", "name": "document.pdf", "id": "file123"}],
     * "agents": [{"name": "knowledge_search", "enabled": true}],
     * "settings": {"temperature": 0.7, "maxTokens": 4000}
     * }
     */
    private String metadata;

}