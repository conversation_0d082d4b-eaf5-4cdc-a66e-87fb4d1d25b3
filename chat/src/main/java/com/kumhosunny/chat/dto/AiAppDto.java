package com.kumhosunny.chat.dto;

import com.kumhosunny.common.entity.AiApp;
import lombok.Data;

@Data
public class AiAppDto {
    private Integer id;
    private String name;
    private String description;
    private String avatar;
    private Integer categoryId;
    private String categoryName;

    public static AiAppDto fromEntity(AiApp app) {
        if (app == null) {
            return null;
        }
        AiAppDto dto = new AiAppDto();
        dto.setId(app.getId());
        dto.setName(app.getName());
        dto.setDescription(app.getDescription());
        dto.setAvatar(app.getAvatar());
        dto.setCategoryId(app.getCategoryId());
        // 不访问懒加载的 category 属性，避免 LazyInitializationException
        return dto;
    }

    public static AiAppDto fromEntityWithCategory(AiApp app) {
        if (app == null) {
            return null;
        }
        AiAppDto dto = new AiAppDto();
        dto.setId(app.getId());
        dto.setName(app.getName());
        dto.setDescription(app.getDescription());
        dto.setAvatar(app.getAvatar());
        dto.setCategoryId(app.getCategoryId());
        // 如果 category 已经被预加载，则可以安全访问
        if (app.getCategory() != null) {
            dto.setCategoryName(app.getCategory().getName());
        }
        return dto;
    }
}