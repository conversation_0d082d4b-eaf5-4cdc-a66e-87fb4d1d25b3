package com.kumhosunny.chat.dto.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;

import java.io.IOException;

/**
 * 将任意 JSON 值（字符串、null、对象、数组）序列化为其文本表示，
 * 以兼容 OpenAI 最新规范中的多模态 content 数组格式。
 */
public class ContentAsTextDeserializer extends JsonDeserializer<String> {
    @Override
    public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonNode node = p.getCodec().readTree(p);
        if (node == null || node.isNull()) {
            return null;
        }
        if (node.isTextual()) {
            return node.asText();
        }
        // 对于对象、数组等复杂结构，直接返回其 JSON 字符串表示
        return node.toString();
    }
}