package com.kumhosunny.chat.controller;

import com.kumhosunny.common.entity.ChatSession;
import com.kumhosunny.common.entity.ChatMessage;
import com.kumhosunny.common.result.ApiResponse;
import com.kumhosunny.common.util.UserContextUtil;
import com.kumhosunny.chat.dto.CreateSessionRequest;
import com.kumhosunny.chat.dto.SaveMessageRequest;
import com.kumhosunny.chat.dto.UpdateSessionRequest;
import com.kumhosunny.chat.service.ChatSessionService;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 聊天会话控制器
 */
@RestController
@RequestMapping("/api/chat")
public class ChatSessionController {

    private final ChatSessionService chatSessionService;
    private final UserContextUtil userContextUtil;

    public ChatSessionController(ChatSessionService chatSessionService, UserContextUtil userContextUtil) {
        this.chatSessionService = chatSessionService;
        this.userContextUtil = userContextUtil;
    }

    /**
     * 新建会话
     */
    @PostMapping("/sessions")
    public ApiResponse<ChatSession> createSession(@RequestBody CreateSessionRequest request,
            HttpServletRequest httpRequest) {
        Long userId = userContextUtil.getCurrentUserId(httpRequest);
        ChatSession session = chatSessionService.createSession(request, userId);
        return ApiResponse.success(session);
    }

    /**
     * 查询会话数据，带出对话记录
     */
    @GetMapping("/sessions/{sessionId}")
    public ApiResponse<ChatSession> getSessionWithMessages(@PathVariable String sessionId, HttpServletRequest request) {
        Long userId = userContextUtil.getCurrentUserId(request);
        ChatSession session = chatSessionService.getSessionWithMessages(sessionId, userId);
        if (session == null) {
            return ApiResponse.error("会话不存在或无权限访问");
        }
        return ApiResponse.success(session);
    }

    /**
     * 对话信息存储
     */
    @PostMapping("/messages")
    public ApiResponse<ChatMessage> saveMessage(@RequestBody SaveMessageRequest request,
            HttpServletRequest httpRequest) {
        Long userId = userContextUtil.getCurrentUserId(httpRequest);
        ChatMessage message = chatSessionService.saveMessage(request, userId);
        return ApiResponse.success(message);
    }

    /**
     * 查询会话历史
     */
    @GetMapping("/sessions")
    public ApiResponse<List<ChatSession>> getSessionHistory(HttpServletRequest request) {
        Long userId = userContextUtil.getCurrentUserId(request);
        List<ChatSession> sessions = chatSessionService.getSessionHistory(userId);
        return ApiResponse.success(sessions);
    }

    /**
     * 删除会话
     */
    @DeleteMapping("/sessions/{sessionId}")
    public ApiResponse<Boolean> deleteSession(@PathVariable String sessionId, HttpServletRequest request) {
        Long userId = userContextUtil.getCurrentUserId(request);
        boolean success = chatSessionService.deleteSession(sessionId, userId);
        if (success) {
            return ApiResponse.success(true);
        } else {
            return ApiResponse.error("会话不存在或无权限删除");
        }
    }

    /**
     * 获取用户的活跃会话列表
     */
    @GetMapping("/sessions/active")
    public ApiResponse<List<ChatSession>> getActiveSessions(HttpServletRequest request) {
        Long userId = userContextUtil.getCurrentUserId(request);
        List<ChatSession> sessions = chatSessionService.getActiveSessions(userId);
        return ApiResponse.success(sessions);
    }

    /**
     * 获取会话的消息列表
     */
    @GetMapping("/sessions/{sessionId}/messages")
    public ApiResponse<List<ChatMessage>> getSessionMessages(@PathVariable String sessionId,
            HttpServletRequest request) {
        Long userId = userContextUtil.getCurrentUserId(request);
        List<ChatMessage> messages = chatSessionService.getSessionMessages(sessionId, userId);
        return ApiResponse.success(messages);
    }

    /**
     * 更新会话标题
     */
    @PutMapping("/sessions/{sessionId}/title")
    public ApiResponse<ChatSession> updateSessionTitle(@PathVariable String sessionId, @RequestParam String title,
            HttpServletRequest request) {
        Long userId = userContextUtil.getCurrentUserId(request);
        ChatSession session = chatSessionService.updateSessionTitle(sessionId, userId, title);
        return ApiResponse.success(session);
    }

    /**
     * 更新会话
     */
    @PutMapping("/sessions/{sessionId}")
    public ApiResponse<ChatSession> updateSession(@PathVariable String sessionId,
            @RequestBody UpdateSessionRequest request,
            HttpServletRequest httpRequest) {
        Long userId = userContextUtil.getCurrentUserId(httpRequest);
        ChatSession session = chatSessionService.updateSession(sessionId, userId, request);
        return ApiResponse.success(session);
    }

    /**
     * 归档会话
     */
    @PutMapping("/sessions/{sessionId}/archive")
    public ApiResponse<ChatSession> archiveSession(@PathVariable String sessionId, HttpServletRequest request) {
        Long userId = userContextUtil.getCurrentUserId(request);
        ChatSession session = chatSessionService.archiveSession(sessionId, userId);
        return ApiResponse.success(session);
    }

    /**
     * 统计用户的会话数量
     */
    @GetMapping("/sessions/count")
    public ApiResponse<Long> countUserSessions(HttpServletRequest request) {
        Long userId = userContextUtil.getCurrentUserId(request);
        long count = chatSessionService.countUserSessions(userId);
        return ApiResponse.success(count);
    }
}