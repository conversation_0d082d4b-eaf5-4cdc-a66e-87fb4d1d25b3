package com.kumhosunny.chat.controller;

import com.kumhosunny.common.entity.AiApp;
import com.kumhosunny.common.entity.AiAppSettings;
import com.kumhosunny.common.entity.AiAppTools;
import com.kumhosunny.common.result.ApiResponse;
import com.kumhosunny.chat.service.AiAppService;
import com.kumhosunny.chat.dto.AiAppCategoryWithAppsDto;
import com.kumhosunny.chat.dto.AiAppDetailDto;
import com.kumhosunny.chat.dto.AiAppDto;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * AI应用控制器
 */
@RestController
@RequestMapping("/api/apps")
public class AiAppController {

    private final AiAppService aiAppService;

    public AiAppController(AiAppService aiAppService) {
        this.aiAppService = aiAppService;
    }

    /**
     * 查询目前可用的AI应用
     * 
     * @return 统一格式的API响应
     */
    @GetMapping("/available")
    public ApiResponse<List<AiAppDto>> getAvailableApps() {
        try {
            List<AiApp> apps = aiAppService.getAvailableAppsWithCategory();
            List<AiAppDto> appDtos = apps.stream()
                    .map(AiAppDto::fromEntityWithCategory)
                    .collect(java.util.stream.Collectors.toList());
            return ApiResponse.success("查询可用AI应用成功", appDtos);
        } catch (Exception e) {
            return ApiResponse.error("查询AI应用失败: " + e.getMessage());
        }
    }

    @GetMapping("/categories")
    public ApiResponse<List<AiAppCategoryWithAppsDto>> getAppCategories() {
        return ApiResponse.success(aiAppService.getAppsGroupedByCategory());
    }

    @GetMapping("/{id}")
    public ApiResponse<AiAppDetailDto> getAppDetail(@PathVariable Integer id) {
        return ApiResponse.success(aiAppService.getAppFullInfo(id));
    }

}