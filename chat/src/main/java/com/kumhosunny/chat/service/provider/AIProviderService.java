package com.kumhosunny.chat.service.provider;

import com.kumhosunny.chat.dto.ChatCompletionRequest;
import com.kumhosunny.chat.dto.ChatCompletionResponse;
import com.kumhosunny.common.entity.AiProvider;
import reactor.core.publisher.Mono;

/**
 * AI 提供商服务接口
 * 定义各个AI服务提供商需要实现的统一接口
 */
public interface AIProviderService {

    /**
     * 聊天完成
     * 
     * @param request  聊天完成请求
     * @param provider 提供商配置
     * @return 聊天完成响应
     */
    Mono<ChatCompletionResponse> chatCompletion(ChatCompletionRequest request, AiProvider provider);

    /**
     * 聊天完成 - 流式 (Server-Sent Events)
     * 默认不支持，由具体 Provider 覆盖实现。
     */
    default reactor.core.publisher.Flux<com.kumhosunny.chat.dto.ChatCompletionChunk> chatCompletionStream(
            ChatCompletionRequest request, AiProvider provider) {
        return reactor.core.publisher.Flux.error(
                new UnsupportedOperationException("Streaming not supported by this provider"));
    }

    /**
     * 聊天完成 (兼容旧版)
     * 
     * @deprecated 请使用 chatCompletion(ChatCompletionRequest request, AiProvider
     *             provider)
     */
    @Deprecated
    default Mono<ChatCompletionResponse> chatCompletion(ChatCompletionRequest request) {
        return Mono.error(new UnsupportedOperationException("This method is deprecated."));
    }

    /**
     * 流式聊天完成 (兼容旧版)
     * 
     * @deprecated 请使用 chatCompletionStream(ChatCompletionRequest request,
     *             AiProvider provider)
     */
    @Deprecated
    default reactor.core.publisher.Flux<com.kumhosunny.chat.dto.ChatCompletionChunk> chatCompletionStream(
            ChatCompletionRequest request) {
        return reactor.core.publisher.Flux.error(new UnsupportedOperationException("This method is deprecated."));
    }

    /**
     * 获取提供商名称
     * 
     * @return 提供商名称
     */
    String getProviderName();

    /**
     * 检查服务是否可用
     * 
     * @return 是否可用
     */
    boolean isAvailable();

    /**
     * 检查具体提供商配置是否可用
     * 
     * @param provider 提供商配置
     * @return 是否可用
     */
    default boolean isAvailable(AiProvider provider) {
        if (provider == null) {
            return false;
        }
        return provider.getStatus() == 1 &&
                provider.getApiKey() != null &&
                !provider.getApiKey().trim().isEmpty();
    }

    /**
     * 图像生成
     * 
     * @param request  图像生成请求
     * @param provider 提供商配置
     * @return 图像生成响应
     */
    default Mono<com.kumhosunny.chat.dto.ImageGenerationResponse> imageGeneration(
            com.kumhosunny.chat.dto.ImageGenerationRequest request, AiProvider provider, Long userId) {
        return Mono.error(new UnsupportedOperationException("Image generation not supported by this provider"));
    }
}