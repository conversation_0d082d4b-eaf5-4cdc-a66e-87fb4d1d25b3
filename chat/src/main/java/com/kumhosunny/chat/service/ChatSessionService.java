package com.kumhosunny.chat.service;

import com.kumhosunny.common.entity.ChatSession;
import com.kumhosunny.common.entity.ChatMessage;
import com.kumhosunny.chat.dto.CreateSessionRequest;
import com.kumhosunny.chat.dto.SaveMessageRequest;
import com.kumhosunny.chat.dto.UpdateSessionRequest;

import java.util.List;

/**
 * 聊天会话服务接口
 */
public interface ChatSessionService {

    /**
     * 新建会话
     */
    ChatSession createSession(CreateSessionRequest request, Long userId);

    /**
     * 查询会话数据，带出对话记录
     */
    ChatSession getSessionWithMessages(String sessionId, Long userId);

    /**
     * 对话信息存储
     */
    ChatMessage saveMessage(SaveMessageRequest request, Long userId);

    /**
     * 查询会话历史
     */
    List<ChatSession> getSessionHistory(Long userId);

    /**
     * 删除会话
     */
    boolean deleteSession(String sessionId, Long userId);

    /**
     * 获取用户的活跃会话列表
     */
    List<ChatSession> getActiveSessions(Long userId);

    /**
     * 获取会话的消息列表
     */
    List<ChatMessage> getSessionMessages(String sessionId, Long userId);

    /**
     * 激活会话
     * 将指定会话设置为 active，并将该用户的其他所有会话设置为 archived
     * 
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 更新后的会话
     */
    ChatSession activateSession(String sessionId, Long userId);

    /**
     * 更新会话标题
     */
    ChatSession updateSessionTitle(String sessionId, Long userId, String title);

    /**
     * 归档会话
     */
    ChatSession archiveSession(String sessionId, Long userId);

    /**
     * 统计用户的会话数量
     */
    long countUserSessions(Long userId);

    /**
     * 根据对话内容生成并保存标题
     */
    void generateAndSaveTitle(String sessionId, Long userId);

    ChatSession updateSession(String sessionId, Long userId, UpdateSessionRequest request);
}