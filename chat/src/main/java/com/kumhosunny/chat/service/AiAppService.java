package com.kumhosunny.chat.service;

import com.kumhosunny.common.entity.AiApp;
import com.kumhosunny.common.entity.AiAppSettings;
import com.kumhosunny.common.entity.AiAppTools;
import com.kumhosunny.chat.dto.AiAppCategoryWithAppsDto;
import com.kumhosunny.chat.dto.AiAppDetailDto;

import java.util.List;

/**
 * AI应用服务接口
 */
public interface AiAppService {

    /**
     * 获取所有可用的AI应用（公开应用）
     */
    List<AiApp> getAvailableApps();

    /**
     * 获取所有可用的AI应用（公开应用），预加载分类信息
     */
    List<AiApp> getAvailableAppsWithCategory();

    /**
     * 根据ID获取AI应用详情
     */
    AiApp getAppById(Integer id);

    /**
     * 根据应用ID获取应用设置
     */
    AiAppSettings getAppSettings(Integer appId);

    /**
     * 根据应用ID获取应用工具列表
     */
    List<AiAppTools> getAppTools(Integer appId);

    /**
     * 获取应用的完整信息（包含设置和工具）
     */
    AiAppDetailDto getAppFullInfo(Integer id);

    /**
     * 获取所有应用，按分类分组
     */
    List<AiAppCategoryWithAppsDto> getAppsGroupedByCategory();
}