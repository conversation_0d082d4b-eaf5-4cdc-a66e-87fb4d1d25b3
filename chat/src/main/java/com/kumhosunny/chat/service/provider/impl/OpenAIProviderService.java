package com.kumhosunny.chat.service.provider.impl;

import com.kumhosunny.chat.dto.ChatCompletionRequest;
import com.kumhosunny.chat.dto.ChatCompletionResponse;
import com.kumhosunny.chat.dto.ChatCompletionChunk;
import com.kumhosunny.chat.dto.ChatMessage;
import com.kumhosunny.chat.dto.ImageGenerationRequest;
import com.kumhosunny.chat.dto.ImageGenerationResponse;
import com.kumhosunny.chat.service.provider.AIProviderService;
import com.kumhosunny.common.entity.AiProvider;
import com.kumhosunny.common.repository.AiProviderRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Flux;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.Instant;
import java.util.Arrays;
import java.util.Optional;
import java.util.UUID;
import java.util.Base64;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.IOException;
import java.util.List;
import java.util.ArrayList;
import com.kumhosunny.common.entity.AiGeneratedContent;
import com.kumhosunny.common.repository.AiGeneratedContentRepository;
import com.kumhosunny.common.util.OssUtil;
import com.kumhosunny.common.util.UserContextUtil;
import reactor.core.scheduler.Schedulers;

/**
 * OpenAI 提供商服务实现
 * 从数据库获取API Key和Base URL配置
 */
@Service("openai")
public class OpenAIProviderService implements AIProviderService {

    private static final String PROVIDER_CODE = "openai";
    private static final String DEFAULT_BASE_URL = "https://api.openai.com/v1";

    @Autowired
    private AiProviderRepository aiProviderRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AiGeneratedContentRepository aiGeneratedContentRepository;

    @Autowired
    private OssUtil ossUtil;

    private final WebClient webClient;

    public OpenAIProviderService() {
        this.webClient = WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB
                .build();
    }

    @Override
    public Mono<ChatCompletionResponse> chatCompletion(ChatCompletionRequest request, AiProvider provider) {
        if (!isAvailable(provider)) {
            return Mono.error(new IllegalStateException("OpenAI provider is not available or configured correctly."));
        }

        String baseUrl = provider.getBaseUrl() != null ? provider.getBaseUrl() : DEFAULT_BASE_URL;

        return webClient.post()
                .uri(baseUrl + "/chat/completions")
                .header("Authorization", "Bearer " + provider.getApiKey())
                .header("Content-Type", "application/json")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(ChatCompletionResponse.class)
                .onErrorMap(ex -> new RuntimeException("OpenAI API error: " + ex.getMessage(), ex));
    }

    @Override
    public String getProviderName() {
        return "OpenAI";
    }

    @Override
    public boolean isAvailable() {
        try {
            Optional<AiProvider> provider = aiProviderRepository.findByProviderCode(PROVIDER_CODE);
            return provider.map(this::isAvailable).orElse(false);
        } catch (Exception e) {
            // 如果数据库访问失败，返回false
            return false;
        }
    }

    /**
     * 流式聊天完成
     */
    @Override
    public Flux<ChatCompletionChunk> chatCompletionStream(ChatCompletionRequest request, AiProvider provider) {
        if (!isAvailable(provider)) {
            return Flux.error(new IllegalStateException("OpenAI provider is not available or configured correctly."));
        }
        if (!Boolean.TRUE.equals(request.getStream())) {
            return Flux.error(new IllegalArgumentException("stream 参数必须为 true"));
        }

        String baseUrl = provider.getBaseUrl() != null ? provider.getBaseUrl() : DEFAULT_BASE_URL;

        WebClient client = WebClient.builder().baseUrl(baseUrl).build();

        return client.post()
                .uri("/chat/completions")
                .header("Authorization", "Bearer " + provider.getApiKey())
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .bodyValue(request)
                .retrieve()
                .bodyToFlux(new ParameterizedTypeReference<ServerSentEvent<String>>() {
                })
                .filter(event -> event.data() != null && !event.data().equals("[DONE]"))
                .map(event -> {
                    try {
                        return objectMapper.readValue(event.data(), ChatCompletionChunk.class);
                    } catch (Exception e) {
                        return null;
                    }
                })
                .filter(chunk -> chunk != null)
                .doOnError(WebClientResponseException.class, ex -> {
                    // log error if needed
                });
    }

    /**
     * 图像生成
     */
    @Override
    public Mono<ImageGenerationResponse> imageGeneration(ImageGenerationRequest request, AiProvider provider,
            Long userId) {
        if (!isAvailable(provider)) {
            return Mono.error(new IllegalStateException("OpenAI provider is not available or configured correctly."));
        }

        String baseUrl = provider.getBaseUrl() != null ? provider.getBaseUrl() : DEFAULT_BASE_URL;

        // 1. 调用外部API
        return webClient.post()
                .uri(baseUrl + "/images/generations")
                .header("Authorization", "Bearer " + provider.getApiKey())
                .header("Content-Type", "application/json")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(ImageGenerationResponse.class)
                .flatMap(response -> {
                    // 2. 处理响应，上传到OSS并保存记录
                    return processAndSaveImage(response, request, userId)
                            .map(newUrls -> {
                                // 3. 用我们自己的OSS URL替换原始响应中的URL
                                for (int i = 0; i < response.getData().size(); i++) {
                                    response.getData().get(i).setUrl(newUrls.get(i));
                                    response.getData().get(i).setB64Json(null); // 清空base64
                                }
                                return response;
                            });
                })
                .onErrorMap(ex -> new RuntimeException("OpenAI image generation error: " + ex.getMessage(), ex));
    }

    private Mono<List<String>> processAndSaveImage(ImageGenerationResponse response, ImageGenerationRequest request,
            Long userId) {
        List<Mono<String>> uploadMonos = new ArrayList<>();

        for (ImageGenerationResponse.ImageData imageData : response.getData()) {
            Mono<String> uploadMono = Mono.fromCallable(() -> {
                InputStream imageInputStream;
                String fileExtension;

                // a. 获取图片输入流
                if (imageData.getB64Json() != null && !imageData.getB64Json().isEmpty()) {
                    byte[] imageBytes = Base64.getDecoder().decode(imageData.getB64Json());
                    imageInputStream = new ByteArrayInputStream(imageBytes);
                    fileExtension = ".png"; // Base64通常是png
                } else if (imageData.getUrl() != null && !imageData.getUrl().isEmpty()) {
                    // 从URL下载图片
                    byte[] imageBytes = webClient.get().uri(imageData.getUrl()).retrieve().bodyToMono(byte[].class)
                            .block();
                    if (imageBytes == null) {
                        throw new IOException("Failed to download image from url: " + imageData.getUrl());
                    }
                    imageInputStream = new ByteArrayInputStream(imageBytes);
                    fileExtension = ".jpg"; // URL通常是jpg
                } else {
                    throw new IllegalArgumentException("Image data is missing from provider response.");
                }

                // b. 上传到OSS
                String fileName = UUID.randomUUID().toString() + fileExtension;
                String ossUrl = ossUtil.uploadFile(imageInputStream, fileName, "image/" + fileExtension.substring(1));

                // c. 保存到数据库
                AiGeneratedContent content = new AiGeneratedContent()
                        .setContentId(UUID.randomUUID().toString())
                        .setUserId(userId)
                        .setContentType("IMAGE")
                        .setGenerationStatus("COMPLETED")
                        .setPrompt(request.getPrompt())
                        .setRevisedPrompt(imageData.getRevisedPrompt())
                        .setModelUsed(request.getModel())
                        .setSize(request.getSize())
                        .setStyle(request.getStyle())
                        .setQuality(request.getQuality())
                        .setOssUrl(ossUrl)
                        .setResponseFormat(request.getResponseFormat());

                aiGeneratedContentRepository.save(content);

                return ossUrl;
            }).subscribeOn(Schedulers.boundedElastic());
            uploadMonos.add(uploadMono);
        }

        return Flux.merge(uploadMonos).collectList();
    }
}