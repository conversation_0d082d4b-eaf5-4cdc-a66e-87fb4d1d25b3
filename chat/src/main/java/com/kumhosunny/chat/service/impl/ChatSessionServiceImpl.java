package com.kumhosunny.chat.service.impl;

import com.kumhosunny.common.entity.ChatSession;
import com.kumhosunny.common.entity.ChatMessage;
import com.kumhosunny.common.repository.ChatSessionRepository;
import com.kumhosunny.common.repository.ChatMessageRepository;
import com.kumhosunny.chat.dto.CreateSessionRequest;
import com.kumhosunny.chat.dto.SaveMessageRequest;
import com.kumhosunny.chat.dto.UpdateSessionRequest;
import com.kumhosunny.chat.service.ChatSessionService;
import com.kumhosunny.chat.service.ModelRouterService;
import com.kumhosunny.chat.dto.ChatCompletionRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 聊天会话服务实现类
 */
@Service
@Slf4j
public class ChatSessionServiceImpl implements ChatSessionService {

    private final ChatSessionRepository chatSessionRepository;
    private final ChatMessageRepository chatMessageRepository;
    private final ModelRouterService modelRouterService;

    @Autowired
    public ChatSessionServiceImpl(ChatSessionRepository chatSessionRepository,
            ChatMessageRepository chatMessageRepository,
            ModelRouterService modelRouterService) {
        this.chatSessionRepository = chatSessionRepository;
        this.chatMessageRepository = chatMessageRepository;
        this.modelRouterService = modelRouterService;
    }

    @Override
    @Transactional
    public ChatSession createSession(CreateSessionRequest request, Long userId) {
        // 1. 将该用户的所有会话状态设置为 archived
        chatSessionRepository.updateStatusByUserId(userId, ChatSession.SessionStatus.archived);

        // 2. 创建新会话并设置为 active
        ChatSession session = new ChatSession()
                .setUserId(userId)
                .setSessionTitle(StringUtils.hasText(request.getSessionTitle()) ? request.getSessionTitle() : "新对话")
                .setModelUsed(request.getModelUsed())
                .setMetadata(request.getMetadata())
                .setStatus(ChatSession.SessionStatus.active);

        return chatSessionRepository.save(session);
    }

    @Override
    @Transactional(readOnly = true)
    public ChatSession getSessionWithMessages(String sessionId, Long userId) {
        // 激活当前会话，并归档其他会话
        activateSession(sessionId, userId);
        return chatSessionRepository.findByIdAndUserIdWithMessages(sessionId, userId)
                .orElse(null);
    }

    @Override
    @Transactional
    public ChatMessage saveMessage(SaveMessageRequest request, Long userId) {
        // 验证会话是否存在且属于该用户
        ChatSession session = chatSessionRepository.findById(request.getSessionId())
                .orElseThrow(() -> new RuntimeException("会话不存在"));

        if (!session.getUserId().equals(userId)) {
            throw new RuntimeException("无权限访问该会话");
        }

        // 保存消息时要考虑特殊字符，使用 TEXT 字段类型已经可以处理
        ChatMessage message = new ChatMessage()
                .setSessionId(request.getSessionId())
                .setSender(request.getSender())
                .setContent(request.getContent())
                .setContentType(request.getContentType())
                .setParentMessageId(request.getParentMessageId())
                .setTokenCount(request.getTokenCount())
                .setMetadata(request.getMetadata());

        ChatMessage savedMessage = chatMessageRepository.save(message);

        // 更新会话的最后更新时间
        session.setUpdatedAt(LocalDateTime.now());
        chatSessionRepository.save(session);

        return savedMessage;
    }

    @Override
    @Transactional(readOnly = true)
    public List<ChatSession> getSessionHistory(Long userId) {
        return chatSessionRepository.findByUserIdOrderByUpdatedAtDesc(userId);
    }

    @Override
    @Transactional
    public boolean deleteSession(String sessionId, Long userId) {
        ChatSession session = chatSessionRepository.findByIdAndUserId(sessionId, userId)
                .orElse(null);

        if (session == null) {
            return false;
        }

        // 删除相关消息
        chatMessageRepository.deleteBySessionId(sessionId);

        // 删除会话
        chatSessionRepository.delete(session);

        return true;
    }

    @Override
    @Transactional(readOnly = true)
    public List<ChatSession> getActiveSessions(Long userId) {
        return chatSessionRepository.findByUserIdAndStatusOrderByUpdatedAtDesc(
                userId, ChatSession.SessionStatus.active);
    }

    @Override
    public List<ChatMessage> getSessionMessages(String sessionId, Long userId) {
        // 激活当前会话，并归档其他会话
        activateSession(sessionId, userId);
        // 验证会话权限
        ChatSession session = chatSessionRepository.findByIdAndUserId(sessionId, userId)
                .orElse(null);

        if (session == null) {
            throw new RuntimeException("会话不存在或无权限访问");
        }

        return chatMessageRepository.findBySessionIdOrderByCreatedAtAsc(sessionId);
    }

    @Override
    @Transactional
    public ChatSession updateSessionTitle(String sessionId, Long userId, String title) {
        ChatSession session = chatSessionRepository.findByIdAndUserId(sessionId, userId)
                .orElse(null);

        if (session == null) {
            throw new RuntimeException("会话不存在或无权限访问");
        }

        session.setSessionTitle(title);
        return chatSessionRepository.save(session);
    }

    @Override
    @Transactional
    public ChatSession archiveSession(String sessionId, Long userId) {
        ChatSession session = chatSessionRepository.findByIdAndUserId(sessionId, userId)
                .orElse(null);

        if (session == null) {
            throw new RuntimeException("会话不存在或无权限访问");
        }

        session.setStatus(ChatSession.SessionStatus.archived);
        return chatSessionRepository.save(session);
    }

    @Override
    public long countUserSessions(Long userId) {
        return chatSessionRepository.countByUserId(userId);
    }

    @Override
    @Transactional
    public ChatSession activateSession(String sessionId, Long userId) {
        // 1. 验证会话是否存在且属于该用户
        ChatSession session = chatSessionRepository.findByIdAndUserId(sessionId, userId)
                .orElseThrow(() -> new RuntimeException("会话不存在或无权限访问"));

        // 2. 将该用户的所有会话状态设置为 archived
        chatSessionRepository.updateStatusByUserId(userId, ChatSession.SessionStatus.archived);

        // 3. 激活当前会话
        session.setStatus(ChatSession.SessionStatus.active);
        return chatSessionRepository.save(session);
    }

    @Override
    public ChatSession updateSession(String sessionId, Long userId, UpdateSessionRequest request) {
        ChatSession session = chatSessionRepository.findById(sessionId)
                .orElseThrow(() -> new RuntimeException("会话不存在"));

        if (!session.getUserId().equals(userId)) {
            throw new RuntimeException("无权修改此会话");
        }

        if (request.getTitle() != null && !request.getTitle().isEmpty()) {
            session.setSessionTitle(request.getTitle());
        }

        if (request.getModelUsed() != null) {
            session.setModelUsed(request.getModelUsed());
        }

        if (request.getStatus() != null) {
            session.setStatus(request.getStatus());
        }

        if (request.getMetadata() != null) {
            session.setMetadata(request.getMetadata());
        }

        return chatSessionRepository.save(session);
    }

    @Async
    @Override
    @Transactional
    public void generateAndSaveTitle(String sessionId, Long userId) {
        try {
            // 使用 a transactional 方法来确保 session 对象是 managed state
            chatSessionRepository.findById(sessionId).ifPresent(session -> {
                String currentTitle = session.getSessionTitle();
                // 默认标题列表，如果会话标题是其中之一，则会生成新标题
                List<String> defaultTitles = List.of("新对话", "新建对话", "新建会话");

                if (currentTitle != null && !defaultTitles.contains(currentTitle)) {
                    log.info("Session {} has a custom title: '{}'. Skipping generation.", sessionId, currentTitle);
                    return;
                }

                // 获取前4条对话记录
                List<ChatMessage> messages = chatMessageRepository.findTop4BySessionIdOrderByCreatedAtAsc(sessionId);
                if (messages.size() <= 3) {
                    log.info("Not enough messages in session {} to generate title. Found {} messages.", sessionId,
                            messages.size());
                    return;
                }

                String conversationSummary = messages.stream()
                        .map(msg -> msg.getSender().name() + ": " + msg.getContent())
                        .collect(Collectors.joining("\n"));

                String prompt = "根据以下对话内容，生成一个简洁的、不超过10个词的标题。请直接返回标题本身，不要包含任何额外的解释、标点符号或引号。\n\n对话内容：\n"
                        + conversationSummary;

                List<com.kumhosunny.chat.dto.ChatMessage> titleRequestMessages = List
                        .of(new com.kumhosunny.chat.dto.ChatMessage("user", prompt));

                ChatCompletionRequest titleRequest = new ChatCompletionRequest();
                titleRequest.setModel("deepseek-ai/DeepSeek-V3"); // 或者使用一个专门用于此类任务的轻量级模型
                titleRequest.setMessages(titleRequestMessages);
                titleRequest.setStream(false);

                log.info("Generating title for session: {}", sessionId);

                modelRouterService.routeAndProcess(titleRequest)
                        .doOnSuccess(response -> {
                            if (response != null && response.getChoices() != null && !response.getChoices().isEmpty()) {
                                String title = response.getChoices().get(0).getMessage().getContent().toString();
                                // 去除可能的引号
                                title = title.replace("\"", "").replace("'", "");
                                log.info("Generated title for session {}: {}", sessionId, title);

                                // 再次获取 session 以确保它没有被其他事务修改
                                ChatSession sessionToUpdate = chatSessionRepository.findById(sessionId).orElse(null);
                                if (sessionToUpdate != null) {
                                    sessionToUpdate.setSessionTitle(title);
                                    chatSessionRepository.save(sessionToUpdate);
                                }
                            }
                        })
                        .doOnError(error -> log.error("Failed to generate title for session {}", sessionId, error))
                        .subscribe();
            });
        } catch (Exception e) {
            log.error("Error in generateAndSaveTitle for session {}", sessionId, e);
        }
    }
}