package com.kumhosunny.chat.service;

import com.kumhosunny.chat.dto.ChatCompletionRequest;
import com.kumhosunny.chat.dto.ChatCompletionResponse;
import com.kumhosunny.chat.dto.ModelsResponse;
import com.kumhosunny.chat.dto.ImageGenerationRequest;
import com.kumhosunny.chat.dto.ImageGenerationResponse;
import reactor.core.publisher.Mono;

/**
 * 模型路由服务接口
 * 负责根据模型名称路由到不同的AI服务提供商
 */
public interface ModelRouterService {

    /**
     * 路由并处理聊天完成请求
     * 
     * @param request 聊天完成请求
     * @return 聊天完成响应
     */
    Mono<ChatCompletionResponse> routeAndProcess(ChatCompletionRequest request);

    /**
     * 路由并处理流式聊天完成请求
     */
    reactor.core.publisher.Flux<com.kumhosunny.chat.dto.ChatCompletionChunk> routeAndStream(
            ChatCompletionRequest request);

    /**
     * 获取所有可用的模型列表
     * 
     * @return 模型列表响应
     */
    Mono<ModelsResponse> getAvailableModels();

    /**
     * 检查模型是否可用
     * 
     * @param modelId 模型ID
     * @return 是否可用
     */
    boolean isModelAvailable(String modelId);

    /**
     * 路由并处理图像生成请求
     * 
     * @param request 图像生成请求
     * @return 图像生成响应
     */
    Mono<ImageGenerationResponse> imageGeneration(ImageGenerationRequest request, Long userId);
}