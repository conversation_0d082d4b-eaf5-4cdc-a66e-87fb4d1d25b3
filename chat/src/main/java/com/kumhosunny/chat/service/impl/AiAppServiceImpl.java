package com.kumhosunny.chat.service.impl;

import com.kumhosunny.chat.dto.AiAppCategoryWithAppsDto;
import com.kumhosunny.chat.dto.AiAppDto;
import com.kumhosunny.chat.dto.AiAppDetailDto;
import com.kumhosunny.common.entity.AiApp;
import com.kumhosunny.common.entity.AiAppCategory;
import com.kumhosunny.common.entity.AiAppSettings;
import com.kumhosunny.common.entity.AiAppTools;
import com.kumhosunny.common.repository.AiAppCategoryRepository;
import com.kumhosunny.common.repository.AiAppRepository;
import com.kumhosunny.common.repository.AiAppSettingsRepository;
import com.kumhosunny.common.repository.AiAppToolsRepository;
import com.kumhosunny.chat.service.AiAppService;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AI应用服务实现类
 */
@Service
public class AiAppServiceImpl implements AiAppService {

    private final AiAppRepository aiAppRepository;
    private final AiAppSettingsRepository aiAppSettingsRepository;
    private final AiAppToolsRepository aiAppToolsRepository;
    private final AiAppCategoryRepository aiAppCategoryRepository;

    public AiAppServiceImpl(AiAppRepository aiAppRepository,
            AiAppSettingsRepository aiAppSettingsRepository,
            AiAppToolsRepository aiAppToolsRepository,
            AiAppCategoryRepository aiAppCategoryRepository) {
        this.aiAppRepository = aiAppRepository;
        this.aiAppSettingsRepository = aiAppSettingsRepository;
        this.aiAppToolsRepository = aiAppToolsRepository;
        this.aiAppCategoryRepository = aiAppCategoryRepository;
    }

    @Override
    public List<AiApp> getAvailableApps() {
        return aiAppRepository.findByIsPublicTrueOrderByCreatedAtDesc();
    }

    @Override
    public List<AiApp> getAvailableAppsWithCategory() {
        return aiAppRepository.findByIsPublicTrueWithCategoryOrderByCreatedAtDesc();
    }

    @Override
    public AiApp getAppById(Integer id) {
        return aiAppRepository.findById(id).orElse(null);
    }

    @Override
    public AiAppSettings getAppSettings(Integer appId) {
        return aiAppSettingsRepository.findByAppId(appId).orElse(null);
    }

    @Override
    public List<AiAppTools> getAppTools(Integer appId) {
        return aiAppToolsRepository.findByAppId(appId);
    }

    @Override
    public AiAppDetailDto getAppFullInfo(Integer id) {
        AiApp app = getAppById(id);
        if (app == null) {
            return null;
        }
        AiAppSettings settings = getAppSettings(id);
        List<AiAppTools> tools = getAppTools(id);

        return AiAppDetailDto.fromEntity(app, settings, tools);
    }

    @Override
    public List<AiAppCategoryWithAppsDto> getAppsGroupedByCategory() {
        // 1. 获取所有公开的应用,并预加载分类
        List<AiApp> apps = aiAppRepository.findByIsPublicTrueWithCategoryOrderByCreatedAtDesc();
        List<AiAppDto> appDtos = apps.stream().map(AiAppDto::fromEntityWithCategory).collect(Collectors.toList());

        // 2. 按 categoryId 分组
        Map<Integer, List<AiAppDto>> appsByCategory = appDtos.stream()
                .filter(app -> app.getCategoryId() != null)
                .collect(Collectors.groupingBy(AiAppDto::getCategoryId));

        // 3. 获取所有分类并转换为DTO
        List<AiAppCategory> categories = aiAppCategoryRepository.findAll(Sort.by(Sort.Direction.ASC, "sortOrder"));

        return categories.stream()
                .map(category -> AiAppCategoryWithAppsDto.fromEntity(
                        category,
                        appsByCategory.get(category.getId())))
                .collect(Collectors.toList());
    }
}