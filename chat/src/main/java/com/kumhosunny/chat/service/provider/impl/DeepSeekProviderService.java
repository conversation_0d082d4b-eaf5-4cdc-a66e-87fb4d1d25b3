package com.kumhosunny.chat.service.provider.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kumhosunny.chat.dto.ChatCompletionRequest;
import com.kumhosunny.chat.dto.ChatCompletionResponse;
import com.kumhosunny.chat.dto.ChatCompletionChunk;
import com.kumhosunny.chat.dto.ChatMessage;
import com.kumhosunny.chat.service.provider.AIProviderService;
import com.kumhosunny.common.entity.AiProvider;
import com.kumhosunny.common.repository.AiProviderRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * DeepSeek AI 提供商服务实现
 */
@Service("deepseek")
public class DeepSeekProviderService implements AIProviderService {

    private static final Logger log = LoggerFactory.getLogger(DeepSeekProviderService.class);
    private static final String PROVIDER_CODE = "deepseek";
    private static final String DEFAULT_BASE_URL = "https://api.deepseek.com";

    @Autowired
    private AiProviderRepository aiProviderRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public Mono<ChatCompletionResponse> chatCompletion(ChatCompletionRequest request, AiProvider provider) {
        log.info("DeepSeek API 请求开始，模型: {}, 流式: {}", request.getModel(), request.getStream());

        if (!isAvailable(provider)) {
            return Mono.error(new RuntimeException("DeepSeek provider is not available or configured correctly."));
        }

        String baseUrl = provider.getBaseUrl() != null ? provider.getBaseUrl() : DEFAULT_BASE_URL;

        // 记录请求详情
        try {
            String requestJson = objectMapper.writeValueAsString(request);
            log.info("DeepSeek API 请求URL: {}/v1/chat/completions", baseUrl);
            log.info("DeepSeek API 请求头: Authorization: Bearer {}",
                    provider.getApiKey().substring(0, Math.min(10, provider.getApiKey().length())) + "...");
            log.info("DeepSeek API 请求体: {}", requestJson);
        } catch (Exception e) {
            log.warn("无法序列化请求体进行日志记录: {}", e.getMessage());
        }

        WebClient webClient = WebClient.builder()
                .baseUrl(baseUrl)
                .build();

        // 检查是否为流式请求
        if (Boolean.TRUE.equals(request.getStream())) {
            // For DeepSeek, even in streaming mode, we might want to return a single
            // aggregated response.
            // The `handleStreamingResponse` seems to do that.
            // If the client wants a real stream, they should call `chatCompletionStream`.
            return handleStreamingResponse(webClient, provider, request);
        } else {
            return handleNonStreamingResponse(webClient, provider, request);
        }
    }

    /**
     * 处理非流式响应
     */
    private Mono<ChatCompletionResponse> handleNonStreamingResponse(WebClient webClient, AiProvider provider,
            ChatCompletionRequest request) {
        return webClient.post()
                .uri("/v1/chat/completions")
                .header("Authorization", "Bearer " + provider.getApiKey())
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(ChatCompletionResponse.class)
                .timeout(Duration.ofSeconds(60))
                .doOnSuccess(response -> {
                    log.info("DeepSeek API 非流式请求成功，响应: {}", response);
                })
                .onErrorMap(WebClientResponseException.class, ex -> {
                    log.error("DeepSeek API 非流式请求失败 - 状态码: {}, 响应体: {}",
                            ex.getStatusCode(), ex.getResponseBodyAsString());
                    return new RuntimeException("DeepSeek API调用失败: " + ex.getMessage() +
                            ", 响应: " + ex.getResponseBodyAsString(), ex);
                })
                .onErrorMap(Exception.class, ex -> {
                    if (!(ex instanceof RuntimeException)) {
                        log.error("DeepSeek API 非流式请求异常: {}", ex.getMessage(), ex);
                        return new RuntimeException("DeepSeek API调用异常: " + ex.getMessage(), ex);
                    }
                    return ex;
                });
    }

    /**
     * 处理流式响应
     */
    private Mono<ChatCompletionResponse> handleStreamingResponse(WebClient webClient, AiProvider provider,
            ChatCompletionRequest request) {
        return webClient.post()
                .uri("/v1/chat/completions")
                .header("Authorization", "Bearer " + provider.getApiKey())
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .bodyValue(request)
                .retrieve()
                .bodyToFlux(new ParameterizedTypeReference<ServerSentEvent<String>>() {
                })
                .timeout(Duration.ofSeconds(60))
                .filter(event -> event.data() != null && !event.data().equals("[DONE]"))
                .map(event -> {
                    try {
                        return objectMapper.readValue(event.data(), ChatCompletionChunk.class);
                    } catch (Exception e) {
                        log.warn("解析流式响应块失败: {}, 数据: {}", e.getMessage(), event.data());
                        return null;
                    }
                })
                .filter(chunk -> chunk != null)
                .collectList()
                .map(this::convertChunksToResponse)
                .doOnSuccess(response -> {
                    log.info("DeepSeek API 流式请求成功，合并了 {} 个事件", response != null ? "多个" : "0");
                })
                .onErrorMap(WebClientResponseException.class, ex -> {
                    log.error("DeepSeek API 流式请求失败 - 状态码: {}, 响应体: {}",
                            ex.getStatusCode(), ex.getResponseBodyAsString());
                    return new RuntimeException("DeepSeek API流式调用失败: " + ex.getMessage() +
                            ", 响应: " + ex.getResponseBodyAsString(), ex);
                })
                .onErrorMap(Exception.class, ex -> {
                    if (!(ex instanceof RuntimeException)) {
                        log.error("DeepSeek API 流式请求异常: {}", ex.getMessage(), ex);
                        return new RuntimeException("DeepSeek API流式调用异常: " + ex.getMessage(), ex);
                    }
                    return ex;
                });
    }

    /**
     * 将流式响应块转换为完整的响应对象
     */
    private ChatCompletionResponse convertChunksToResponse(List<ChatCompletionChunk> chunks) {
        if (chunks.isEmpty()) {
            throw new RuntimeException("没有收到有效的流式响应数据");
        }

        ChatCompletionChunk firstChunk = chunks.get(0);
        ChatCompletionResponse response = new ChatCompletionResponse();

        // 设置基本信息
        response.setId(firstChunk.getId());
        response.setObject("chat.completion");
        response.setCreated(firstChunk.getCreated());
        response.setModel(firstChunk.getModel());
        response.setSystemFingerprint(firstChunk.getSystemFingerprint());

        // 合并内容
        StringBuilder contentBuilder = new StringBuilder();
        String finishReason = null;
        ChatCompletionResponse.Usage usage = null;

        for (ChatCompletionChunk chunk : chunks) {
            if (chunk.getChoices() != null && !chunk.getChoices().isEmpty()) {
                ChatCompletionChunk.ChunkChoice choice = chunk.getChoices().get(0);
                if (choice.getDelta() != null && choice.getDelta().getContent() != null) {
                    contentBuilder.append(choice.getDelta().getContent());
                }
                if (choice.getFinishReason() != null) {
                    finishReason = choice.getFinishReason();
                }
            }

            // 获取最后一个包含usage信息的chunk
            if (chunk.getUsage() != null) {
                usage = new ChatCompletionResponse.Usage();
                usage.setPromptTokens(chunk.getUsage().getPromptTokens());
                usage.setCompletionTokens(chunk.getUsage().getCompletionTokens());
                usage.setTotalTokens(chunk.getUsage().getTotalTokens());
            }
        }

        // 创建选择项
        ChatMessage message = new ChatMessage("assistant", contentBuilder.toString());
        ChatCompletionResponse.Choice choice = new ChatCompletionResponse.Choice(0, message, finishReason);
        response.setChoices(List.of(choice));
        response.setUsage(usage);

        log.info("流式响应转换完成，内容长度: {}", contentBuilder.length());
        return response;
    }

    @Override
    public String getProviderName() {
        return PROVIDER_CODE;
    }

    @Override
    public boolean isAvailable() {
        try {
            return aiProviderRepository.findByProviderCode(PROVIDER_CODE)
                    .map(this::isAvailable)
                    .orElse(false);
        } catch (Exception e) {
            log.error("检查DeepSeek提供商可用性时出错: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 流式响应: 直接将 DeepSeek SSE 数据转为 ChatCompletionChunk 并向上游返回
     */
    @Override
    public Flux<ChatCompletionChunk> chatCompletionStream(ChatCompletionRequest request, AiProvider provider) {
        if (!isAvailable(provider)) {
            return Flux.error(new RuntimeException("DeepSeek provider is not available or configured correctly."));
        }
        if (!Boolean.TRUE.equals(request.getStream())) {
            return Flux.error(new IllegalArgumentException("stream 参数必须为 true"));
        }

        String baseUrl = provider.getBaseUrl() != null ? provider.getBaseUrl() : DEFAULT_BASE_URL;

        WebClient webClient = WebClient.builder()
                .baseUrl(baseUrl)
                .build();

        return webClient.post()
                .uri("/v1/chat/completions")
                .header("Authorization", "Bearer " + provider.getApiKey())
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .bodyValue(request)
                .retrieve()
                .bodyToFlux(new ParameterizedTypeReference<ServerSentEvent<String>>() {
                })
                .timeout(Duration.ofSeconds(60))
                .filter(event -> event.data() != null && !event.data().equals("[DONE]"))
                .map(event -> {
                    try {
                        return objectMapper.readValue(event.data(), ChatCompletionChunk.class);
                    } catch (Exception e) {
                        log.warn("解析流式响应块失败: {}, 数据: {}", e.getMessage(), event.data());
                        return null;
                    }
                })
                .filter(chunk -> chunk != null)
                .doOnError(WebClientResponseException.class, ex -> {
                    log.error("DeepSeek API 流式请求失败 - 状态码: {}, 响应体: {}",
                            ex.getStatusCode(), ex.getResponseBodyAsString(), ex);
                })
                .doOnError(Exception.class, ex -> {
                    if (!(ex instanceof WebClientResponseException)) {
                        log.error("DeepSeek API 流式请求异常: {}", ex.getMessage(), ex);
                    }
                });
    }
}