package com.kumhosunny.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 搜索服务配置属性
 * 
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "app.search")
public class SearchProperties {

    private DuckDuckGo duckduckgo = new DuckDuckGo();

    public DuckDuckGo getDuckduckgo() {
        return duckduckgo;
    }

    public void setDuckduckgo(DuckDuckGo duckduckgo) {
        this.duckduckgo = duckduckgo;
    }

    /**
     * DuckDuckGo 搜索服务配置
     */
    public static class DuckDuckGo {
        private String apiUrl = "http://192.168.170.66:6060";
        private int timeout = 30000;
        private int maxResults = 10;
        private int summaryMaxLength = 200;
        private String region = "zh-CN";

        public String getApiUrl() {
            return apiUrl;
        }

        public void setApiUrl(String apiUrl) {
            this.apiUrl = apiUrl;
        }

        public int getTimeout() {
            return timeout;
        }

        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }

        public int getMaxResults() {
            return maxResults;
        }

        public void setMaxResults(int maxResults) {
            this.maxResults = maxResults;
        }

        public int getSummaryMaxLength() {
            return summaryMaxLength;
        }

        public void setSummaryMaxLength(int summaryMaxLength) {
            this.summaryMaxLength = summaryMaxLength;
        }

        public String getRegion() {
            return region;
        }

        public void setRegion(String region) {
            this.region = region;
        }
    }
}