package com.kumhosunny.common.config;

import io.qdrant.client.QdrantClient;
import io.qdrant.client.QdrantGrpcClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Qdrant向量数据库配置类
 * 
 * <AUTHOR> AI
 */
@Slf4j
@Configuration
public class QdrantConfig {

    @Value("${qdrant.host:**************}")
    private String host;

    @Value("${qdrant.port:6334}")
    private int port;

    @Value("${qdrant.use-tls:false}")
    private boolean useTls;

    @Value("${qdrant.api-key:}")
    private String apiKey;

    /**
     * 创建Qdrant客户端Bean
     */
    @Bean
    public QdrantClient qdrantClient() {
        try {
            QdrantGrpcClient.Builder builder = QdrantGrpcClient.newBuilder(host, port, useTls);

            // 如果配置了API Key，则添加认证
            if (apiKey != null && !apiKey.trim().isEmpty()) {
                builder.withApiKey(apiKey);
            }

            QdrantClient client = new QdrantClient(builder.build());
            log.info("成功连接到Qdrant向量数据库: {}:{}", host, port);
            return client;
        } catch (Exception e) {
            log.error("连接Qdrant向量数据库失败: {}:{}", host, port, e);
            throw new RuntimeException("无法连接到Qdrant向量数据库", e);
        }
    }
}