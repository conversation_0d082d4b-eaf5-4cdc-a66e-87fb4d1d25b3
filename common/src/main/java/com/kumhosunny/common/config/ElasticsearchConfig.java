package com.kumhosunny.common.config;

import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @program: kum-ai-app-backend
 * @description:
 * @author: wye
 * @create: 2025-06-18 18:03
 **/

@Configuration
public class ElasticsearchConfig {

    @Bean
    public RestHighLevelClient restHighLevelClient(){
        RestHighLevelClient esClient = new RestHighLevelClient(
                RestClient.builder(new HttpHost("**************", 9200, "http"))
        );
        return esClient;
    }
}
