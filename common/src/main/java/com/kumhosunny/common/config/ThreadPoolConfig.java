package com.kumhosunny.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置类
 * 为不同的异步任务配置专用的线程池
 * 
 * <AUTHOR>
 */
@Configuration
@EnableAsync
public class ThreadPoolConfig {

    @Value("${app.vectorization.thread-pool.core-size:4}")
    private int vectorizationCorePoolSize;

    @Value("${app.vectorization.thread-pool.max-size:8}")
    private int vectorizationMaxPoolSize;

    @Value("${app.vectorization.thread-pool.queue-capacity:100}")
    private int vectorizationQueueCapacity;

    @Value("${app.vectorization.thread-pool.keep-alive-seconds:60}")
    private int vectorizationKeepAliveSeconds;

    /**
     * 向量化处理专用线程池
     * 用于并行处理文档分片的向量化任务
     */
    @Bean("vectorizationExecutor")
    public Executor vectorizationExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(vectorizationCorePoolSize);
        
        // 最大线程数
        executor.setMaxPoolSize(vectorizationMaxPoolSize);
        
        // 队列容量
        executor.setQueueCapacity(vectorizationQueueCapacity);
        
        // 线程保活时间
        executor.setKeepAliveSeconds(vectorizationKeepAliveSeconds);
        
        // 线程名前缀
        executor.setThreadNamePrefix("Vectorization-");
        
        // 拒绝策略：调用者运行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 关闭时等待任务完成
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        return executor;
    }

    /**
     * 通用异步任务线程池
     */
    @Bean("taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(4);
        executor.setQueueCapacity(50);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("Async-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }
} 