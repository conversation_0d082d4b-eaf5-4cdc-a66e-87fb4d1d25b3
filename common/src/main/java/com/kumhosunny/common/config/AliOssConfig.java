package com.kumhosunny.common.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云OSS配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "aliyun.oss")
public class AliOssConfig {

    /**
     * OSS Endpoint
     */
    private String endpoint;

    /**
     * AccessKey ID (映射配置文件中的access-key-id)
     */
    private String accessKeyId;

    /**
     * AccessKey Secret (映射配置文件中的access-key-secret)
     */
    private String accessKeySecret;

    /**
     * Bucket名称 (映射配置文件中的bucket-name)
     */
    private String bucketName;

    /**
     * 文件URL前缀 (映射配置文件中的url-prefix)
     */
    private String urlPrefix;

    /**
     * 文件上传路径前缀 (映射配置文件中的path-prefix)
     */
    private String pathPrefix = "uploads/";

    @Bean
    public OSS ossClient() {
        return new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
    }
}