package com.kumhosunny.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * 环境配置工具类
 * 用于获取当前环境信息和配置
 * 
 * <AUTHOR>
 * @since 2025-06-11
 */
@Component
public class EnvironmentConfig {

    @Autowired
    private Environment environment;

    /**
     * 判断是否为开发环境
     */
    public boolean isDev() {
        return hasProfile("dev");
    }

    /**
     * 判断是否为生产环境
     */
    public boolean isProd() {
        return hasProfile("prod");
    }

    /**
     * 判断是否为测试环境
     */
    public boolean isTest() {
        return hasProfile("test");
    }

    /**
     * 获取当前激活的环境
     */
    public String getActiveProfile() {
        String[] profiles = environment.getActiveProfiles();
        if (profiles.length > 0) {
            return profiles[0];
        }
        return "default";
    }

    /**
     * 获取所有激活的环境
     */
    public String[] getActiveProfiles() {
        return environment.getActiveProfiles();
    }

    /**
     * 检查是否包含指定的环境配置
     */
    public boolean hasProfile(String profile) {
        String[] profiles = environment.getActiveProfiles();
        for (String activeProfile : profiles) {
            if (activeProfile.equals(profile)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取配置属性值
     */
    public String getProperty(String key) {
        return environment.getProperty(key);
    }

    /**
     * 获取配置属性值，如果不存在则返回默认值
     */
    public String getProperty(String key, String defaultValue) {
        return environment.getProperty(key, defaultValue);
    }

    /**
     * 获取应用名称
     */
    public String getApplicationName() {
        return getProperty("spring.application.name", "kumhosunny-ai-app");
    }

    /**
     * 获取服务器端口
     */
    public String getServerPort() {
        return getProperty("server.port", "8080");
    }

    /**
     * 获取上下文路径
     */
    public String getContextPath() {
        return getProperty("server.servlet.context-path", "");
    }

    /**
     * 获取环境描述信息
     */
    public String getEnvironmentDescription() {
        String profile = getActiveProfile();
        switch (profile) {
            case "dev":
                return "开发环境 - 用于本地开发和调试";
            case "prod":
                return "生产环境 - 正式运行环境";
            case "test":
                return "测试环境 - 用于功能测试";
            default:
                return "默认环境";
        }
    }
}