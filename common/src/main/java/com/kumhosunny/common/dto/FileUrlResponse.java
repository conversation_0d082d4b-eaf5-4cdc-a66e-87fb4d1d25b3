package com.kumhosunny.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件外链响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FileUrlResponse {

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件外链URL
     */
    private String fileUrl;

    /**
     * 是否为临时链接
     */
    private Boolean isTemporary;

    /**
     * 过期时间（临时链接有效，时间戳）
     */
    private Long expireTime;

    /**
     * 生成时间戳
     */
    private Long generateTime;

    // 便捷构造方法 - 用于永久链接
    public FileUrlResponse(String fileName, String fileUrl) {
        this.fileName = fileName;
        this.fileUrl = fileUrl;
        this.isTemporary = false;
        this.expireTime = null;
        this.generateTime = System.currentTimeMillis();
    }

    // 便捷构造方法 - 用于临时链接
    public FileUrlResponse(String fileName, String fileUrl, long expireTime) {
        this.fileName = fileName;
        this.fileUrl = fileUrl;
        this.isTemporary = true;
        this.expireTime = expireTime;
        this.generateTime = System.currentTimeMillis();
    }
}