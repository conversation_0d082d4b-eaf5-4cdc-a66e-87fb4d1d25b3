package com.kumhosunny.common.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @description: 文件详情，关联文档信息
 * @author: wye
 * @create: 2025-06-26
 **/
@Data
@NoArgsConstructor
public class FileDetailDto {
    private Long fileId;
    private Long userId;
    private String originalFileName;
    private String fileUrl;
    private String path; // OSS存储地址
    private String fileType;
    private String fileSize;
    private String status; // 处理状态
    private String statusDescription; // 状态描述
    private String errorMessage; // 错误信息
    private String uploadTime;
    private Long documentId;
    private String documentTitle;
    private String documentType;
    private LocalDateTime documentCreatedAt;

    // 状态判断方法
    private Boolean isProcessing; // 是否处理中
    private Boolean isCompleted; // 是否完成
    private Boolean isFailed; // 是否失败

    // 原有的构造函数，用于现有查询
    public FileDetailDto(Long fileId, Long userId, String originalFileName, String fileUrl, String path,
            String fileType, String fileSize, String status, String uploadTime,
            Long documentId, String documentTitle, String documentType, LocalDateTime documentCreatedAt) {
        this.fileId = fileId;
        this.userId = userId;
        this.originalFileName = originalFileName;
        this.fileUrl = fileUrl;
        this.path = path;
        this.fileType = fileType;
        this.fileSize = fileSize;
        this.status = status;
        this.uploadTime = uploadTime;
        this.documentId = documentId;
        this.documentTitle = documentTitle;
        this.documentType = documentType;
        this.documentCreatedAt = documentCreatedAt;

        // 设置默认值，后续会在服务层更新
        this.statusDescription = "";
        this.errorMessage = "";
        this.isProcessing = false;
        this.isCompleted = false;
        this.isFailed = false;
    }
}