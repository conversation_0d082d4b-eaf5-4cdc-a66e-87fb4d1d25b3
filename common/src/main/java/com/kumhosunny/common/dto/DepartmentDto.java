package com.kumhosunny.common.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 部门DTO类
 * 用于API返回数据
 * 
 * <AUTHOR>
 */
@Data
public class DepartmentDto {

    /**
     * 部门主键id
     */
    private Long departmentId;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 部门负责人id
     */
    private Long managerId;

    /**
     * 部门负责人姓名
     */
    private String managerName;

    /**
     * 部门的父级id
     */
    private Long parentId;

    /**
     * 父部门名称
     */
    private String parentName;

    /**
     * 部门排序
     */
    private Integer sort;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 子部门列表
     */
    private List<DepartmentDto> children;

    /**
     * 部门员工数量
     */
    private Long employeeCount;

    /**
     * 部门层级深度
     */
    private Integer level;

    /**
     * 部门完整路径
     */
    private String fullPath;

    /**
     * 是否为根部门
     */
    private Boolean isRoot;

    /**
     * 是否有子部门
     */
    private Boolean hasChildren;

    /**
     * 是否可以删除
     */
    private Boolean canDelete;

    // 构造函数
    public DepartmentDto() {
    }

    public DepartmentDto(Long departmentId, String departmentName) {
        this.departmentId = departmentId;
        this.departmentName = departmentName;
    }

    /**
     * 获取部门ID（兼容方法）
     */
    public Long getId() {
        return departmentId;
    }

    /**
     * 设置部门ID（兼容方法）
     */
    public void setId(Long id) {
        this.departmentId = id;
    }
} 