package com.kumhosunny.common.dto;

import com.kumhosunny.common.entity.DocumentChunk;
import lombok.Data;

import java.util.List;

/**
 * 包含上下文的搜索结果DTO
 * 
 * <AUTHOR>
 */
@Data
public class ContextualSearchResult {

    /**
     * 匹配的chunk
     */
    private DocumentChunk matchedChunk;

    /**
     * 上下文chunks（包括匹配的chunk，按chunkIndex排序）
     */
    private List<DocumentChunk> contextChunks;

    /**
     * 拼接后的完整文本内容
     */
    private String concatenatedContent;

    /**
     * 相似度分数
     */
    private Float score;

    /**
     * 文档ID
     */
    private Long documentId;

    /**
     * 文档标题
     */
    private String documentTitle;

    /**
     * 匹配chunk在上下文中的位置索引
     */
    private Integer matchedChunkPosition;

    /**
     * 向量ID
     */
    private String vectorId;

    public ContextualSearchResult() {
    }

    public ContextualSearchResult(DocumentChunk matchedChunk, List<DocumentChunk> contextChunks,
            Float score, String documentTitle) {
        this.matchedChunk = matchedChunk;
        this.contextChunks = contextChunks;
        this.score = score;
        this.documentTitle = documentTitle;

        if (matchedChunk != null) {
            this.documentId = matchedChunk.getDocumentId();
            this.vectorId = matchedChunk.getVectorId();
        }

        // 生成拼接内容和匹配位置
        generateConcatenatedContent();
        findMatchedChunkPosition();
    }

    /**
     * 生成拼接后的完整文本内容
     */
    private void generateConcatenatedContent() {
        if (contextChunks == null || contextChunks.isEmpty()) {
            this.concatenatedContent = matchedChunk != null ? matchedChunk.getContent() : "";
            return;
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < contextChunks.size(); i++) {
            if (i > 0) {
                sb.append(" "); // 用空格连接不同的chunk
            }
            sb.append(contextChunks.get(i).getContent());
        }
        this.concatenatedContent = sb.toString();
    }

    /**
     * 找到匹配chunk在上下文中的位置
     */
    private void findMatchedChunkPosition() {
        if (matchedChunk == null || contextChunks == null) {
            this.matchedChunkPosition = 0;
            return;
        }

        for (int i = 0; i < contextChunks.size(); i++) {
            if (contextChunks.get(i).getChunkIndex().equals(matchedChunk.getChunkIndex())) {
                this.matchedChunkPosition = i;
                return;
            }
        }
        this.matchedChunkPosition = 0;
    }

    /**
     * 获取匹配chunk在拼接文本中的大致位置（字符偏移）
     */
    public Integer getMatchedContentOffset() {
        if (matchedChunkPosition == null || contextChunks == null || matchedChunkPosition >= contextChunks.size()) {
            return 0;
        }

        int offset = 0;
        for (int i = 0; i < matchedChunkPosition; i++) {
            offset += contextChunks.get(i).getContent().length() + 1; // +1 for space
        }
        return offset;
    }

    /**
     * 获取匹配chunk内容的长度
     */
    public Integer getMatchedContentLength() {
        return matchedChunk != null ? matchedChunk.getContent().length() : 0;
    }

    /**
     * 获取来源（文档标题）
     */
    public String getSource() {
        return this.documentTitle;
    }

    /**
     * 获取内容（拼接后的上下文）
     */
    public String getContent() {
        return this.concatenatedContent;
    }
}