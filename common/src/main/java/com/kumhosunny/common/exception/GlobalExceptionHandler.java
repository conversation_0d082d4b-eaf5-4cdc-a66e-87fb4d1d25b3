package com.kumhosunny.common.exception;

import com.kumhosunny.common.result.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理RuntimeException异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ApiResponse<Object> handleRuntimeException(RuntimeException e) {
        log.error("系统异常: ", e);

        String message = e.getMessage();
        if ("TOKEN_EXPIRED".equals(message)) {
            return ApiResponse.tokenExpired("Token已过期，请重新登录");
        } else if ("TOKEN_INVALID".equals(message)) {
            return ApiResponse.tokenInvalid("Token无效，请重新登录");
        } else if ("UNAUTHORIZED".equals(message)) {
            return ApiResponse.unauthorized("未授权访问，请先登录");
        }

        return ApiResponse.error("系统异常: " + message);
    }

    /**
     * 处理所有其他异常
     */
    @ExceptionHandler(Exception.class)
    public ApiResponse<Object> handleException(Exception e) {
        log.error("系统异常: ", e);
        return ApiResponse.error("系统异常，请稍后重试");
    }
}