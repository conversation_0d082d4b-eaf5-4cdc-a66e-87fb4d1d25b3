package com.kumhosunny.common.util;

import java.net.InetAddress;
import java.net.UnknownHostException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;

/**
 * 启动横幅工具类
 * 用于打印漂亮的应用启动成功信息
 * 
 * <AUTHOR>
 * @since 2025-06-11
 */
public class StartupBannerUtil {

    private static final Logger log = LoggerFactory.getLogger(StartupBannerUtil.class);

    private static final String ANSI_RESET = "\u001B[0m";
    private static final String ANSI_BOLD = "\u001B[1m";
    private static final String ANSI_CYAN = "\u001B[36m";
    private static final String ANSI_GREEN = "\u001B[32m";
    private static final String ANSI_YELLOW = "\u001B[33m";
    private static final String ANSI_BLUE = "\u001B[34m";
    private static final String ANSI_PURPLE = "\u001B[35m";

    /**
     * 打印启动成功信息
     */
    public static void printStartupSuccess(Environment env) {
        String protocol = "http";
        if (env.getProperty("server.ssl.key-store") != null) {
            protocol = "https";
        }

        String serverPort = env.getProperty("server.port", "8080");
        String contextPath = env.getProperty("server.servlet.context-path", "");
        String hostAddress = "localhost";

        try {
            hostAddress = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            log.warn("无法获取主机地址: {}", e.getMessage());
        }

        String banner = buildStartupBanner(protocol, hostAddress, serverPort, contextPath, env);
        System.out.println(banner);
    }

    private static String buildStartupBanner(String protocol, String hostAddress, String serverPort,
            String contextPath, Environment env) {
        StringBuilder banner = new StringBuilder();

        banner.append("\n");
        banner.append(ANSI_GREEN).append(ANSI_BOLD).append("🎉 KumhoSunny AI 应用启动成功！").append(ANSI_RESET)
                .append("\n\n");

        banner.append(ANSI_CYAN).append("📍 访问地址:").append(ANSI_RESET).append("\n");
        banner.append("   ").append(ANSI_BLUE).append("本地: ").append(protocol).append("://localhost:")
                .append(serverPort).append(contextPath).append(ANSI_RESET).append("\n");
        banner.append("   ").append(ANSI_BLUE).append("外部: ").append(protocol).append("://").append(hostAddress)
                .append(":").append(serverPort).append(contextPath).append(ANSI_RESET).append("\n");
        banner.append("   ").append(ANSI_PURPLE).append("文档: ").append(protocol).append("://localhost:")
                .append(serverPort).append(contextPath).append("/doc.html").append(ANSI_RESET).append("\n\n");

        String activeProfiles = String.join(",", env.getActiveProfiles());
        if (activeProfiles.isEmpty()) {
            activeProfiles = "default";
        }

        String environmentDesc = getEnvironmentDescription(activeProfiles);
        banner.append(ANSI_YELLOW).append("🚀 环境: ").append(activeProfiles).append(" (").append(environmentDesc)
                .append(")").append(ANSI_RESET).append("\n");
        banner.append(ANSI_CYAN).append("⚠️  按 Ctrl+C 停止应用").append(ANSI_RESET).append("\n\n");

        return banner.toString();
    }

    /**
     * 获取环境描述信息
     */
    private static String getEnvironmentDescription(String profile) {
        switch (profile) {
            case "dev":
                return "开发环境";
            case "prod":
                return "生产环境";
            case "test":
                return "测试环境";
            default:
                return "默认环境";
        }
    }
}