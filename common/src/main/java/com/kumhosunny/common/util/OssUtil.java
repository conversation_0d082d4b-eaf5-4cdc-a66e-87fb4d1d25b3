package com.kumhosunny.common.util;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectResult;
import com.kumhosunny.common.config.AliOssConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 阿里云OSS工具类
 */
@Slf4j
@Component
public class OssUtil {

    private final OSS ossClient;
    private final AliOssConfig ossConfig;

    public OssUtil(OSS ossClient, AliOssConfig ossConfig) {
        this.ossClient = ossClient;
        this.ossConfig = ossConfig;
    }

    /**
     * 上传文件到OSS
     *
     * @param file 文件
     * @return 文件上传结果（包含文件路径和URL）
     */
    public FileUploadResult uploadFile(MultipartFile file) {
        try {
            // 生成文件名
            String fileName = generateFileName(file.getOriginalFilename());

            // 上传文件
            String fileUrl = uploadFile(file.getInputStream(), fileName, file.getContentType());

            return new FileUploadResult(fileName, fileUrl);
        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("文件上传失败", e);
        }
    }

    /**
     * 文件上传结果内部类
     */
    public static class FileUploadResult {
        private final String filePath;
        private final String fileUrl;

        public FileUploadResult(String filePath, String fileUrl) {
            this.filePath = filePath;
            this.fileUrl = fileUrl;
        }

        public String getFilePath() {
            return filePath;
        }

        public String getFileUrl() {
            return fileUrl;
        }
    }

    /**
     * 上传文件到OSS
     *
     * @param inputStream 文件流
     * @param fileName    文件名
     * @param contentType 文件类型
     * @return 文件URL
     */
    public String uploadFile(InputStream inputStream, String fileName, String contentType) {
        try {
            // 设置文件元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(contentType);

            // 构建文件路径
            String objectKey = ossConfig.getPathPrefix() + fileName;

            // 上传文件
            PutObjectResult result = ossClient.putObject(ossConfig.getBucketName(), objectKey, inputStream, metadata);

            // 返回文件URL
            String urlPrefix = ossConfig.getUrlPrefix();
            String fileUrl;
            if (urlPrefix == null || urlPrefix.trim().isEmpty()) {
                // 如果没有配置URL前缀，返回相对路径
                fileUrl = "/" + objectKey;
            } else {
                // 确保URL拼接正确
                if (urlPrefix.endsWith("/")) {
                    urlPrefix = urlPrefix.substring(0, urlPrefix.length() - 1);
                }
                fileUrl = urlPrefix + "/" + objectKey;
            }
            log.info("文件上传成功: {}", fileUrl);
            return fileUrl;
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("文件上传失败", e);
        }
    }

    /**
     * 删除文件
     *
     * @param fileName 文件名
     */
    public void deleteFile(String fileName) {
        try {
            String objectKey = ossConfig.getPathPrefix() + fileName;
            ossClient.deleteObject(ossConfig.getBucketName(), objectKey);
            log.info("文件删除成功: {}", fileName);
        } catch (Exception e) {
            log.error("文件删除失败", e);
            throw new RuntimeException("文件删除失败", e);
        }
    }

    /**
     * 生成文件名
     *
     * @param originalFilename 原始文件名
     * @return 新文件名
     */
    private String generateFileName(String originalFilename) {
        // 获取文件扩展名
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }

        // 生成唯一文件名：日期 + UUID + 扩展名
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String uuid = UUID.randomUUID().toString().replace("-", "");

        return dateStr + "/" + uuid + extension;
    }

    /**
     * 检查文件是否存在
     *
     * @param fileName 文件名
     * @return 是否存在
     */
    public boolean doesFileExist(String fileName) {
        try {
            String objectKey = ossConfig.getPathPrefix() + fileName;
            return ossClient.doesObjectExist(ossConfig.getBucketName(), objectKey);
        } catch (Exception e) {
            log.error("检查文件是否存在失败", e);
            return false;
        }
    }

    /**
     * 获取文件外链URL
     *
     * @param fileName 文件名（相对路径，例如：20231225/abc123def456.jpg）
     * @return 文件外链URL
     */
    public String getFileUrl(String fileName) {
        try {
            String urlPrefix = ossConfig.getUrlPrefix();
            String pathPrefix = ossConfig.getPathPrefix();

            // 构建完整的文件URL
            String fileUrl;
            if (urlPrefix == null || urlPrefix.trim().isEmpty()) {
                // 如果没有配置URL前缀，返回相对路径
                fileUrl = "/" + pathPrefix + fileName;
            } else {
                // 确保URL拼接正确
                if (urlPrefix.endsWith("/")) {
                    urlPrefix = urlPrefix.substring(0, urlPrefix.length() - 1);
                }
                if (!pathPrefix.startsWith("/")) {
                    pathPrefix = "/" + pathPrefix;
                }
                fileUrl = urlPrefix + pathPrefix + fileName;
            }

            log.info("获取文件外链成功: {}", fileUrl);
            return fileUrl;
        } catch (Exception e) {
            log.error("获取文件外链失败", e);
            throw new RuntimeException("获取文件外链失败", e);
        }
    }

    /**
     * 获取文件的临时访问URL（带签名）
     *
     * @param fileName      文件名
     * @param expireSeconds 过期时间（秒）
     * @return 临时访问URL
     */
    public String getPresignedUrl(String fileName, int expireSeconds) {
        try {
            String objectKey = ossConfig.getPathPrefix() + fileName;
            java.util.Date expiration = new java.util.Date(System.currentTimeMillis() + expireSeconds * 1000L);

            // 生成预签名URL
            java.net.URL url = ossClient.generatePresignedUrl(ossConfig.getBucketName(), objectKey, expiration);
            String presignedUrl = url.toString();

            log.info("获取文件临时访问URL成功: {}", presignedUrl);
            return presignedUrl;
        } catch (Exception e) {
            log.error("获取文件临时访问URL失败", e);
            throw new RuntimeException("获取文件临时访问URL失败", e);
        }
    }
}