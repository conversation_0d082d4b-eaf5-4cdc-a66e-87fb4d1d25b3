package com.kumhosunny.common.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * 用户上下文工具类
 * 用于从HttpServletRequest的token中获取当前登录用户的信息
 */
@Slf4j
@Component
public class UserContextUtil {

    private static final Logger logger = LoggerFactory.getLogger(UserContextUtil.class);

    // JWT密钥
    @Value("${app.jwt.secret}")
    private String secret;

    // 用户信息在token中的claim名
    private static final String USER_ID_KEY = "userId";
    private static final String USERNAME_KEY = "username";
    private static final String AUTHORIZATION_HEADER = "Authorization";

    /**
     * 从请求头中获取token
     *
     * @param request HTTP请求对象
     * @return token字符串，如果没有找到返回null
     */
    public String getTokenFromRequest(HttpServletRequest request) {
        String token = request.getHeader(AUTHORIZATION_HEADER);
        if (token != null && !token.trim().isEmpty()) {
            // 如果token以"Bearer "开头，则去掉前缀
            if (token.startsWith("Bearer ")) {
                return token.substring(7);
            }
            return token;
        }
        return null;
    }

    /**
     * 解析token获取Claims
     *
     * @param token JWT token
     * @return Claims对象
     * @throws RuntimeException 如果token解析失败
     */
    public Claims parseToken(String token) {
        try {
            return Jwts.parser()
                    .setSigningKey(secret)
                    .parseClaimsJws(token)
                    .getBody();
        } catch (io.jsonwebtoken.ExpiredJwtException e) {
            logger.error("Token解析失败: {}", e.getMessage());
            throw new RuntimeException("TOKEN_EXPIRED");
        } catch (Exception e) {
            logger.error("Token解析失败: {}", e.getMessage());
            throw new RuntimeException("TOKEN_INVALID");
        }
    }

    /**
     * 从请求中获取当前用户ID
     *
     * @param request HTTP请求对象
     * @return 用户ID
     * @throws RuntimeException 如果用户未登录或token无效
     */
    public Long getCurrentUserId(HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        if (token == null) {
            logger.warn("请求头中缺少Authorization token");
            throw new RuntimeException("UNAUTHORIZED");
        }

        try {
            Claims claims = parseToken(token);
            Object userId = claims.get(USER_ID_KEY);
            if (userId == null) {
                logger.warn("Token中缺少用户ID信息");
                throw new RuntimeException("TOKEN_INVALID");
            }
            try {
                return Long.valueOf(userId.toString());
            } catch (NumberFormatException e) {
                logger.error("用户ID格式转换失败");
                throw new RuntimeException("TOKEN_INVALID");
            }
        } catch (RuntimeException e) {
            // 如果是token相关的异常，直接抛出
            if ("TOKEN_EXPIRED".equals(e.getMessage()) || "TOKEN_INVALID".equals(e.getMessage())
                    || "UNAUTHORIZED".equals(e.getMessage())) {
                throw e;
            }
            // 其他异常统一处理
            logger.error("获取用户ID失败", e);
            throw new RuntimeException("TOKEN_INVALID");
        }
    }

    /**
     * 从请求中获取当前用户名
     *
     * @param request HTTP请求对象
     * @return 用户名，如果获取失败返回null
     */
    public String getCurrentUsername(HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            if (token == null) {
                return null;
            }
            Claims claims = parseToken(token);
            Object username = claims.get(USERNAME_KEY);
            return username != null ? username.toString() : null;
        } catch (Exception e) {
            logger.warn("获取用户名失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 检查当前用户是否已登录
     *
     * @param request HTTP请求对象
     * @return 如果用户已登录返回true，否则返回false
     */
    public boolean isUserLoggedIn(HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            if (token == null) {
                return false;
            }
            Claims claims = parseToken(token);
            return claims.get(USER_ID_KEY) != null;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证token是否过期
     *
     * @param token token
     * @return 是否过期
     */
    public boolean isTokenExpired(String token) {
        try {
            Claims claims = parseToken(token);
            Date expiration = claims.getExpiration();
            return expiration.before(new Date());
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 获取用户信息（包含ID和用户名）
     *
     * @param request HTTP请求对象
     * @return 用户信息对象
     */
    public UserInfo getCurrentUserInfo(HttpServletRequest request) {
        Long userId = getCurrentUserId(request);
        String username = getCurrentUsername(request);
        return new UserInfo(userId, username);
    }

    /**
     * 用户信息内部类
     */
    public static class UserInfo {
        private final Long userId;
        private final String username;

        public UserInfo(Long userId, String username) {
            this.userId = userId;
            this.username = username;
        }

        public Long getUserId() {
            return userId;
        }

        public String getUsername() {
            return username;
        }

        @Override
        public String toString() {
            return "UserInfo{" +
                    "userId=" + userId +
                    ", username='" + username + '\'' +
                    '}';
        }
    }
}