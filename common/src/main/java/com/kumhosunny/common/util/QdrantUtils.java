package com.kumhosunny.common.util;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.google.protobuf.Value;
import io.qdrant.client.QdrantClient;
import io.qdrant.client.grpc.JsonWithInt;
import io.qdrant.client.grpc.Points;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static io.qdrant.client.PointIdFactory.id;
import static io.qdrant.client.ValueFactory.value;
import static io.qdrant.client.VectorsFactory.vectors;

/**
 * @program: kum-ai-app-backend
 * @description:
 * @author: wye
 * @create: 2025-06-18 17:44
 **/
@Component
public class QdrantUtils {

    @Autowired
    private QdrantClient qdrantClient;


    private static final String API_KEY = "sk-aaabbbcccdddeeefffggghhhiiijjjkkk";
    private static final String MODEL_NAME = "bce-embedding-base_v1";
    private static final String BASE_URL = "http://**************:9997/v1";

    private static final OkHttpClient client = new OkHttpClient();

    public List<Float> embedText(List<String> tokens) {
        return embed(tokens);
    }

    public List<Float> embed(Object tokens) {
        String jsonBody = new JSONObject()
                .put("model", MODEL_NAME)
                .put("input", tokens)
                .toString();

        Request request = new Request.Builder()
                .url(BASE_URL + "/embeddings")
                .addHeader("Authorization", "Bearer " + API_KEY)
                .addHeader("Content-Type", "application/json")
                .post(RequestBody.create(jsonBody, MediaType.parse("application/json")))
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected HTTP code: " + response.code() + " - " + response.message());
            }

            String responseBody = response.body().string();
            JSONObject obj = new JSONObject(responseBody);
            JSONArray array = obj.getJSONArray("data").getJSONObject(0).getJSONArray("embedding");

            List<Float> vector = new ArrayList<>();
            for (int i = 0; i < array.size(); i++) {
                vector.add(array.getFloat(i));
            }
            return vector;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }



//    public static Points.Vectors toVectorStruct(List<Float> vector) {
//        return Points.Vectors.newBuilder()
//                .addAllData(vector)
//                .build();
//    }

    /**
     * 存入向量数据库
     * @param id
     * @param vector
     * @param text
     * @throws Exception
     */
    public void upsertToQdrant(String id, List<Float> vector, String text) throws Exception {
        Points.PointStruct point = Points.PointStruct.newBuilder()
                .setId(Points.PointId.newBuilder().setUuid(id).build())
                .setVectors(vectors(vector))
                .putAllPayload(Map.of("text", value(text)))
                .build();

        qdrantClient.upsertAsync("literature_collection", List.of(point)).get();
    }

    /**
     * 存入向量数据库（带metadata）
     * @param id
     * @param vector
     * @param text
     * @param metadata
     * @throws Exception
     */
    public void upsertToQdrant(String id, List<Float> vector, String text, Map<String, Object> metadata) throws Exception {
        Map<String, io.qdrant.client.grpc.JsonWithInt.Value> payloadMap = new HashMap<>();
        payloadMap.put("text", value(text));
        
        // 添加metadata
        if (metadata != null) {
            for (Map.Entry<String, Object> entry : metadata.entrySet()) {
                Object val = entry.getValue();
                if (val instanceof String) {
                    payloadMap.put(entry.getKey(), value((String) val));
                } else if (val instanceof Integer) {
                    payloadMap.put(entry.getKey(), value((Integer) val));
                } else if (val instanceof Long) {
                    payloadMap.put(entry.getKey(), value(((Long) val).intValue()));
                } else if (val instanceof Double) {
                    payloadMap.put(entry.getKey(), value((Double) val));
                } else {
                    payloadMap.put(entry.getKey(), value(val.toString()));
                }
            }
        }

        Points.PointStruct point = Points.PointStruct.newBuilder()
                .setId(Points.PointId.newBuilder().setUuid(id).build())
                .setVectors(vectors(vector))
                .putAllPayload(payloadMap)
                .build();

        qdrantClient.upsertAsync("literature_collection", List.of(point)).get();
    }

    /**
     * 搜索向量数据库
     * @param collectionName
     * @param vector
     * @param limit
     * @return
     * @throws Exception
     */
    public List<Points.ScoredPoint> search(String collectionName, List<Float> vector, int limit) throws Exception {
        Points.SearchPoints searchRequest = Points.SearchPoints.newBuilder()
                .setCollectionName(collectionName)
                .setLimit(limit)
                .addAllVector(vector)
                .setWithPayload(Points.WithPayloadSelector.newBuilder().setEnable(true).build())
                .build();
        
        return qdrantClient.searchAsync(searchRequest).get();
    }

//    public static void upsertToQdrant(String id, List<Float> vector, String text) throws Exception {
//        QdrantClient client = QdrantClient.newBuilder("localhost", 6334).build();
//
//        Points.PointStruct point = Points.PointStruct.newBuilder()
//                .setId(Points.PointId.newBuilder().setStringValue(id).build())
//                .setVectors(toVectorStruct(vector))
//                .putAllPayload(Map.of("text", Value.newBuilder().setStringValue(text).build()))
//                .build();
//
//        client.upsertAsync("literature_collection", List.of(point)).get();
//        client.close();
//    }

    public List<Points.ScoredPoint> searchQdrant(List<Float> vector, int limit) throws Exception {

        Points.SearchPoints searchRequest = Points.SearchPoints.newBuilder()
                .setCollectionName("literature_collection")
                .setLimit(limit)
                .addAllVector(vector)
                .setWithPayload(Points.WithPayloadSelector.newBuilder().setEnable(true).build())
                .build();
        List<Points.ScoredPoint> scoredPoints = qdrantClient.searchAsync(searchRequest).get();

        for (Points.ScoredPoint point : scoredPoints) {
            System.out.println("Score: " + point.getScore());
//            System.out.println("Text: " + point.getPayloadMap().get("text").getStringValue());
            System.out.println("ID: " + point.getId().getUuid());
            System.out.println("------");
        }

        // 要过滤掉得分低于 0.5 的结果
        double threshold = 0.5;

        List<Points.ScoredPoint> filtered = scoredPoints.stream()
                .filter(p -> p.getScore() >= threshold)
                .collect(Collectors.toList());
        return filtered;
    }
}
