package com.kumhosunny.common.util;

import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * VectorService使用示例
 * 
 * <AUTHOR> AI
 */
@Component
public class VectorServiceExample {

    private final VectorService vectorService;

    public VectorServiceExample(VectorService vectorService) {
        this.vectorService = vectorService;
    }

    /**
     * 演示如何创建集合
     */
    public CompletableFuture<Boolean> demonstrateCreateCollection() {
        return vectorService.executeAsync(
                VectorService.VectorOperations.createCollection("documents", 1536));
    }

    /**
     * 演示如何插入向量
     */
    public CompletableFuture<Boolean> demonstrateInsertVectors() {
        List<List<Float>> vectors = Arrays.asList(
                Arrays.asList(0.1f, 0.2f, 0.3f),
                Arrays.asList(0.4f, 0.5f, 0.6f),
                Arrays.asList(0.7f, 0.8f, 0.9f));

        List<String> metadata = Arrays.asList(
                "文档1的内容",
                "文档2的内容",
                "文档3的内容");

        return vectorService.executeAsync(
                VectorService.VectorOperations.insertVectors("documents", vectors, metadata));
    }

    /**
     * 演示如何搜索相似向量
     */
    public CompletableFuture<List<String>> demonstrateSearchSimilar() {
        List<Float> queryVector = Arrays.asList(0.1f, 0.2f, 0.3f);

        return vectorService.executeAsync(
                VectorService.VectorOperations.searchSimilar("documents", queryVector, 5));
    }

    /**
     * 演示同步操作
     */
    public boolean demonstrateSyncOperation() {
        return vectorService.execute(client -> {
            // 可以直接使用Qdrant客户端进行更复杂的操作
            System.out.println("执行同步操作");
            return true;
        });
    }

    /**
     * 检查向量服务状态
     */
    public boolean checkVectorServiceStatus() {
        return vectorService.isConnected();
    }
}