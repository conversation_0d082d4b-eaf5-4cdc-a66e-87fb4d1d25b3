package com.kumhosunny.common.util;

import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.AnalyzeRequest;
import org.elasticsearch.client.indices.AnalyzeResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @program: kum-ai-app-backend
 * @description:
 * @author: wye
 * @create: 2025-06-18 18:09
 **/
@Component
public class ElasticsearchUtil {

    @Autowired
    private RestHighLevelClient esClient;

    public List<String> analyzeTextWithES(String text) throws IOException {

        AnalyzeRequest request = AnalyzeRequest.withGlobalAnalyzer("ik_max_word", text);
        AnalyzeResponse response = esClient.indices().analyze(request, RequestOptions.DEFAULT);

        List<String> tokens = response.getTokens().stream()
                .map(AnalyzeResponse.AnalyzeToken::getTerm)
                .collect(Collectors.toList());
        return tokens;
    }

}
