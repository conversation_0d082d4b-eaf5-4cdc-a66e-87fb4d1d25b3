package com.kumhosunny.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 哈希工具类
 * 提供各种哈希算法的计算功能，主要用于文档内容去重
 * 
 * <AUTHOR>
 */
@Slf4j
public class HashUtils {

    private static final String SHA256_ALGORITHM = "SHA-256";
    private static final String MD5_ALGORITHM = "MD5";

    /**
     * 计算字符串的SHA-256哈希值
     * 
     * @param content 要计算哈希的内容
     * @return SHA-256哈希值的十六进制字符串
     */
    public static String sha256(String content) {
        if (!StringUtils.hasText(content)) {
            return null;
        }

        try {
            MessageDigest digest = MessageDigest.getInstance(SHA256_ALGORITHM);
            byte[] hashBytes = digest.digest(content.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            log.error("SHA-256算法不可用", e);
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }

    /**
     * 计算字符串的MD5哈希值
     * 
     * @param content 要计算哈希的内容
     * @return MD5哈希值的十六进制字符串
     */
    public static String md5(String content) {
        if (!StringUtils.hasText(content)) {
            return null;
        }

        try {
            MessageDigest digest = MessageDigest.getInstance(MD5_ALGORITHM);
            byte[] hashBytes = digest.digest(content.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5算法不可用", e);
            throw new RuntimeException("MD5算法不可用", e);
        }
    }

    /**
     * 计算文档内容的标准化哈希值
     * 会先对内容进行标准化处理（去除多余空白字符、统一换行符等），再计算SHA-256
     * 
     * @param content 文档内容
     * @return 标准化后的SHA-256哈希值
     */
    public static String normalizedContentHash(String content) {
        if (!StringUtils.hasText(content)) {
            return null;
        }

        // 标准化内容：
        // 1. 去除首尾空白
        // 2. 统一换行符为\n
        // 3. 将多个连续空白字符替换为单个空格
        // 4. 去除空行
        String normalized = content.trim()
                .replaceAll("\\r\\n", "\n")  // 统一换行符
                .replaceAll("\\r", "\n")    // 统一换行符
                .replaceAll("[ \\t]+", " ")  // 多个空白字符替换为单个空格
                .replaceAll("\\n\\s*\\n", "\n")  // 去除空行
                .trim();

        return sha256(normalized);
    }

    /**
     * 检查两个内容是否相同（基于哈希值比较）
     * 
     * @param content1 内容1
     * @param content2 内容2
     * @return 如果内容相同返回true，否则返回false
     */
    public static boolean isContentSame(String content1, String content2) {
        String hash1 = normalizedContentHash(content1);
        String hash2 = normalizedContentHash(content2);
        
        if (hash1 == null && hash2 == null) {
            return true;
        }
        
        return hash1 != null && hash1.equals(hash2);
    }

    /**
     * 将字节数组转换为十六进制字符串
     * 
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * 生成内容的短哈希（前8位），用于快速比较
     * 
     * @param content 内容
     * @return 短哈希字符串
     */
    public static String shortHash(String content) {
        String fullHash = sha256(content);
        return fullHash != null && fullHash.length() >= 8 ? fullHash.substring(0, 8) : null;
    }

    /**
     * 验证哈希值格式是否正确
     * 
     * @param hash 哈希值
     * @param algorithm 算法类型（sha256或md5）
     * @return 如果格式正确返回true
     */
    public static boolean isValidHash(String hash, String algorithm) {
        if (!StringUtils.hasText(hash)) {
            return false;
        }

        // 检查是否只包含十六进制字符
        if (!hash.matches("^[a-fA-F0-9]+$")) {
            return false;
        }

        // 检查长度
        switch (algorithm.toLowerCase()) {
            case "sha256":
                return hash.length() == 64;  // SHA-256 = 256位 = 64个十六进制字符
            case "md5":
                return hash.length() == 32;  // MD5 = 128位 = 32个十六进制字符
            default:
                return false;
        }
    }
} 