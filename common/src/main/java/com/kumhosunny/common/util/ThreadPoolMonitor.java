package com.kumhosunny.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池监控工具类
 * 定期监控线程池的运行状态，提供性能指标
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ThreadPoolMonitor {

    @Autowired
    @Qualifier("vectorizationExecutor")
    private Executor vectorizationExecutor;

    /**
     * 定期监控向量化线程池状态（每30秒执行一次）
     */
    @Scheduled(fixedRate = 30000)
    public void monitorVectorizationThreadPool() {
        if (vectorizationExecutor instanceof ThreadPoolTaskExecutor) {
            ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) vectorizationExecutor;
            ThreadPoolExecutor threadPoolExecutor = executor.getThreadPoolExecutor();
            
            if (threadPoolExecutor != null) {
                int activeCount = threadPoolExecutor.getActiveCount();
                int poolSize = threadPoolExecutor.getPoolSize();
                int corePoolSize = threadPoolExecutor.getCorePoolSize();
                int maximumPoolSize = threadPoolExecutor.getMaximumPoolSize();
                long taskCount = threadPoolExecutor.getTaskCount();
                long completedTaskCount = threadPoolExecutor.getCompletedTaskCount();
                int queueSize = threadPoolExecutor.getQueue().size();
                
                // 只有在有活动任务时才记录日志，避免日志过多
                if (activeCount > 0 || queueSize > 0) {
                    log.info("向量化线程池状态 - 活跃线程: {}/{}, 核心/最大线程: {}/{}, " +
                            "任务总数: {}, 已完成: {}, 队列大小: {}", 
                            activeCount, poolSize, corePoolSize, maximumPoolSize,
                            taskCount, completedTaskCount, queueSize);
                }
                
                // 警告：如果队列过满
                if (queueSize > maximumPoolSize * 2) {
                    log.warn("向量化线程池队列过满！当前队列大小: {}, 建议调整线程池配置", queueSize);
                }
                
                // 警告：如果线程池饱和
                if (activeCount == maximumPoolSize && queueSize > 0) {
                    log.warn("向量化线程池已饱和！活跃线程: {}, 最大线程: {}, 队列大小: {}", 
                            activeCount, maximumPoolSize, queueSize);
                }
            }
        }
    }

    /**
     * 获取向量化线程池统计信息
     */
    public ThreadPoolStats getVectorizationThreadPoolStats() {
        if (vectorizationExecutor instanceof ThreadPoolTaskExecutor) {
            ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) vectorizationExecutor;
            ThreadPoolExecutor threadPoolExecutor = executor.getThreadPoolExecutor();
            
            if (threadPoolExecutor != null) {
                return ThreadPoolStats.builder()
                        .activeCount(threadPoolExecutor.getActiveCount())
                        .poolSize(threadPoolExecutor.getPoolSize())
                        .corePoolSize(threadPoolExecutor.getCorePoolSize())
                        .maximumPoolSize(threadPoolExecutor.getMaximumPoolSize())
                        .taskCount(threadPoolExecutor.getTaskCount())
                        .completedTaskCount(threadPoolExecutor.getCompletedTaskCount())
                        .queueSize(threadPoolExecutor.getQueue().size())
                        .build();
            }
        }
        return ThreadPoolStats.builder().build();
    }

    /**
     * 线程池统计信息DTO
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class ThreadPoolStats {
        private int activeCount;
        private int poolSize;
        private int corePoolSize;
        private int maximumPoolSize;
        private long taskCount;
        private long completedTaskCount;
        private int queueSize;
        
        /**
         * 计算线程池使用率
         */
        public double getPoolUtilization() {
            return maximumPoolSize == 0 ? 0.0 : (double) activeCount / maximumPoolSize;
        }
        
        /**
         * 计算任务完成率
         */
        public double getTaskCompletionRate() {
            return taskCount == 0 ? 0.0 : (double) completedTaskCount / taskCount;
        }
        
        /**
         * 计算队列使用率（相对于最大线程数）
         */
        public double getQueueUtilization() {
            return maximumPoolSize == 0 ? 0.0 : (double) queueSize / (maximumPoolSize * 2);
        }
    }
} 