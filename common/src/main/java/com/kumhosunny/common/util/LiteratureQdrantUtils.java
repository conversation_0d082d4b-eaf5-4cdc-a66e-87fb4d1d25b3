package com.kumhosunny.common.util;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.kumhosunny.common.dto.ContextualSearchResult;
import com.kumhosunny.common.entity.*;
import com.kumhosunny.common.repository.AiFilesRepository;
import com.kumhosunny.common.repository.DocumentChunkRepository;
import com.kumhosunny.common.repository.DocumentRepository;
import com.kumhosunny.common.repository.EmployeeRepository;
import com.kumhosunny.common.service.DepartmentService;
import io.qdrant.client.QdrantClient;
import io.qdrant.client.ConditionFactory;
import io.qdrant.client.grpc.JsonWithInt;
import io.qdrant.client.grpc.Points;
import io.qdrant.client.grpc.Points.PointStruct;
import io.qdrant.client.grpc.Points.UpsertPoints;
import io.qdrant.client.grpc.Points.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static io.qdrant.client.ValueFactory.value;
import static io.qdrant.client.VectorsFactory.vectors;

/**
 * 文学文档向量化服务
 * 支持文档分段、向量化存储和语义搜索
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class LiteratureQdrantUtils {

    private static final int DEFAULT_CHUNK_SIZE = 1000;
    private static final int DEFAULT_SEARCH_LIMIT = 10;
    private static final int EMBEDDING_BATCH_SIZE = 32;

    // 添加常量定义
    private static final String LEGAL_TYPE = "legal";
    private static final String COMPANY_TYPE = "company";
    private static final String USER_ID_FIELD = "user_id";
    private static final String DEPARTMENT_ID_FIELD = "departmentId";
    private static final String TYPE_FIELD = "type";

    @Value("${app.embedding.base-url:http://localhost:9997/v1}")
    private String embeddingBaseUrl;

    @Value("${app.embedding.api-key:sk-xxx}")
    private String embeddingApiKey;

    @Value("${app.embedding.model:bce-embedding-base_v1}")
    private String embeddingModel;

    @Value("${app.embedding.dimension:1024}")
    private int embeddingDimension;

    @Autowired
    private QdrantClient qdrantClient;

    @Autowired
    private AiFilesRepository aiFilesRepository;

    @Autowired
    private ElasticsearchUtil elasticsearchUtil;

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private DocumentChunkRepository documentChunkRepository;

    @Autowired
    private Executor vectorizationExecutor;

    @Autowired
    private DepartmentService departmentService;

    private final OkHttpClient httpClient = new OkHttpClient();

    /**
     * 插入文学文档到向量数据库
     * 
     * @param content        文档内容
     * @param collectionName 集合名称
     * @return 插入的段落数量
     */
    public int insertLiterature(String content, String collectionName, AiFiles aiFiles, Document.DocumentType type)
            throws IOException, ExecutionException, InterruptedException {
        String contentHash = HashUtils.normalizedContentHash(content);
        String title = aiFiles.getOriginalFileName();
        log.info("开始插入文学文档：{}, 内容长度：{}", title, content.length());
        EmployeeEntity employeeEntity = employeeRepository.findById(Long.valueOf(aiFiles.getUserId())).get();
        Document document = new Document();
        document.setContent(content);
        document.setTitle(title);
        document.setContentHash(contentHash);
        document.setFileId(aiFiles.getId());
        document.setOwnerId(Long.valueOf(aiFiles.getUserId()));
        document.setDepartmentId(employeeEntity.getDepartmentId() == null ? -1 : employeeEntity.getDepartmentId());
        document.setType(type);
        document.setPath(aiFiles.getPath());
        documentRepository.save(document);

        JSONArray elements = JSONUtil.parseArray(content);
        List<JSONObject> chunks = new ArrayList<>();
        for (Object element : elements) {
            if (element instanceof JSONObject) {
                chunks.add((JSONObject) element);
            }
        }

        // 使用多线程处理向量化
        log.info("开始多线程向量化处理，总分片数：{}, 批处理大小：{}", chunks.size(), EMBEDDING_BATCH_SIZE);

        // 线程安全的集合
        List<PointStruct> pointStructs = Collections.synchronizedList(new ArrayList<>());
        List<DocumentChunk> documentChunks = Collections.synchronizedList(new ArrayList<>());

        // 使用CompletableFuture并行处理
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        AtomicInteger processedCount = new AtomicInteger(0);

        for (int i = 0; i < chunks.size(); i += EMBEDDING_BATCH_SIZE) {
            int end = Math.min(i + EMBEDDING_BATCH_SIZE, chunks.size());
            List<JSONObject> batch = chunks.subList(i, end);
            final int batchIndex = i / EMBEDDING_BATCH_SIZE;
            final int batchStartIndex = i;

            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    log.info("开始处理批次 {}，包含 {} 个分片", batchIndex, batch.size());
                    List<String> textsToEmbed = batch.stream()
                            .map(chunk -> chunk.getStr("text"))
                            .collect(Collectors.toList());

                    List<List<Float>> embeddings = embedTexts(textsToEmbed);

                    if (embeddings.size() != batch.size()) {
                        String errorMsg = String.format("嵌入数量与文本数量不匹配。批次: %d, 预期: %d, 实际: %d",
                                batchIndex, batch.size(), embeddings.size());
                        log.error(errorMsg);
                        throw new RuntimeException(errorMsg);
                    }

                    for (int j = 0; j < batch.size(); j++) {
                        JSONObject chunkObject = batch.get(j);
                        List<Float> vector = embeddings.get(j);

                        String text = chunkObject.getStr("text");
                        int chunkIndexInDoc = batchStartIndex + j; // 使用自己生成的、可靠的索引
                        String vectorId = UUID.randomUUID().toString();

                        Map<String, Object> metadata = new HashMap<>();
                        if (chunkObject.get("metadata") instanceof JSONObject) {
                            metadata = ((JSONObject) chunkObject.get("metadata")).getRaw();
                        }

                        Map<String, JsonWithInt.Value> payload = buildPayload(
                                title, text, null, chunkIndexInDoc, aiFiles, document.getDepartmentId(),
                                type.toString(), metadata);

                        PointStruct point = PointStruct.newBuilder()
                                .setId(Points.PointId.newBuilder().setUuid(vectorId).build())
                                .setVectors(vectors(vector))
                                .putAllPayload(payload)
                                .build();
                        pointStructs.add(point);

                        DocumentChunk docChunk = new DocumentChunk();
                        docChunk.setDocumentId(document.getId());
                        docChunk.setVectorId(vectorId);
                        docChunk.setChunkIndex(chunkIndexInDoc);
                        docChunk.setContent(text);
                        docChunk.setCreatedAt(LocalDateTime.now());
                        documentChunks.add(docChunk);
                    }
                    int processed = processedCount.addAndGet(batch.size());
                    log.info("完成处理批次 {}, 总计已处理 {}/{}", batchIndex, processed, chunks.size());

                } catch (Exception e) {
                    log.error("处理批次 {} 时发生错误: {}", batchIndex, e.getMessage(), e);
                    throw new RuntimeException("批处理失败", e);
                }
            }, vectorizationExecutor);

            futures.add(future);
        }

        // 等待所有任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
            log.info("所有分片向量化处理完成，总数：{}", chunks.size());
        } catch (Exception e) {
            log.error("向量化处理过程中发生错误", e);
            throw new RuntimeException("向量化处理失败", e);
        }

        documentChunkRepository.saveAll(documentChunks);

        // 批量写入 Qdrant
        if (!pointStructs.isEmpty()) {
            log.info("开始向Qdrant批量写入 {} 个点", pointStructs.size());
            UpdateResult result = qdrantClient.upsertAsync(
                    UpsertPoints.newBuilder()
                            .setCollectionName(collectionName)
                            .addAllPoints(pointStructs)
                            .build())
                    .get();

            log.info("成功插入文献段落数：{}，操作ID：{}", pointStructs.size(), result.getOperationId());
        } else {
            log.info("没有有效的文献段落可插入");
        }

        // 更新文件记录的向量信息
        if (aiFiles.getId() != null) {
            aiFiles.setVectorChunks(String.valueOf(chunks.size()));
        }

        return pointStructs.size();
    }

    /**
     * 语义搜索文档
     * 
     * @param query          查询文本
     * @param collectionName 集合名称
     * @param topK           返回结果数量
     * @param userId         用户ID
     * @param type           文档类型
     * @return 搜索结果
     */
    public List<Points.ScoredPoint> searchLiterature(String query, String collectionName, int topK, Long userId,
            String type) throws IOException, ExecutionException, InterruptedException {
        
        log.info("开始语义搜索：query={}, topK={}, type={}, userId={}", query, topK, type, userId);

        // 参数验证
        if (query == null || query.trim().isEmpty()) {
            log.warn("搜索查询为空，返回空结果");
            return new ArrayList<>();
        }

        // 获取查询向量
        List<Float> vector = embedText(query);

        // 构建过滤条件
        Points.Filter filter = buildSearchFilter(type, userId);

        // 构建搜索请求
        Points.SearchPoints.Builder searchRequestBuilder = Points.SearchPoints.newBuilder()
                .setCollectionName(collectionName)
                .setLimit(topK)
                .addAllVector(vector)
                .setWithPayload(Points.WithPayloadSelector.newBuilder().setEnable(true).build());

        // 添加过滤条件
        if (filter != null) {
            searchRequestBuilder.setFilter(filter);
        }

        // 执行搜索
        List<Points.ScoredPoint> results = qdrantClient.searchAsync(searchRequestBuilder.build()).get();

        log.info("搜索完成，返回结果数：{}", results.size());
        return results;
    }

    /**
     * 构建搜索过滤条件
     * 
     * @param type   文档类型
     * @param userId 用户ID
     * @return 过滤条件
     */
    private Points.Filter buildSearchFilter(String type, Long userId) {
        if (LEGAL_TYPE.equals(type)) {
            return buildLegalFilter();
        } else {
            return buildRegularFilter(userId);
        }
    }

    /**
     * 构建法律知识库过滤条件
     * 
     * @return 过滤条件
     */
    private Points.Filter buildLegalFilter() {
        Points.Condition legalCondition = ConditionFactory.matchKeyword(TYPE_FIELD, LEGAL_TYPE);
        return Points.Filter.newBuilder()
                .addMust(legalCondition)
                .build();
    }

    /**
     * 构建普通文档过滤条件
     * 支持用户文档、部门文档和公司文档的权限控制
     * 
     * @param userId 用户ID
     * @return 过滤条件
     */
    private Points.Filter buildRegularFilter(Long userId) {
        try {
            // 获取用户信息
            EmployeeEntity employeeEntity = employeeRepository.findById(userId)
                    .orElseThrow(() -> new IllegalArgumentException("用户不存在：" + userId));

            // 根据用户部门ID查询顶级父部门及其所有子部门ID列表
            List<Long> departmentIds = departmentService.getTopParentAndAllChildrenIds(employeeEntity.getDepartmentId());
            
            log.info("用户{}的部门体系IDs（顶级父部门及所有子部门）: {}", userId, departmentIds);

            // 构建OR条件：user_id = userId OR department_id in (departmentIds) OR type = "company"
            Points.Filter.Builder orFilterBuilder = Points.Filter.newBuilder();

            // 条件1: user_id = userId
            orFilterBuilder.addShould(ConditionFactory.match(USER_ID_FIELD, userId));

            // 条件2: department_id in (departmentIds)
            if (!departmentIds.isEmpty()) {
                Points.Filter.Builder departmentFilterBuilder = Points.Filter.newBuilder();
                for (Long deptId : departmentIds) {
                    departmentFilterBuilder.addShould(ConditionFactory.match(DEPARTMENT_ID_FIELD, deptId));
                }
                orFilterBuilder.addShould(
                        Points.Condition.newBuilder()
                                .setFilter(departmentFilterBuilder.build())
                                .build()
                );
            }

            // 条件3: type = "company"
            orFilterBuilder.addShould(ConditionFactory.matchKeyword(TYPE_FIELD, COMPANY_TYPE));

            return Points.Filter.newBuilder()
                    .addMust(Points.Condition.newBuilder().setFilter(orFilterBuilder.build()).build())
                    .build();

        } catch (Exception e) {
            log.error("构建搜索过滤条件失败，userId: {}, error: {}", userId, e.getMessage(), e);
            // 返回只允许访问用户自己文档的过滤条件作为降级方案
            return Points.Filter.newBuilder()
                    .addMust(ConditionFactory.match(USER_ID_FIELD, userId))
                    .build();
        }
    }

    /**
     * 搜索文档（使用默认参数）
     */
    // public List<Points.ScoredPoint> searchLiterature(String query, String
    // collectionName)
    // throws IOException, ExecutionException, InterruptedException {
    // return searchLiterature(query, collectionName, DEFAULT_SEARCH_LIMIT);
    // }

    /**
     * 混合搜索：结合ES分词和向量搜索
     * 
     * @param query          查询文本
     * @param collectionName 集合名称
     * @param vectorWeight   向量搜索权重 (0.0-1.0)
     * @param topK           返回结果数量
     * @param userId         用户ID
     * @param type           文档类型
     * @return 搜索结果
     */
    public List<Points.ScoredPoint> hybridSearch(String query, String collectionName,
            double vectorWeight, int topK, Long userId, String type)
            throws IOException, ExecutionException, InterruptedException {

        log.info("开始混合搜索：{}, 向量权重：{}, type：{}, userId：{}", query, vectorWeight, type, userId);

        // 1. 向量搜索
        List<Points.ScoredPoint> vectorResults = searchLiterature(query, collectionName, topK * 2, userId, type);

        // 2. 分词搜索（在payload中查找匹配的tokens）
        List<String> queryTokens = elasticsearchUtil.analyzeTextWithES(query);

        // 3. 重新评分：结合向量相似度和分词匹配
        List<Points.ScoredPoint> rerankedResults = vectorResults.stream()
                .map(point -> reScoreWithTokens(point, queryTokens, vectorWeight))
                .filter(point -> point.getScore() > 0.1f) // 降低过滤阈值，只返回分数大于0.1的结果
                .sorted((a, b) -> Float.compare(b.getScore(), a.getScore()))
                .limit(topK)
                .collect(Collectors.toList());

        log.info("混合搜索完成，返回结果数：{}", rerankedResults.size());

        return rerankedResults;
    }



    /**
     * 带上下文的语义搜索
     * 
     * @param query          查询文本
     * @param collectionName 集合名称
     * @param topK           返回结果数量
     * @param userId         用户ID
     * @param type           文档类型
     * @param contextSize    上下文大小（前后各contextSize个chunk）
     * @return 包含上下文的搜索结果
     */
    public List<ContextualSearchResult> searchWithContext(String query, String collectionName,
            int topK, Long userId, String type,
            int contextSize)
            throws IOException, ExecutionException, InterruptedException {

        log.info("开始带上下文的语义搜索：{}, topK：{}, type：{}, userId：{}, contextSize：{}",
                query, topK, type, userId, contextSize);

        // 1. 先进行普通的向量搜索
        List<Points.ScoredPoint> vectorResults = searchLiterature(query, collectionName, topK, userId, type);

        // 2. 为每个搜索结果获取上下文
        List<ContextualSearchResult> contextualResults = new ArrayList<>();

        for (Points.ScoredPoint point : vectorResults) {
            try {
                // 从payload中获取信息
                String title = point.getPayload().get("title").getStringValue();
                String vectorId = point.getId().getUuid();

                // 根据vectorId查找对应的DocumentChunk
                Optional<DocumentChunk> chunkOpt = documentChunkRepository.findByVectorId(vectorId);
                if (!chunkOpt.isPresent()) {
                    log.warn("未找到vectorId对应的DocumentChunk: {}", vectorId);
                    continue;
                }

                DocumentChunk matchedChunk = chunkOpt.get();

                // 获取上下文chunks
                List<DocumentChunk> contextChunks = getContextChunks(
                        matchedChunk.getDocumentId(),
                        matchedChunk.getChunkIndex(),
                        contextSize);

                // 创建上下文搜索结果
                ContextualSearchResult result = new ContextualSearchResult(
                        matchedChunk,
                        contextChunks,
                        point.getScore(),
                        title);

                contextualResults.add(result);

            } catch (Exception e) {
                log.error("处理搜索结果时发生错误: {}", e.getMessage(), e);
            }
        }

        log.info("带上下文搜索完成，返回结果数：{}", contextualResults.size());
        return contextualResults;
    }

    /**
     * 带上下文的混合搜索
     * 
     * @param query          查询文本
     * @param collectionName 集合名称
     * @param vectorWeight   向量搜索权重 (0.0-1.0)
     * @param topK           返回结果数量
     * @param userId         用户ID
     * @param type           文档类型
     * @param contextSize    上下文大小（前后各contextSize个chunk）
     * @return 包含上下文的搜索结果
     */
    public List<ContextualSearchResult> hybridSearchWithContext(String query, String collectionName,
            double vectorWeight, int topK,
            Long userId, String type,
            int contextSize)
            throws IOException, ExecutionException, InterruptedException {

        log.info("开始带上下文的混合搜索：{}, 向量权重：{}, type：{}, userId：{}, contextSize：{}",
                query, vectorWeight, type, userId, contextSize);

        // 1. 先进行混合搜索
        List<Points.ScoredPoint> hybridResults = hybridSearch(query, collectionName, vectorWeight, topK * 2, userId,
                type);

        // 2. 为每个搜索结果获取上下文
        List<ContextualSearchResult> contextualResults = new ArrayList<>();

        for (Points.ScoredPoint point : hybridResults) {
            try {
                // 从payload中获取信息
                String title = point.getPayload().get("title").getStringValue();
                String vectorId = point.getId().getUuid();

                // 根据vectorId查找对应的DocumentChunk
                Optional<DocumentChunk> chunkOpt = documentChunkRepository.findByVectorId(vectorId);
                if (!chunkOpt.isPresent()) {
                    log.warn("未找到vectorId对应的DocumentChunk: {}", vectorId);
                    continue;
                }

                DocumentChunk matchedChunk = chunkOpt.get();

                // 获取上下文chunks
                List<DocumentChunk> contextChunks = getContextChunks(
                        matchedChunk.getDocumentId(),
                        matchedChunk.getChunkIndex(),
                        contextSize);

                // 创建上下文搜索结果
                ContextualSearchResult result = new ContextualSearchResult(
                        matchedChunk,
                        contextChunks,
                        point.getScore(),
                        title);

                contextualResults.add(result);

                // 限制结果数量
                if (contextualResults.size() >= topK) {
                    break;
                }

            } catch (Exception e) {
                log.error("处理混合搜索结果时发生错误: {}", e.getMessage(), e);
            }
        }

        log.info("带上下文混合搜索完成，返回结果数：{}", contextualResults.size());
        return contextualResults;
    }

    /**
     * 获取指定chunk的上下文chunks
     * 
     * @param documentId  文档ID
     * @param chunkIndex  当前chunk索引
     * @param contextSize 上下文大小（前后各contextSize个chunk）
     * @return 上下文chunks列表（包括当前chunk，按chunkIndex排序）
     */
    private List<DocumentChunk> getContextChunks(Long documentId, Integer chunkIndex, int contextSize) {
        int startIndex = Math.max(0, chunkIndex - contextSize);
        int endIndex = chunkIndex + contextSize;

        return documentChunkRepository.findContextChunks(documentId, startIndex, endIndex);
    }

    /**
     * 根据向量ID直接获取上下文chunks（便捷方法）
     * 
     * @param vectorId    向量ID
     * @param contextSize 上下文大小（前后各contextSize个chunk）
     * @return 上下文chunks列表（包括当前chunk，按chunkIndex排序）
     */
    public List<DocumentChunk> getContextChunksByVectorId(String vectorId, int contextSize) {
        return documentChunkRepository.findContextChunksByVectorId(vectorId, contextSize);
    }

    /**
     * 将长文本分段
     */
    public List<String> splitIntoChunks(String text, int chunkSize) {
        if (text == null || text.isEmpty() || chunkSize <= 0) {
            return new ArrayList<>();
        }
        List<String> chunks = new ArrayList<>();
        for (int i = 0; i < text.length(); i += chunkSize) {
            chunks.add(text.substring(i, Math.min(text.length(), i + chunkSize)));
        }
        return chunks;
    }

    /**
     * @deprecated Use {@link #embedTexts(List)} instead for better performance.
     */
    @Deprecated
    public List<Float> embedText(String text) throws IOException {
        JSONObject payload = new JSONObject();
        payload.set("model", embeddingModel);
        payload.set("input", text);

        RequestBody body = RequestBody.create(
                payload.toString(),
                MediaType.get("application/json; charset=utf-8"));

        Request request = new Request.Builder()
                .url(embeddingBaseUrl + "/embeddings")
                .header("Authorization", "Bearer " + embeddingApiKey)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response + " " + response.body().string());
            }

            JSONObject jsonResponse = JSONUtil.parseObj(response.body().string());
            JSONArray data = jsonResponse.getJSONArray("data");
            JSONObject firstEmbedding = data.getJSONObject(0);
            JSONArray embeddingArray = firstEmbedding.getJSONArray("embedding");

            List<Float> vector = embeddingArray.toList(Float.class);
            log.debug("文本向量化完成，维度：{}", vector.size());
            return vector;
        }
    }

    public List<List<Float>> embedTexts(List<String> texts) throws IOException {
        JSONObject payload = new JSONObject();
        payload.set("model", embeddingModel);
        payload.set("input", texts);

        RequestBody body = RequestBody.create(
                payload.toString(),
                MediaType.get("application/json; charset=utf-8"));

        Request request = new Request.Builder()
                .url(embeddingBaseUrl + "/embeddings")
                .header("Authorization", "Bearer " + embeddingApiKey)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response + " " + response.body().string());
            }

            JSONObject jsonResponse = JSONUtil.parseObj(response.body().string());
            JSONArray data = jsonResponse.getJSONArray("data");

            List<List<Float>> embeddings = new ArrayList<>();
            for (Object item : data) {
                JSONObject embeddingObj = (JSONObject) item;
                JSONArray embeddingArray = embeddingObj.getJSONArray("embedding");
                List<Float> vector = embeddingArray.toList(Float.class);
                embeddings.add(vector);
            }
            log.debug("文本向量化完成，批处理大小：{}, 维度：{}", texts.size(), embeddings.isEmpty() ? 0 : embeddings.get(0).size());
            return embeddings;
        }
    }

    /**
     * 构建Qdrant点的payload
     */
    private Map<String, JsonWithInt.Value> buildPayload(String title, String text, List<String> tokens,
            int chunkIndex, AiFiles aiFiles, Long departmentId, String type, Map<String, Object> metadata) {
        Map<String, JsonWithInt.Value> payload = new HashMap<>();

        payload.put("title", value(title));
        payload.put("text", value(text));
        payload.put("timestamp", value(LocalDateTime.now().toString()));
        payload.put("chunk_index", value(chunkIndex));
        payload.put("file_id", value(aiFiles.getId()));
        payload.put("user_id", value(Long.valueOf(aiFiles.getUserId())));
        if (departmentId != null) {
            payload.put("departmentId", value(departmentId));
        }
        payload.put("type", value(type));

        if (tokens != null && !tokens.isEmpty()) {
            payload.put("tokens",
                    value(tokens.stream().map(io.qdrant.client.ValueFactory::value).collect(Collectors.toList())));
        }

        if (metadata != null) {
            for (Map.Entry<String, Object> entry : metadata.entrySet()) {
                String key = entry.getKey();
                Object val = entry.getValue();

                // 避免覆盖核心字段
                if (payload.containsKey(key)) {
                    continue;
                }

                if (val instanceof String) {
                    payload.put(key, value((String) val));
                } else if (val instanceof Integer) {
                    payload.put(key, value((long) (Integer) val));
                } else if (val instanceof Long) {
                    payload.put(key, value((Long) val));
                } else if (val instanceof Double) {
                    payload.put(key, value((Double) val));
                } else if (val instanceof Boolean) {
                    payload.put(key, value((Boolean) val));
                } else if (val != null) {
                    payload.put(key, value(JSONUtil.toJsonStr(val)));
                }
            }
        }

        return payload;
    }

    private Points.ScoredPoint reScoreWithTokens(Points.ScoredPoint point, List<String> queryTokens,
            double vectorWeight) {
        Map<String, JsonWithInt.Value> payload = point.getPayloadMap();
        String text = payload.getOrDefault("text", value("")).getStringValue();
        String tokensJson = payload.getOrDefault("tokens", value("[]")).getStringValue();

        // Jaccard 相似度计算
        List<String> docTokens = new ArrayList<>();
        if (tokensJson != null && !tokensJson.trim().isEmpty() && tokensJson.trim().startsWith("[")) {
            try {
                docTokens = JSONUtil.toList(tokensJson, String.class);
            } catch (Exception e) {
                log.warn("Failed to parse 'tokens' payload as JSON array, treating as empty. Payload: '{}', Error: {}",
                        tokensJson, e.getMessage());
            }
        }
        Set<String> queryTokenSet = new HashSet<>(queryTokens);
        Set<String> docTokenSet = new HashSet<>(docTokens);

        // 如果没有文档token，则只依赖向量得分
        if (docTokenSet.isEmpty()) {
            return point.toBuilder().setScore((float) (point.getScore() * vectorWeight)).build();
        }

        // 计算Jaccard相似度
        Set<String> intersection = new HashSet<>(queryTokenSet);
        intersection.retainAll(docTokenSet);
        Set<String> union = new HashSet<>(queryTokenSet);
        union.addAll(docTokenSet);
        double jaccardScore = (double) intersection.size() / union.size();

        // 综合得分
        float finalScore = (float) (point.getScore() * vectorWeight + jaccardScore * (1.0 - vectorWeight));

        return point.toBuilder().setScore(finalScore).build();
    }

    public List<Points.ScoredPoint> searchLiteratureByFile(String query, String collectionName, int topK, Long fileId)
            throws IOException, ExecutionException, InterruptedException {

        log.info("开始在文件内进行语义搜索：{}, topK：{}, fileId：{}", query, topK, fileId);

        List<Float> vector = embedText(query);

        // 构建过滤条件
        Points.Filter.Builder filterBuilder = Points.Filter.newBuilder();

        // 过滤 file_id
        if (fileId != null) {
            filterBuilder.addMust(ConditionFactory.match("file_id", fileId));
        }

        Points.SearchPoints.Builder searchRequestBuilder = Points.SearchPoints.newBuilder()
                .setCollectionName(collectionName)
                .setLimit(topK)
                .addAllVector(vector)
                .setWithPayload(Points.WithPayloadSelector.newBuilder().setEnable(true).build());

        if (filterBuilder.getMustCount() > 0 || filterBuilder.getShouldCount() > 0
                || filterBuilder.getMustNotCount() > 0) {
            searchRequestBuilder.setFilter(filterBuilder.build());
        }

        List<Points.ScoredPoint> result = qdrantClient.searchAsync(searchRequestBuilder.build()).get();

        log.info("文件内语义搜索完成，找到结果数：{}", result.size());

        return result;
    }

    /**
     * 根据文档ID删除向量库中的数据
     * 
     * @param documentId     文档ID
     * @param collectionName 集合名称
     * @return 删除的向量数量
     */
    public int deleteVectorsByDocumentId(Long documentId, String collectionName)
            throws ExecutionException, InterruptedException {

        log.info("开始删除文档ID为{}的向量数据", documentId);

        // 1. 根据文档ID获取所有相关的DocumentChunk
        List<DocumentChunk> documentChunks = documentChunkRepository.findByDocumentIdOrderByChunkIndexAsc(documentId);

        if (documentChunks.isEmpty()) {
            log.info("文档ID{}没有找到相关的向量数据", documentId);
            return 0;
        }

        // 2. 收集所有的向量ID
        List<String> vectorIds = documentChunks.stream()
                .map(DocumentChunk::getVectorId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (vectorIds.isEmpty()) {
            log.info("文档ID{}没有找到有效的向量ID", documentId);
            return 0;
        }

        // 3. 批量删除向量数据 - 使用过滤器删除
        try {
            // 使用过滤器删除所有document_id匹配的向量
            Points.Filter filter = Points.Filter.newBuilder()
                    .addMust(ConditionFactory.match("document_id", documentId))
                    .build();

            UpdateResult result = qdrantClient.deleteAsync(collectionName, filter).get();

            log.info("成功删除文档ID{}的向量数据，删除数量：{}，操作ID：{}",
                    documentId, vectorIds.size(), result.getOperationId());

            return vectorIds.size();
        } catch (Exception e) {
            log.error("删除文档ID{}的向量数据时发生错误: {}", documentId, e.getMessage(), e);
            throw new RuntimeException("删除向量数据失败", e);
        }
    }

}