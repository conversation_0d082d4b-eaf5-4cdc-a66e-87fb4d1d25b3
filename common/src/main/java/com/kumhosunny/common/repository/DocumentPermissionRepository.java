package com.kumhosunny.common.repository;

import com.kumhosunny.common.entity.DocumentPermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 文档权限Repository接口
 * 
 * <AUTHOR>
 */
@Repository
public interface DocumentPermissionRepository extends JpaRepository<DocumentPermission, Long> {

    /**
     * 根据文档ID查找所有权限配置
     */
    List<DocumentPermission> findByDocumentId(Long documentId);

    /**
     * 根据用户ID查找用户的所有权限
     */
    List<DocumentPermission> findByUserId(Long userId);

    /**
     * 根据部门ID查找部门的所有权限
     */
    List<DocumentPermission> findByDepartmentId(Long departmentId);

    /**
     * 根据文档ID和用户ID查找特定权限
     */
    Optional<DocumentPermission> findByDocumentIdAndUserId(Long documentId, Long userId);

    /**
     * 根据文档ID和部门ID查找特定权限
     */
    Optional<DocumentPermission> findByDocumentIdAndDepartmentId(Long documentId, Long departmentId);

    /**
     * 查找用户对文档的权限（包括用户权限和部门权限）
     */
    @Query("SELECT dp FROM DocumentPermission dp WHERE dp.documentId = :documentId " +
           "AND (dp.userId = :userId OR dp.departmentId = :departmentId)")
    List<DocumentPermission> findUserPermissions(@Param("documentId") Long documentId, 
                                                 @Param("userId") Long userId, 
                                                 @Param("departmentId") Long departmentId);

    /**
     * 检查用户是否对文档有特定权限
     */
    @Query("SELECT COUNT(dp) > 0 FROM DocumentPermission dp WHERE dp.documentId = :documentId " +
           "AND (dp.userId = :userId OR dp.departmentId = :departmentId) " +
           "AND dp.permission = :permission")
    boolean hasPermission(@Param("documentId") Long documentId, 
                         @Param("userId") Long userId, 
                         @Param("departmentId") Long departmentId, 
                         @Param("permission") DocumentPermission.PermissionType permission);

    /**
     * 查找用户有特定权限的所有文档ID
     */
    @Query("SELECT dp.documentId FROM DocumentPermission dp WHERE " +
           "(dp.userId = :userId OR dp.departmentId = :departmentId) " +
           "AND dp.permission = :permission")
    List<Long> findDocumentIdsByUserPermission(@Param("userId") Long userId, 
                                              @Param("departmentId") Long departmentId, 
                                              @Param("permission") DocumentPermission.PermissionType permission);

    /**
     * 删除文档的所有权限配置
     */
    void deleteByDocumentId(Long documentId);

    /**
     * 删除用户的特定文档权限
     */
    void deleteByDocumentIdAndUserId(Long documentId, Long userId);

    /**
     * 删除部门的特定文档权限
     */
    void deleteByDocumentIdAndDepartmentId(Long documentId, Long departmentId);

    /**
     * 统计文档的权限配置数量
     */
    long countByDocumentId(Long documentId);

    /**
     * 统计用户拥有权限的文档数量
     */
    @Query("SELECT COUNT(DISTINCT dp.documentId) FROM DocumentPermission dp WHERE " +
           "(dp.userId = :userId OR dp.departmentId = :departmentId)")
    long countDocumentsByUser(@Param("userId") Long userId, @Param("departmentId") Long departmentId);

    /**
     * 查找拥有文档管理员权限的用户
     */
    @Query("SELECT dp FROM DocumentPermission dp WHERE dp.documentId = :documentId " +
           "AND dp.permission = 'admin' AND dp.userId IS NOT NULL")
    List<DocumentPermission> findDocumentAdmins(@Param("documentId") Long documentId);

    /**
     * 查找文档的所有权限类型（用于权限检查）
     */
    @Query("SELECT DISTINCT dp.permission FROM DocumentPermission dp WHERE dp.documentId = :documentId " +
           "AND (dp.userId = :userId OR dp.departmentId = :departmentId)")
    List<DocumentPermission.PermissionType> findUserPermissionTypes(@Param("documentId") Long documentId, 
                                                                    @Param("userId") Long userId, 
                                                                    @Param("departmentId") Long departmentId);
} 