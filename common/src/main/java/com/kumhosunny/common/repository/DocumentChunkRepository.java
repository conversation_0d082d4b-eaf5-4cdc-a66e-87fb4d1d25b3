package com.kumhosunny.common.repository;

import com.kumhosunny.common.entity.DocumentChunk;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 文档片段Repository接口
 * 
 * <AUTHOR>
 */
@Repository
public interface DocumentChunkRepository extends JpaRepository<DocumentChunk, Long> {

    /**
     * 根据文档ID查找所有片段（按序号排序）
     */
    List<DocumentChunk> findByDocumentIdOrderByChunkIndexAsc(Long documentId);

    /**
     * 根据文档ID和片段索引查找特定片段
     */
    Optional<DocumentChunk> findByDocumentIdAndChunkIndex(Long documentId, Integer chunkIndex);

    /**
     * 根据向量ID查找片段
     */
    Optional<DocumentChunk> findByVectorId(String vectorId);

    /**
     * 根据嵌入状态查找片段
     */
    List<DocumentChunk> findByEmbeddingStatus(DocumentChunk.EmbeddingStatus status);

    /**
     * 根据文档ID和嵌入状态查找片段
     */
    List<DocumentChunk> findByDocumentIdAndEmbeddingStatus(Long documentId, DocumentChunk.EmbeddingStatus status);

    /**
     * 查找所有待处理的向量嵌入片段
     */
    List<DocumentChunk> findByEmbeddingStatusOrderByCreatedAtAsc(DocumentChunk.EmbeddingStatus status);

    /**
     * 统计文档的片段数量
     */
    long countByDocumentId(Long documentId);

    /**
     * 统计特定状态的片段数量
     */
    long countByEmbeddingStatus(DocumentChunk.EmbeddingStatus status);

    /**
     * 统计文档中特定状态的片段数量
     */
    long countByDocumentIdAndEmbeddingStatus(Long documentId, DocumentChunk.EmbeddingStatus status);

    /**
     * 删除文档的所有片段
     */
    void deleteByDocumentId(Long documentId);

    /**
     * 根据内容关键词搜索片段
     */
    @Query("SELECT dc FROM DocumentChunk dc WHERE dc.content LIKE %:keyword%")
    List<DocumentChunk> searchByContent(@Param("keyword") String keyword);

    /**
     * 查找指定文档中包含关键词的片段
     */
    @Query("SELECT dc FROM DocumentChunk dc WHERE dc.documentId = :documentId AND dc.content LIKE %:keyword%")
    List<DocumentChunk> searchByDocumentAndContent(@Param("documentId") Long documentId,
            @Param("keyword") String keyword);

    /**
     * 查找已完成向量化且有向量ID的片段
     */
    @Query("SELECT dc FROM DocumentChunk dc WHERE dc.embeddingStatus = 'done' AND dc.vectorId IS NOT NULL")
    List<DocumentChunk> findVectorizedChunks();

    /**
     * 根据文档ID查找已向量化的片段
     */
    @Query("SELECT dc FROM DocumentChunk dc WHERE dc.documentId = :documentId AND dc.embeddingStatus = 'done' AND dc.vectorId IS NOT NULL")
    List<DocumentChunk> findVectorizedChunksByDocument(@Param("documentId") Long documentId);

    /**
     * 批量更新片段的向量嵌入状态
     */
    @Query("UPDATE DocumentChunk dc SET dc.embeddingStatus = :status WHERE dc.documentId = :documentId")
    void updateEmbeddingStatusByDocument(@Param("documentId") Long documentId,
            @Param("status") DocumentChunk.EmbeddingStatus status);

    /**
     * 获取指定chunk的上下文chunks（前后相邻的chunks）
     * 
     * @param documentId  文档ID
     * @param chunkIndex  当前chunk索引
     * @param contextSize 上下文大小（前后各contextSize个chunk）
     * @return 上下文chunks列表（包括当前chunk）
     */
    @Query("SELECT dc FROM DocumentChunk dc WHERE dc.documentId = :documentId " +
            "AND dc.chunkIndex >= :startIndex AND dc.chunkIndex <= :endIndex " +
            "ORDER BY dc.chunkIndex ASC")
    List<DocumentChunk> findContextChunks(@Param("documentId") Long documentId,
            @Param("startIndex") Integer startIndex,
            @Param("endIndex") Integer endIndex);

    /**
     * 根据向量ID查找chunk及其上下文
     * 
     * @param vectorId    向量ID
     * @param contextSize 上下文大小（前后各contextSize个chunk）
     * @return 上下文chunks列表（包括当前chunk）
     */
    @Query("SELECT dc2 FROM DocumentChunk dc1, DocumentChunk dc2 " +
            "WHERE dc1.vectorId = :vectorId " +
            "AND dc2.documentId = dc1.documentId " +
            "AND dc2.chunkIndex >= (dc1.chunkIndex - :contextSize) " +
            "AND dc2.chunkIndex <= (dc1.chunkIndex + :contextSize) " +
            "ORDER BY dc2.chunkIndex ASC")
    List<DocumentChunk> findContextChunksByVectorId(@Param("vectorId") String vectorId,
            @Param("contextSize") Integer contextSize);

    List<DocumentChunk> findByDocumentIdAndChunkIndexBetween(Long documentId, Integer start, Integer end);

    @Modifying
    @Query("DELETE FROM DocumentChunk dc WHERE dc.documentId IN :documentIds")
    void deleteByDocumentIds(@Param("documentIds") List<Long> documentIds);
}