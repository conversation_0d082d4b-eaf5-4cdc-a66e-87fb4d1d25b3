package com.kumhosunny.common.repository;

import com.kumhosunny.common.entity.AiModelRoute;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * AI模型路由配置 Repository
 */
@Repository
public interface AiModelRouteRepository extends JpaRepository<AiModelRoute, Long> {

    /**
     * 根据请求模型名查找启用的路由配置（按优先级降序）
     */
    List<AiModelRoute> findByRequestModelAndStatusOrderByPriorityDesc(String requestModel, Integer status);

    /**
     * 根据请求模型名查找最高优先级的启用路由配置
     */
    Optional<AiModelRoute> findFirstByRequestModelAndStatusOrderByPriorityDesc(String requestModel, Integer status);

    /**
     * 根据请求模型名查找启用的路由配置
     */
    @Query("SELECT r FROM AiModelRoute r " +
            "LEFT JOIN FETCH r.targetModel tm " +
            "LEFT JOIN FETCH tm.provider " +
            "WHERE r.requestModel = ?1 AND r.status = 1 ORDER BY r.priority DESC")
    Optional<AiModelRoute> findActiveRouteByRequestModel(String requestModel);

    /**
     * 查找所有启用的路由配置（按优先级降序）
     */
    List<AiModelRoute> findByStatusOrderByPriorityDesc(Integer status);

    /**
     * 查找所有启用的路由配置（使用JOIN FETCH预加载关联对象）
     */
    @Query("SELECT r FROM AiModelRoute r " +
            "LEFT JOIN FETCH r.targetModel tm " +
            "LEFT JOIN FETCH tm.provider " +
            "WHERE r.status = 1 ORDER BY r.priority DESC")
    List<AiModelRoute> findAllActiveRoutes();

    /**
     * 根据目标模型ID查找路由配置
     */
    List<AiModelRoute> findByTargetModelIdAndStatus(Long targetModelId, Integer status);

    /**
     * 检查请求模型名是否已存在路由配置
     */
    boolean existsByRequestModelAndStatus(String requestModel, Integer status);

    /**
     * 统计启用的路由配置数量
     */
    long countByStatus(Integer status);

    /**
     * 根据请求模型名模糊查询启用的路由配置
     */
    List<AiModelRoute> findByRequestModelContainingAndStatusOrderByPriorityDesc(String requestModel, Integer status);

    /**
     * 根据优先级范围查找启用的路由配置
     */
    List<AiModelRoute> findByPriorityBetweenAndStatusOrderByPriorityDesc(Integer minPriority, Integer maxPriority,
            Integer status);
}