package com.kumhosunny.common.repository;

import com.kumhosunny.common.entity.AiGeneratedContent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * AI生成内容仓库接口
 */
@Repository
public interface AiGeneratedContentRepository extends JpaRepository<AiGeneratedContent, Long> {

    /**
     * 根据内容的公开ID查找内容
     * 
     * @param contentId 公开ID (UUID)
     * @return 内容实体
     */
    Optional<AiGeneratedContent> findByContentId(String contentId);

    /**
     * 根据用户ID查找其生成的内容（分页）- 只查询正常状态的内容
     * 
     * @param userId   用户ID
     * @param pageable 分页和排序信息
     * @return 分页后的内容列表
     */
    @Query("SELECT a FROM AiGeneratedContent a WHERE a.userId = :userId AND a.status = 0")
    Page<AiGeneratedContent> findByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查找所有公开的内容（分页）- 只查询正常状态的内容
     * 
     * @param isPublic 是否公开
     * @param pageable 分页和排序信息
     * @return 分页后的公开内容列表
     */
    @Query("SELECT a FROM AiGeneratedContent a WHERE a.isPublic = :isPublic AND a.status = 0")
    Page<AiGeneratedContent> findByIsPublic(@Param("isPublic") Boolean isPublic, Pageable pageable);

    /**
     * 虚拟删除指定用户的指定ID内容（将status设置为-1）
     * 
     * @param userId     用户ID
     * @param contentIds 内容ID列表
     * @return 更新的记录数
     */
    @Modifying
    @Query("UPDATE AiGeneratedContent a SET a.status = -1 WHERE a.userId = :userId AND a.id IN :contentIds AND a.status = 0")
    int softDeleteByUserIdAndIdIn(@Param("userId") Long userId, @Param("contentIds") List<Long> contentIds);

    /**
     * 根据contentId虚拟删除指定用户的内容（将status设置为-1）
     * 
     * @param userId     用户ID
     * @param contentIds 内容UUID列表
     * @return 更新的记录数
     */
    @Modifying
    @Query("UPDATE AiGeneratedContent a SET a.status = -1 WHERE a.userId = :userId AND a.contentId IN :contentIds AND a.status = 0")
    int softDeleteByUserIdAndContentIdIn(@Param("userId") Long userId, @Param("contentIds") List<String> contentIds);
}