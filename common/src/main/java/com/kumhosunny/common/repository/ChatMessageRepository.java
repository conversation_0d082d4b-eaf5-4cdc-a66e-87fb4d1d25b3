package com.kumhosunny.common.repository;

import com.kumhosunny.common.entity.ChatMessage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 聊天消息 Repository
 */
@Repository
public interface ChatMessageRepository extends JpaRepository<ChatMessage, Long> {

    /**
     * 根据会话ID查询所有消息，按创建时间排序
     */
    List<ChatMessage> findBySessionIdOrderByCreatedAtAsc(String sessionId);

    /**
     * 根据会话ID查询前4条消息，用于生成标题
     */
    List<ChatMessage> findTop4BySessionIdOrderByCreatedAtAsc(String sessionId);

    /**
     * 根据会话ID查询最新的N条消息
     */
    @Query("SELECT m FROM ChatMessage m WHERE m.sessionId = :sessionId ORDER BY m.createdAt DESC")
    List<ChatMessage> findRecentMessagesBySessionId(@Param("sessionId") String sessionId);

    /**
     * 根据父消息ID查询回复消息
     */
    List<ChatMessage> findByParentMessageIdOrderByCreatedAtAsc(Long parentMessageId);

    /**
     * 查询会话中的消息数量
     */
    long countBySessionId(String sessionId);

    /**
     * 删除指定会话的所有消息
     */
    void deleteBySessionId(String sessionId);

    /**
     * 查询指定会话中指定发送者的消息
     */
    List<ChatMessage> findBySessionIdAndSenderOrderByCreatedAtAsc(String sessionId, ChatMessage.MessageSender sender);

    /**
     * 统计指定会话的总token数
     */
    @Query("SELECT COALESCE(SUM(m.tokenCount), 0) FROM ChatMessage m WHERE m.sessionId = :sessionId")
    Long sumTokenCountBySessionId(@Param("sessionId") String sessionId);
}