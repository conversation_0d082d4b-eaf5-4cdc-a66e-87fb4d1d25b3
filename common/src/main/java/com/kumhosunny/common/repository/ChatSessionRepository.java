package com.kumhosunny.common.repository;

import com.kumhosunny.common.entity.ChatSession;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 聊天会话 Repository
 */
@Repository
public interface ChatSessionRepository extends JpaRepository<ChatSession, String> {

    /**
     * 根据用户ID查询所有活跃会话，按更新时间倒序
     */
    List<ChatSession> findByUserIdAndStatusOrderByUpdatedAtDesc(Long userId, ChatSession.SessionStatus status);

    /**
     * 根据用户ID查询所有会话历史，按更新时间倒序
     */
    List<ChatSession> findByUserIdOrderByUpdatedAtDesc(Long userId);

    /**
     * 根据用户ID和会话ID查询特定会话
     */
    Optional<ChatSession> findByIdAndUserId(String id, Long userId);

    /**
     * 查询指定用户的会话数量
     */
    long countByUserId(Long userId);

    /**
     * 查询包含消息的会话详情
     */
    @Query("SELECT s FROM ChatSession s LEFT JOIN FETCH s.messages WHERE s.id = :sessionId AND s.userId = :userId")
    Optional<ChatSession> findByIdAndUserIdWithMessages(@Param("sessionId") String sessionId,
            @Param("userId") Long userId);

    /**
     * 查询用户最近的N个会话
     */
    @Query("SELECT s FROM ChatSession s WHERE s.userId = :userId ORDER BY s.updatedAt DESC")
    List<ChatSession> findRecentSessions(@Param("userId") Long userId);

    @Modifying
    @Transactional
    @Query("UPDATE ChatSession s SET s.status = :status WHERE s.userId = :userId")
    void updateStatusByUserId(@Param("userId") Long userId, @Param("status") ChatSession.SessionStatus status);
}