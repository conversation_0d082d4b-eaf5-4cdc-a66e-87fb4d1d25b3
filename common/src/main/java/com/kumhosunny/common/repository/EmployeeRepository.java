package com.kumhosunny.common.repository;

import com.kumhosunny.common.entity.EmployeeEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 员工Repository接口
 *
 * <AUTHOR>
 */
@Repository
public interface EmployeeRepository extends JpaRepository<EmployeeEntity, Long> {

    /**
     * 根据登录名查找员工
     */
    Optional<EmployeeEntity> findByLoginName(String loginName);

    /**
     * 根据unionId查找员工
     */
    Optional<EmployeeEntity> findByUnionId(String unionId);

    /**
     * 根据手机号查找员工
     */
    Optional<EmployeeEntity> findByPhone(String phone);

    /**
     * 根据邮箱查找员工
     */
    Optional<EmployeeEntity> findByEmail(String email);

    /**
     * 根据部门ID查找未删除的员工
     */
    List<EmployeeEntity> findByDepartmentIdAndDeletedFlagFalse(Long departmentId);

    /**
     * 根据职务级别ID查找未删除的员工
     */
    List<EmployeeEntity> findByPositionIdAndDeletedFlagFalse(Long positionId);

    /**
     * 查找所有未删除的员工
     */
    List<EmployeeEntity> findByDeletedFlagFalse();

    /**
     * 查找所有未删除且未禁用的员工
     */
    List<EmployeeEntity> findByDeletedFlagFalseAndDisabledFlagFalse();

    /**
     * 查找超级管理员
     */
    List<EmployeeEntity> findByAdministratorFlagTrueAndDeletedFlagFalse();

    /**
     * 检查登录名是否存在（排除指定员工ID）
     */
    @Query("SELECT COUNT(e) > 0 FROM EmployeeEntity e WHERE e.loginName = :loginName AND e.employeeId != :excludeId AND e.deletedFlag = false")
    boolean existsByLoginNameAndNotEmployeeId(@Param("loginName") String loginName, @Param("excludeId") Long excludeId);

    /**
     * 检查手机号是否存在（排除指定员工ID）
     */
    @Query("SELECT COUNT(e) > 0 FROM EmployeeEntity e WHERE e.phone = :phone AND e.employeeId != :excludeId AND e.deletedFlag = false")
    boolean existsByPhoneAndNotEmployeeId(@Param("phone") String phone, @Param("excludeId") Long excludeId);

    /**
     * 检查邮箱是否存在（排除指定员工ID）
     */
    @Query("SELECT COUNT(e) > 0 FROM EmployeeEntity e WHERE e.email = :email AND e.employeeId != :excludeId AND e.deletedFlag = false")
    boolean existsByEmailAndNotEmployeeId(@Param("email") String email, @Param("excludeId") Long excludeId);

    /**
     * 统计部门员工数量
     */
    long countByDepartmentIdAndDeletedFlagFalse(Long departmentId);
    /**
     * 根据部门ID查找员工
     */
    List<EmployeeEntity> findByDepartmentId(Long departmentId);

    /**
     * 根据部门ID列表查找员工
     */
    List<EmployeeEntity> findByDepartmentIdIn(List<Long> departmentIds);

    /**
     * 统计部门员工数量
     */
    long countByDepartmentId(Long departmentId);
} 