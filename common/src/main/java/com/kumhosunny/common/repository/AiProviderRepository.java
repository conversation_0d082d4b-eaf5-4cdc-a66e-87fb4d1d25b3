package com.kumhosunny.common.repository;

import com.kumhosunny.common.entity.AiProvider;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AiProviderRepository extends JpaRepository<AiProvider, Long> {

    /**
     * 根据提供商编码查找提供商
     */
    Optional<AiProvider> findByProviderCode(String providerCode);

    /**
     * 查找所有启用的提供商（按提供商编码排序）
     */
    List<AiProvider> findByStatusOrderByProviderCodeAsc(Integer status);

    /**
     * 根据状态查找提供商
     */
    List<AiProvider> findByStatus(Integer status);

    /**
     * 检查提供商编码是否存在
     */
    boolean existsByProviderCode(String providerCode);

    /**
     * 统计启用的提供商数量
     */
    long countByStatus(Integer status);

    /**
     * 根据提供商名称模糊查询（启用状态）
     */
    List<AiProvider> findByProviderNameContainingAndStatusOrderByProviderCodeAsc(String providerName, Integer status);
}