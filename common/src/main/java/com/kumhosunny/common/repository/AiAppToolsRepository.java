package com.kumhosunny.common.repository;

import com.kumhosunny.common.entity.AiAppTools;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * AI应用工具 Repository
 */
@Repository
public interface AiAppToolsRepository extends JpaRepository<AiAppTools, Integer> {

    /**
     * 根据应用ID查询工具列表
     */
    List<AiAppTools> findByAppId(Integer appId);

    /**
     * 根据工具类型查询
     */
    List<AiAppTools> findByToolType(String toolType);

    /**
     * 根据应用ID和工具类型查询
     */
    List<AiAppTools> findByAppIdAndToolType(Integer appId, String toolType);

    /**
     * 删除指定应用的所有工具
     */
    void deleteByAppId(Integer appId);
}