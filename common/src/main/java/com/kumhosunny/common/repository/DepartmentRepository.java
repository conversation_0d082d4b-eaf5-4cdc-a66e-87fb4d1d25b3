package com.kumhosunny.common.repository;

import com.kumhosunny.common.entity.Department;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 部门Repository接口
 * 
 * <AUTHOR>
 */
@Repository
public interface DepartmentRepository extends JpaRepository<Department, Long> {

    /**
     * 根据父级部门ID查找子部门列表（按排序字段排序）
     */
    List<Department> findByParentIdOrderBySortAsc(Long parentId);

    /**
     * 根据部门名称查找部门
     */
    Optional<Department> findByDepartmentName(String departmentName);

    /**
     * 根据部门名称查找部门（模糊查询）
     */
    List<Department> findByDepartmentNameContaining(String departmentName);

    /**
     * 根据负责人ID查找部门
     */
    List<Department> findByManagerId(Long managerId);

    /**
     * 查找所有根部门（父级ID为0或null）
     */
    @Query("SELECT d FROM Department d WHERE d.parentId = 0 OR d.parentId IS NULL ORDER BY d.sort ASC")
    List<Department> findRootDepartments();

    /**
     * 查找指定部门的所有子部门（递归查询）
     */
    @Query(value = "WITH RECURSIVE dept_tree AS (" +
                   "SELECT * FROM t_department WHERE department_id = :departmentId " +
                   "UNION ALL " +
                   "SELECT d.* FROM t_department d " +
                   "INNER JOIN dept_tree dt ON d.parent_id = dt.department_id" +
                   ") SELECT * FROM dept_tree WHERE department_id != :departmentId", 
           nativeQuery = true)
    List<Department> findAllChildrenDepartments(@Param("departmentId") Long departmentId);

    /**
     * 查找指定部门的所有父级部门（向上递归查询）
     */
    @Query(value = "WITH RECURSIVE dept_path AS (" +
                   "SELECT * FROM t_department WHERE department_id = :departmentId " +
                   "UNION ALL " +
                   "SELECT d.* FROM t_department d " +
                   "INNER JOIN dept_path dp ON d.department_id = dp.parent_id" +
                   ") SELECT * FROM dept_path WHERE department_id != :departmentId ORDER BY department_id", 
           nativeQuery = true)
    List<Department> findAllParentDepartments(@Param("departmentId") Long departmentId);

    /**
     * 查找指定部门及其所有子部门的ID列表
     */
    @Query(value = "WITH RECURSIVE dept_tree AS (" +
                   "SELECT department_id FROM t_department WHERE department_id = :departmentId " +
                   "UNION ALL " +
                   "SELECT d.department_id FROM t_department d " +
                   "INNER JOIN dept_tree dt ON d.parent_id = dt.department_id" +
                   ") SELECT department_id FROM dept_tree", 
           nativeQuery = true)
    List<Long> findDepartmentAndChildrenIds(@Param("departmentId") Long departmentId);

    /**
     * 统计指定部门的子部门数量
     */
    long countByParentId(Long parentId);

    /**
     * 检查部门名称是否存在（排除指定ID）
     */
    @Query("SELECT COUNT(d) > 0 FROM Department d WHERE d.departmentName = :name AND (:excludeId IS NULL OR d.departmentId != :excludeId)")
    boolean existsByDepartmentNameAndIdNot(@Param("name") String departmentName, @Param("excludeId") Long excludeId);

    /**
     * 查找指定层级深度的部门
     */
    @Query(value = "WITH RECURSIVE dept_level AS (" +
                   "SELECT *, 0 as level FROM t_department WHERE parent_id = 0 OR parent_id IS NULL " +
                   "UNION ALL " +
                   "SELECT d.*, dl.level + 1 FROM t_department d " +
                   "INNER JOIN dept_level dl ON d.parent_id = dl.department_id" +
                   ") SELECT * FROM dept_level WHERE level = :level ORDER BY sort", 
           nativeQuery = true)
    List<Department> findByLevel(@Param("level") Integer level);

    /**
     * 查找部门树结构（带层级信息）
     */
    @Query(value = "WITH RECURSIVE dept_tree AS (" +
                   "SELECT *, 0 as level, CAST(department_name AS CHAR(1000)) as path FROM t_department WHERE parent_id = 0 OR parent_id IS NULL " +
                   "UNION ALL " +
                   "SELECT d.*, dt.level + 1, CONCAT(dt.path, ' > ', d.department_name) FROM t_department d " +
                   "INNER JOIN dept_tree dt ON d.parent_id = dt.department_id" +
                   ") SELECT * FROM dept_tree ORDER BY path", 
           nativeQuery = true)
    List<Object[]> findDepartmentTreeWithLevel();



    /**
     * 查找有员工的部门列表
     */
    @Query("SELECT DISTINCT d FROM Department d WHERE d.departmentId IN " +
           "(SELECT e.departmentId FROM EmployeeEntity e WHERE e.departmentId IS NOT NULL)")
    List<Department> findDepartmentsWithEmployees();

    /**
     * 查找没有员工的部门列表
     */
    @Query("SELECT d FROM Department d WHERE d.departmentId NOT IN " +
           "(SELECT DISTINCT e.departmentId FROM EmployeeEntity e WHERE e.departmentId IS NOT NULL)")
    List<Department> findDepartmentsWithoutEmployees();

    /**
     * 查找指定部门的顶级父部门ID（递归查询到根部门）
     */
    @Query(value = "WITH RECURSIVE dept_path AS (" +
                   "SELECT department_id, parent_id FROM t_department WHERE department_id = :departmentId " +
                   "UNION ALL " +
                   "SELECT d.department_id, d.parent_id FROM t_department d " +
                   "INNER JOIN dept_path dp ON d.department_id = dp.parent_id" +
                   ") SELECT department_id FROM dept_path WHERE parent_id = 0 OR parent_id IS NULL", 
           nativeQuery = true)
    Long findTopParentDepartmentId(@Param("departmentId") Long departmentId);
} 