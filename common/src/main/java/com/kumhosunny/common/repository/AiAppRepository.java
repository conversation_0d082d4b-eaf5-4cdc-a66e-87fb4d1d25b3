package com.kumhosunny.common.repository;

import com.kumhosunny.common.entity.AiApp;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.data.jpa.repository.EntityGraph;

import java.util.List;

/**
 * AI应用 Repository
 */
@Repository
public interface AiAppRepository extends JpaRepository<AiApp, Integer> {

    /**
     * 查询所有公开的AI应用
     */
    List<AiApp> findByIsPublicTrueOrderByCreatedAtDesc();

    /**
     * 根据名称查找AI应用
     */
    List<AiApp> findByNameContainingIgnoreCase(String name);

    /**
     * 查询指定创建者的应用
     */
    List<AiApp> findByCreatedByOrderByCreatedAtDesc(Integer createdBy);

    /**
     * 自定义查询：查询公开应用的统计信息
     */
    @Query("SELECT COUNT(a) FROM AiApp a WHERE a.isPublic = true")
    long countPublicApps();

    /**
     * 查询所有公开的AI应用，预加载分类信息
     */
    @Query("SELECT a FROM AiApp a LEFT JOIN FETCH a.category WHERE a.isPublic = true ORDER BY a.createdAt DESC")
    List<AiApp> findByIsPublicTrueWithCategoryOrderByCreatedAtDesc();
}