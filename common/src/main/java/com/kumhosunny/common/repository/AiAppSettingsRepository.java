package com.kumhosunny.common.repository;

import com.kumhosunny.common.entity.AiAppSettings;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * AI应用设置 Repository
 */
@Repository
public interface AiAppSettingsRepository extends JpaRepository<AiAppSettings, Integer> {

    /**
     * 根据应用ID查询设置
     */
    Optional<AiAppSettings> findByAppId(Integer appId);

    /**
     * 根据模型名称查询设置
     */
    Optional<AiAppSettings> findByModel(String model);

    /**
     * 删除指定应用的设置
     */
    void deleteByAppId(Integer appId);
}