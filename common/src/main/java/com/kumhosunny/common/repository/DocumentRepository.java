package com.kumhosunny.common.repository;

import com.kumhosunny.common.entity.Document;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 文档Repository接口
 * 
 * <AUTHOR>
 */
@Repository
public interface DocumentRepository extends JpaRepository<Document, Long> {

    /**
     * 根据文档标题查找文档
     */
    List<Document> findByTitleContaining(String title);

    /**
     * 根据文档类型查找文档
     */
    List<Document> findByType(Document.DocumentType type);

    /**
     * 根据创建者ID查找文档
     */
    List<Document> findByOwnerId(Long ownerId);

    /**
     * 根据部门ID查找部门文档
     */
    List<Document> findByDepartmentId(Long departmentId);

    /**
     * 查找公开文档
     */
    List<Document> findByIsPublicTrue();

    /**
     * 根据创建者和文档类型查找文档
     */
    List<Document> findByOwnerIdAndType(Long ownerId, Document.DocumentType type);

    /**
     * 根据部门ID和文档类型查找文档
     */
    List<Document> findByDepartmentIdAndType(Long departmentId, Document.DocumentType type);

    /**
     * 分页查询用户可访问的文档
     * 包括：个人文档、所属部门文档、公开文档、有权限的文档
     */
    @Query("SELECT DISTINCT d FROM Document d " +
            "LEFT JOIN DocumentPermission dp ON d.id = dp.documentId " +
            "WHERE d.ownerId = :userId " +
            "   OR d.isPublic = true " +
            "   OR (d.type = 'department' AND d.departmentId = :departmentId) " +
            "   OR (dp.userId = :userId) " +
            "   OR (dp.departmentId = :departmentId)")
    Page<Document> findAccessibleDocuments(@Param("userId") Long userId,
            @Param("departmentId") Long departmentId,
            Pageable pageable);

    /**
     * 根据关键词搜索文档标题和内容
     */
    @Query("SELECT d FROM Document d WHERE d.title LIKE %:keyword% OR d.content LIKE %:keyword%")
    List<Document> searchByKeyword(@Param("keyword") String keyword);

    /**
     * 查询用户有特定权限的文档
     */
    @Query("SELECT d FROM Document d " +
            "JOIN DocumentPermission dp ON d.id = dp.documentId " +
            "WHERE dp.userId = :userId AND dp.permission = :permission")
    List<Document> findByUserPermission(@Param("userId") Long userId,
            @Param("permission") String permission);

    /**
     * 检查用户是否对文档有访问权限
     */
    @Query("SELECT COUNT(d) > 0 FROM Document d " +
            "LEFT JOIN DocumentPermission dp ON d.id = dp.documentId " +
            "WHERE d.id = :documentId " +
            "AND (d.ownerId = :userId " +
            "     OR d.isPublic = true " +
            "     OR (d.type = 'department' AND d.departmentId = :departmentId) " +
            "     OR (dp.userId = :userId) " +
            "     OR (dp.departmentId = :departmentId))")
    boolean hasAccess(@Param("documentId") Long documentId,
            @Param("userId") Long userId,
            @Param("departmentId") Long departmentId);

    /**
     * 统计用户可访问的文档数量
     */
    @Query("SELECT COUNT(DISTINCT d) FROM Document d " +
            "LEFT JOIN DocumentPermission dp ON d.id = dp.documentId " +
            "WHERE d.ownerId = :userId " +
            "   OR d.isPublic = true " +
            "   OR (d.type = 'department' AND d.departmentId = :departmentId) " +
            "   OR (dp.userId = :userId) " +
            "   OR (dp.departmentId = :departmentId)")
    long countAccessibleDocuments(@Param("userId") Long userId, @Param("departmentId") Long departmentId);

    /**
     * 根据内容哈希查找文档（用于去重）
     */
    Optional<Document> findByContentHash(String contentHash);

    /**
     * 根据内容哈希查找文档列表（可能存在多个相同内容的文档）
     */
    List<Document> findAllByContentHash(String contentHash);

    /**
     * 检查是否存在相同内容哈希和类型的文档
     */
    boolean existsByContentHashAndType(String contentHash,String type);

    /**
     * 查找具有相同内容哈希但不同ID的文档（排除自身）
     */
    @Query("SELECT d FROM Document d WHERE d.contentHash = :contentHash AND d.id != :excludeId")
    List<Document> findDuplicatesByContentHash(@Param("contentHash") String contentHash,
            @Param("excludeId") Long excludeId);

    /**
     * 统计重复文档数量
     */
    @Query("SELECT COUNT(d) FROM Document d WHERE d.contentHash IN " +
            "(SELECT d2.contentHash FROM Document d2 GROUP BY d2.contentHash HAVING COUNT(d2.contentHash) > 1)")
    long countDuplicateDocuments();

    /**
     * 查找所有重复的文档组（按contentHash分组）
     */
    @Query("SELECT d.contentHash, COUNT(d) as count FROM Document d " +
            "GROUP BY d.contentHash HAVING COUNT(d) > 1")
    List<Object[]> findDuplicateGroups();

    @Query("SELECT d.id FROM Document d WHERE d.fileId = :fileId")
    List<Long> findIdsByFileId(@Param("fileId") Long fileId);

    @Transactional
    @Modifying
    @Query("DELETE FROM Document d WHERE d.fileId = :fileId")
    void deleteByFileId(@Param("fileId") Long fileId);
}