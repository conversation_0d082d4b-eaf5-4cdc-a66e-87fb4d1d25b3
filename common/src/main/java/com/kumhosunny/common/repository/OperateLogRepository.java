package com.kumhosunny.common.repository;

import com.kumhosunny.common.entity.OperateLogEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作日志Repository接口
 *
 * <AUTHOR>
 */
@Repository
public interface OperateLogRepository extends JpaRepository<OperateLogEntity, Long> {

    /**
     * 根据操作用户ID查找操作日志
     */
    Page<OperateLogEntity> findByOperateUserId(Long operateUserId, Pageable pageable);

    /**
     * 根据操作模块查找操作日志
     */
    Page<OperateLogEntity> findByModule(String module, Pageable pageable);

    /**
     * 根据操作结果查找操作日志
     */
    Page<OperateLogEntity> findBySuccessFlag(Boolean successFlag, Pageable pageable);

    /**
     * 根据操作用户类型查找操作日志
     */
    Page<OperateLogEntity> findByOperateUserType(Integer operateUserType, Pageable pageable);

    /**
     * 根据时间范围查找操作日志
     */
    @Query("SELECT o FROM OperateLogEntity o WHERE o.createTime BETWEEN :startTime AND :endTime ORDER BY o.createTime DESC")
    Page<OperateLogEntity> findByCreateTimeBetween(
            @Param("startTime") LocalDateTime startTime, 
            @Param("endTime") LocalDateTime endTime, 
            Pageable pageable);

    /**
     * 根据操作用户ID和时间范围查找操作日志
     */
    @Query("SELECT o FROM OperateLogEntity o WHERE o.operateUserId = :userId AND o.createTime BETWEEN :startTime AND :endTime ORDER BY o.createTime DESC")
    Page<OperateLogEntity> findByOperateUserIdAndCreateTimeBetween(
            @Param("userId") Long userId,
            @Param("startTime") LocalDateTime startTime, 
            @Param("endTime") LocalDateTime endTime, 
            Pageable pageable);

    /**
     * 根据模块和时间范围查找操作日志
     */
    @Query("SELECT o FROM OperateLogEntity o WHERE o.module = :module AND o.createTime BETWEEN :startTime AND :endTime ORDER BY o.createTime DESC")
    Page<OperateLogEntity> findByModuleAndCreateTimeBetween(
            @Param("module") String module,
            @Param("startTime") LocalDateTime startTime, 
            @Param("endTime") LocalDateTime endTime, 
            Pageable pageable);

    /**
     * 查找失败的操作日志
     */
    @Query("SELECT o FROM OperateLogEntity o WHERE o.successFlag = false ORDER BY o.createTime DESC")
    Page<OperateLogEntity> findFailedOperations(Pageable pageable);

    /**
     * 统计某个用户的操作次数
     */
    long countByOperateUserId(Long operateUserId);

    /**
     * 统计某个模块的操作次数
     */
    long countByModule(String module);

    /**
     * 统计失败操作次数
     */
    long countBySuccessFlagFalse();

    /**
     * 统计指定时间范围内的操作次数
     */
    @Query("SELECT COUNT(o) FROM OperateLogEntity o WHERE o.createTime BETWEEN :startTime AND :endTime")
    long countByCreateTimeBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 删除指定时间之前的操作日志
     */
    void deleteByCreateTimeBefore(LocalDateTime beforeTime);

    /**
     * 查找最近的操作日志
     */
    List<OperateLogEntity> findTop10ByOrderByCreateTimeDesc();
} 