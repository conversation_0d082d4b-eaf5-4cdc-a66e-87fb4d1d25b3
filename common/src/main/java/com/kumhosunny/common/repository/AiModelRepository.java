package com.kumhosunny.common.repository;

import com.kumhosunny.common.entity.AiModel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AiModelRepository extends JpaRepository<AiModel, Long> {

    /**
     * 根据模型编码查找模型
     */
    Optional<AiModel> findByModelCode(String modelCode);

    /**
     * 根据提供商ID和状态查找模型
     */
    List<AiModel> findByProviderIdAndStatus(Long providerId, Integer status);

    /**
     * 根据模型类型查找启用的模型
     */
    List<AiModel> findByModelTypeAndStatus(AiModel.ModelType modelType, Integer status);

    /**
     * 查找所有启用的模型（按模型类型和编码排序）
     */
    List<AiModel> findByStatusOrderByModelTypeAscModelCodeAsc(Integer status);

    /**
     * 根据模型类型查找启用的模型（按模型编码排序）
     */
    List<AiModel> findByModelTypeAndStatusOrderByModelCodeAsc(AiModel.ModelType modelType, Integer status);

    /**
     * 根据提供商ID查找启用的模型（按模型编码排序）
     */
    List<AiModel> findByProviderIdAndStatusOrderByModelCodeAsc(Long providerId, Integer status);

    /**
     * 检查模型编码是否存在
     */
    boolean existsByModelCode(String modelCode);

    /**
     * 统计某个提供商的启用模型数量
     */
    long countByProviderIdAndStatus(Long providerId, Integer status);

    /**
     * 统计某个类型的启用模型数量
     */
    long countByModelTypeAndStatus(AiModel.ModelType modelType, Integer status);
}