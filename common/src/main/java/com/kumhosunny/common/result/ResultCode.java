package com.kumhosunny.common.result;

/**
 * 响应状态码枚举
 * 
 * <AUTHOR>
 */
public enum ResultCode {

    // 通用状态码
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    PARAM_ERROR(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),

    // 用户相关
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_DISABLED(1002, "用户已被禁用"),
    USERNAME_EXISTS(1003, "用户名已存在"),
    PASSWORD_ERROR(1004, "密码错误"),
    TOKEN_INVALID(1005, "Token无效"),
    TOKEN_EXPIRED(1006, "Token已过期"),

    // 聊天相关
    CHAT_SESSION_NOT_FOUND(2001, "聊天会话不存在"),
    CHAT_MESSAGE_EMPTY(2002, "聊天消息不能为空"),
    MODEL_NOT_AVAILABLE(2003, "模型不可用"),

    // 知识库相关
    KNOWLEDGE_BASE_NOT_FOUND(3001, "知识库不存在"),
    DOCUMENT_UPLOAD_FAILED(3002, "文档上传失败"),
    DOCUMENT_PARSE_FAILED(3003, "文档解析失败"),
    VECTOR_SEARCH_FAILED(3004, "向量检索失败"),

    // 工具相关
    TOOL_NOT_FOUND(4001, "工具不存在"),
    TOOL_EXECUTION_FAILED(4002, "工具执行失败"),
    CODE_EXECUTION_FAILED(4003, "代码执行失败"),
    N8N_CALL_FAILED(4004, "N8N调用失败");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态消息
     */
    private final String message;

    /**
     * 构造函数
     */
    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 获取状态码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取状态消息
     */
    public String getMessage() {
        return message;
    }
}