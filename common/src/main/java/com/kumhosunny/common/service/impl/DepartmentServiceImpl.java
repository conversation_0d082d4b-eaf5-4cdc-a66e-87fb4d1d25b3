package com.kumhosunny.common.service.impl;

import com.kumhosunny.common.entity.Department;
import com.kumhosunny.common.entity.EmployeeEntity;
import com.kumhosunny.common.exception.BusinessException;
import com.kumhosunny.common.repository.DepartmentRepository;
import com.kumhosunny.common.repository.EmployeeRepository;
import com.kumhosunny.common.service.DepartmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 部门服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class DepartmentServiceImpl implements DepartmentService {

    @Autowired
    private DepartmentRepository departmentRepository;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Override
    public Department createDepartment(Department department) {
        log.info("创建部门: {}", department.getDepartmentName());
        
        // 验证和保存部门
        validateDepartment(department);
        setDefaultValues(department);
        
        Department saved = departmentRepository.save(department);
        log.info("部门创建成功: ID={}, 名称={}", saved.getDepartmentId(), saved.getDepartmentName());
        
        return saved;
    }



    @Override
    public Optional<Department> findById(Long departmentId) {
        return departmentRepository.findById(departmentId);
    }

    @Override
    public Optional<Department> findByName(String departmentName) {
        return departmentRepository.findByDepartmentName(departmentName);
    }

    @Override
    public List<Department> searchByName(String departmentName) {
        return departmentRepository.findByDepartmentNameContaining(departmentName);
    }

    @Override
    public List<Department> findAll() {
        return departmentRepository.findAll();
    }

    @Override
    public List<Department> findRootDepartments() {
        return departmentRepository.findRootDepartments();
    }

    @Override
    public List<Department> findChildDepartments(Long parentId) {
        return departmentRepository.findByParentIdOrderBySortAsc(parentId);
    }

    @Override
    public List<Department> getDepartmentTree() {
        List<Department> allDepartments = findAll();
        return buildDepartmentTree(allDepartments, 0L);
    }

    @Override
    public Department getDepartmentTreeById(Long departmentId) {
        Optional<Department> rootDept = findById(departmentId);
        if (!rootDept.isPresent()) {
            return null;
        }
        
        List<Department> allDepartments = findAll();
        List<Department> tree = buildDepartmentTree(allDepartments, departmentId);
        
        return tree.isEmpty() ? null : tree.get(0);
    }

    @Override
    public List<Department> getAllChildDepartments(Long departmentId) {
        return departmentRepository.findAllChildrenDepartments(departmentId);
    }

    @Override
    public List<Department> getAllParentDepartments(Long departmentId) {
        return departmentRepository.findAllParentDepartments(departmentId);
    }

    public List<Long> getAllParentDepartentIds(Long departmentId){
        List<Department> parentDepartments = getAllParentDepartments(departmentId);
        return parentDepartments.stream()
                .map(Department::getDepartmentId)
                .collect(Collectors.toList());
    }

    @Override
    public Long getTopParentDepartmentId(Long departmentId) {
        if (departmentId == null) {
            return null;
        }
        
        Long topParentId = departmentRepository.findTopParentDepartmentId(departmentId);
        // 如果查询结果为空，说明传入的就是顶级部门
        return topParentId != null ? topParentId : departmentId;
    }

    @Override
    public List<Long> getTopParentAndAllChildrenIds(Long departmentId) {
        if (departmentId == null) {
            return new ArrayList<>();
        }
        
        // 1. 获取顶级父部门ID
        Long topParentId = getTopParentDepartmentId(departmentId);
        
        // 2. 获取顶级父部门及其所有子部门的ID列表
        return getDepartmentAndChildrenIds(topParentId);
    }

    @Override
    public List<Long> getDepartmentAndChildrenIds(Long departmentId) {
        return departmentRepository.findDepartmentAndChildrenIds(departmentId);
    }



    @Override
    public List<EmployeeEntity> getDepartmentEmployees(Long departmentId, boolean includeChildren) {
        if (includeChildren) {
            List<Long> deptIds = getDepartmentAndChildrenIds(departmentId);
            return employeeRepository.findByDepartmentIdIn(deptIds);
        } else {
            return employeeRepository.findByDepartmentId(departmentId);
        }
    }

    @Override
    public long countDepartmentEmployees(Long departmentId, boolean includeChildren) {
        return getDepartmentEmployees(departmentId, includeChildren).size();
    }

    @Override
    public String getDepartmentFullPath(Long departmentId) {
        List<Department> parents = getAllParentDepartments(departmentId);
        Optional<Department> current = findById(departmentId);
        
        if (!current.isPresent()) {
            return "";
        }
        
        List<String> names = new ArrayList<>();
        for (Department parent : parents) {
            names.add(parent.getDepartmentName());
        }
        names.add(current.get().getDepartmentName());
        
        return String.join(" > ", names);
    }

    @Override
    public int getDepartmentLevel(Long departmentId) {
        return getAllParentDepartments(departmentId).size();
    }

    @Override
    public boolean isDepartmentNameExists(String departmentName) {
        return departmentRepository.existsByDepartmentNameAndIdNot(departmentName, null);
    }

    @Override
    public boolean isAncestor(Long ancestorId, Long descendantId) {
        List<Long> parentIds = getDepartmentAndChildrenIds(ancestorId);
        return parentIds.contains(descendantId);
    }

    @Override
    public List<Department> getDepartmentsByLevel(int level) {
        return departmentRepository.findByLevel(level);
    }

    @Override
    public List<Department> getDepartmentsWithEmployees() {
        return departmentRepository.findDepartmentsWithEmployees();
    }

    @Override
    public List<Department> getDepartmentsWithoutEmployees() {
        return departmentRepository.findDepartmentsWithoutEmployees();
    }

    @Override
    public Department enrichDepartmentInfo(Department department) {
        if (department == null) {
            return null;
        }
        
        // 填充负责人信息
        if (department.getManagerId() != null) {
            Optional<EmployeeEntity> manager = employeeRepository.findById(department.getManagerId());
            manager.ifPresent(department::setManager);
        }
        
        // 填充员工数量
        long employeeCount = countDepartmentEmployees(department.getDepartmentId(), false);
        department.setEmployeeCount(employeeCount);
        
        // 填充层级信息
        int level = getDepartmentLevel(department.getDepartmentId());
        department.setLevel(level);
        
        // 填充完整路径
        String fullPath = getDepartmentFullPath(department.getDepartmentId());
        department.setFullPath(fullPath);
        
        return department;
    }

    @Override
    public List<Department> enrichDepartmentInfoBatch(List<Department> departments) {
        return departments.stream()
                .map(this::enrichDepartmentInfo)
                .collect(Collectors.toList());
    }

    // 私有辅助方法
    private void validateDepartment(Department department) {
        if (!StringUtils.hasText(department.getDepartmentName())) {
            throw new BusinessException("部门名称不能为空");
        }
        
        if (isDepartmentNameExists(department.getDepartmentName())) {
            throw new BusinessException("部门名称已存在: " + department.getDepartmentName());
        }
    }

    private void setDefaultValues(Department department) {
        if (department.getParentId() == null) {
            department.setParentId(0L);
        }
        if (department.getSort() == null) {
            department.setSort(999);
        }
    }

    private List<Department> buildDepartmentTree(List<Department> allDepartments, Long parentId) {
        return allDepartments.stream()
                .filter(dept -> Objects.equals(dept.getParentId(), parentId))
                .sorted(Comparator.comparing(Department::getSort))
                .map(dept -> {
                    dept.setChildren(buildDepartmentTree(allDepartments, dept.getDepartmentId()));
                    return dept;
                })
                .collect(Collectors.toList());
    }
} 