package com.kumhosunny.common.service;

import com.kumhosunny.common.entity.Department;
import com.kumhosunny.common.entity.EmployeeEntity;

import java.util.List;
import java.util.Optional;

/**
 * 部门服务接口
 * 
 * <AUTHOR>
 */
public interface DepartmentService {

    /**
     * 创建部门
     * 
     * @param department 部门信息
     * @return 创建的部门
     */
    Department createDepartment(Department department);



    /**
     * 根据ID查找部门
     * 
     * @param departmentId 部门ID
     * @return 部门信息
     */
    Optional<Department> findById(Long departmentId);

    /**
     * 根据部门名称查找部门
     * 
     * @param departmentName 部门名称
     * @return 部门信息
     */
    Optional<Department> findByName(String departmentName);

    /**
     * 根据部门名称模糊查询
     * 
     * @param departmentName 部门名称关键字
     * @return 部门列表
     */
    List<Department> searchByName(String departmentName);

    /**
     * 获取所有部门
     * 
     * @return 部门列表
     */
    List<Department> findAll();

    /**
     * 获取根部门列表
     * 
     * @return 根部门列表
     */
    List<Department> findRootDepartments();

    /**
     * 获取指定部门的子部门列表
     * 
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<Department> findChildDepartments(Long parentId);

    /**
     * 获取部门树结构
     * 
     * @return 完整的部门树
     */
    List<Department> getDepartmentTree();

    /**
     * 获取指定部门的部门树（包含子部门）
     * 
     * @param departmentId 部门ID
     * @return 部门树
     */
    Department getDepartmentTreeById(Long departmentId);

    /**
     * 获取指定部门的所有子部门（平铺列表）
     * 
     * @param departmentId 部门ID
     * @return 所有子部门列表
     */
    List<Department> getAllChildDepartments(Long departmentId);

    /**
     * 获取指定部门的所有父级部门
     * 
     * @param departmentId 部门ID
     * @return 父级部门列表（从根部门到直接父部门）
     */
    List<Department> getAllParentDepartments(Long departmentId);

    /**
     * 获取指定部门及其所有子部门的ID列表
     * 
     * @param departmentId 部门ID
     * @return 部门ID列表
     */
    List<Long> getDepartmentAndChildrenIds(Long departmentId);



    /**
     * 获取部门的员工列表
     * 
     * @param departmentId 部门ID
     * @param includeChildren 是否包含子部门的员工
     * @return 员工列表
     */
    List<EmployeeEntity> getDepartmentEmployees(Long departmentId, boolean includeChildren);

    /**
     * 统计部门员工数量
     * 
     * @param departmentId 部门ID
     * @param includeChildren 是否包含子部门的员工
     * @return 员工数量
     */
    long countDepartmentEmployees(Long departmentId, boolean includeChildren);

    /**
     * 获取部门的完整路径
     * 
     * @param departmentId 部门ID
     * @return 部门完整路径（如：总公司 > 技术部 > 开发组）
     */
    String getDepartmentFullPath(Long departmentId);

    /**
     * 获取部门层级深度
     * 
     * @param departmentId 部门ID
     * @return 层级深度（根部门为0）
     */
    int getDepartmentLevel(Long departmentId);

    /**
     * 检查部门名称是否已存在
     * 
     * @param departmentName 部门名称
     * @return 是否存在
     */
    boolean isDepartmentNameExists(String departmentName);

    /**
     * 检查部门A是否是部门B的祖先
     * 
     * @param ancestorId 祖先部门ID
     * @param descendantId 后代部门ID
     * @return 是否是祖先关系
     */
    boolean isAncestor(Long ancestorId, Long descendantId);

    /**
     * 获取指定层级的部门列表
     * 
     * @param level 层级（0为根部门）
     * @return 部门列表
     */
    List<Department> getDepartmentsByLevel(int level);

    /**
     * 获取有员工的部门列表
     * 
     * @return 部门列表
     */
    List<Department> getDepartmentsWithEmployees();

    /**
     * 获取没有员工的部门列表
     * 
     * @return 部门列表
     */
    List<Department> getDepartmentsWithoutEmployees();

    /**
     * 同步部门信息（填充额外字段）
     * 
     * @param department 部门
     * @return 填充后的部门信息
     */
    Department enrichDepartmentInfo(Department department);

    /**
     * 批量同步部门信息
     * 
     * @param departments 部门列表
     * @return 填充后的部门列表
     */
    List<Department> enrichDepartmentInfoBatch(List<Department> departments);

    /**
     * 获取指定部门的顶级父部门ID
     * 
     * @param departmentId 部门ID
     * @return 顶级父部门ID
     */
    Long getTopParentDepartmentId(Long departmentId);

    /**
     * 根据departmentId查询顶级父id，再根据父id查询部门所有的子集id（包含顶级父部门自身）
     * 
     * @param departmentId 部门ID
     * @return 顶级父部门及其所有子部门的ID列表
     */
    List<Long> getTopParentAndAllChildrenIds(Long departmentId);
} 