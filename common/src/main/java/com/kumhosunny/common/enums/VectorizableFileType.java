package com.kumhosunny.common.enums;

public enum VectorizableFileType {

    TXT("txt"),
    CSV("csv"),
    JSON("json"),
    DOCX("docx"),
    DOC("doc"),
    PDF("pdf"),
    MD("md"),
    PPT("ppt"),
    PPTX("pptx");

    private final String extension;

    VectorizableFileType(String extension) {
        this.extension = extension;
    }

    public String getExtension() {
        return extension;
    }

    /**
     * 根据文件扩展名获取对应的枚举类型
     * 
     * @param extension 文件扩展名
     * @return 对应的枚举类型，如果不存在则返回null
     */
    public static VectorizableFileType fromExtension(String extension) {
        for (VectorizableFileType fileType : VectorizableFileType.values()) {
            if (fileType.getExtension().equalsIgnoreCase(extension)) {
                return fileType;
            }
        }
        return null;
    }

}
