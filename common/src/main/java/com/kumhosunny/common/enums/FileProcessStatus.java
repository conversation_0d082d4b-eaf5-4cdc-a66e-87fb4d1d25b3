package com.kumhosunny.common.enums;

/**
 * 文件处理状态枚举
 * 定义文件从上传到处理完成的各个阶段状态
 * 
 * <AUTHOR>
 */
public enum FileProcessStatus {
    
    /**
     * 上传中 - 文件正在上传到OSS
     */
    UPLOADING("uploading", "上传中"),
    
    /**
     * 已上传 - 文件已成功上传到OSS，等待处理
     */
    UPLOADED("uploaded", "已上传"),
    
    /**
     * 解析中 - 正在解析文件内容
     */
    PARSING("parsing", "解析中"),
    
    /**
     * 已解析 - 文件内容解析完成，等待向量化
     */
    PARSED("parsed", "已解析"),
    
    /**
     * 向量化中 - 正在进行向量化处理
     */
    VECTORIZING("vectorizing", "向量化中"),
    
    /**
     * 处理完成 - 所有处理步骤已完成
     */
    COMPLETED("completed", "处理完成"),
    
    /**
     * 处理失败 - 处理过程中发生错误
     */
    FAILED("failed", "处理失败");
    
    private final String code;
    private final String description;
    
    FileProcessStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取状态枚举
     */
    public static FileProcessStatus fromCode(String code) {
        for (FileProcessStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为处理中状态
     */
    public boolean isProcessing() {
        return this == UPLOADING || this == PARSING || this == VECTORIZING;
    }
    
    /**
     * 判断是否为最终状态（完成或失败）
     */
    public boolean isFinal() {
        return this == COMPLETED || this == FAILED;
    }
    
    /**
     * 判断是否为成功状态
     */
    public boolean isSuccess() {
        return this == COMPLETED;
    }
    
    /**
     * 判断是否为失败状态
     */
    public boolean isFailure() {
        return this == FAILED;
    }
}
