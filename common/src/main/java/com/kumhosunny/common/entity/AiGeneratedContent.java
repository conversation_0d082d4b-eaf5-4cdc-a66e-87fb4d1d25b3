package com.kumhosunny.common.entity;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * AI生成内容（图片/视频）实体类
 */
@Data
@Entity
@Table(name = "ai_generated")
@EntityListeners(AuditingEntityListener.class)
@Accessors(chain = true)
public class AiGeneratedContent {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "content_id", nullable = false, unique = true)
    private String contentId;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "content_type", nullable = false)
    private String contentType;

    @Column(name = "generation_status", nullable = false)
    private String generationStatus = "COMPLETED";

    @Column(columnDefinition = "TEXT")
    private String prompt;

    @Column(name = "revised_prompt", columnDefinition = "TEXT")
    private String revisedPrompt;

    @Column(name = "model_used")
    private String modelUsed;

    @Column
    private String size;

    @Column
    private String style;

    @Column
    private String quality;

    @Column(name = "oss_url", nullable = false)
    private String ossUrl;

    @Column(name = "thumbnail_url")
    private String thumbnailUrl;

    @Column(name = "response_format")
    private String responseFormat;

    @Column(name = "is_public", nullable = false)
    private Boolean isPublic = false;

    @Column(name = "is_featured", nullable = false)
    private Boolean isFeatured = false;

    @Column(name = "view_count", nullable = false)
    private Long viewCount = 0L;

    @Column(name = "like_count", nullable = false)
    private Long likeCount = 0L;

    @Column(name = "status", nullable = false)
    private Integer status = 0; // 0:正常 -1:删除

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
}