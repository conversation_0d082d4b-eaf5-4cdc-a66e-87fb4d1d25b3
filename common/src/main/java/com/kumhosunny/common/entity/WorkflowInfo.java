package com.kumhosunny.common.entity;

/**
 * N8n 工作流信息实体
 */
public class WorkflowInfo {
    private String id;
    private String name;
    private boolean active;
    private String createdAt;
    private String updatedAt;
    private int triggerCount;
    private boolean isArchived;
    private String webhookUrl;

    // 构造函数
    public WorkflowInfo() {
        // 默认构造函数
    }

    public WorkflowInfo(String id, String name, boolean active, String createdAt,
            String updatedAt, int triggerCount, boolean isArchived, String webhookUrl) {
        this.id = id;
        this.name = name;
        this.active = active;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.triggerCount = triggerCount;
        this.isArchived = isArchived;
        this.webhookUrl = webhookUrl;
    }

    // Getter 和 Setter 方法
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    public int getTriggerCount() {
        return triggerCount;
    }

    public void setTriggerCount(int triggerCount) {
        this.triggerCount = triggerCount;
    }

    public boolean isArchived() {
        return isArchived;
    }

    public void setArchived(boolean archived) {
        isArchived = archived;
    }

    public String getWebhookUrl() {
        return webhookUrl;
    }

    public void setWebhookUrl(String webhookUrl) {
        this.webhookUrl = webhookUrl;
    }

    // toString 方法便于显示
    @Override
    public String toString() {
        return String.format("WorkflowInfo{id='%s', name='%s', active=%s, triggerCount=%d}",
                id, name, active, triggerCount);
    }
}