package com.kumhosunny.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import javax.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 操作记录实体类
 *
 * <AUTHOR> 罗伊
 * @Date 2021-12-08 20:48:52
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright  <a href="https://1024lab.net">1024创新实验室</a>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_operate_log")
@EntityListeners(AuditingEntityListener.class)
public class OperateLogEntity {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "operate_log_id")
    private Long operateLogId;

    /**
     * 操作人id
     */
    @Column(name = "operate_user_id")
    private Long operateUserId;

    /**
     * 用户类型
     */
    @Column(name = "operate_user_type")
    private Integer operateUserType;

    /**
     * 操作人名称
     */
    @Column(name = "operate_user_name")
    private String operateUserName;

    /**
     * 操作模块
     */
    @Column(name = "module")
    private String module;

    /**
     * 操作内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 请求路径
     */
    @Column(name = "url")
    private String url;

    /**
     * 请求方法
     */
    @Column(name = "method")
    private String method;

    /**
     * 请求参数
     */
    @Column(name = "param", columnDefinition = "TEXT")
    private String param;

    /**
     * 客户ip
     */
    @Column(name = "ip")
    private String ip;

    /**
     * 客户ip地区
     */
    @Column(name = "ip_region")
    private String ipRegion;

    /**
     * user-agent
     */
    @Column(name = "user_agent", columnDefinition = "TEXT")
    private String userAgent;

    /**
     * 请求结果 0失败 1成功
     */
    @Column(name = "success_flag", nullable = false, columnDefinition = "TINYINT(1)")
    private Boolean successFlag = true;

    /**
     * 失败原因
     */
    @Column(name = "fail_reason", columnDefinition = "TEXT")
    private String failReason;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;
} 