package com.kumhosunny.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 聊天消息实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Entity
@Table(name = "chat_messages")
public class ChatMessage {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 会话ID
     */
    @Column(name = "session_id", columnDefinition = "CHAR(36)")
    private String sessionId;

    /**
     * 消息发送者
     */
    @Enumerated(EnumType.STRING)
    private MessageSender sender;

    /**
     * 消息内容
     */
    @Column(columnDefinition = "TEXT")
    private String content;

    /**
     * 内容类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "content_type")
    private ContentType contentType = ContentType.text;

    /**
     * 父消息ID（支持多轮threading）
     */
    @Column(name = "parent_message_id")
    private Long parentMessageId;

    /**
     * Token数量
     */
    @Column(name = "token_count")
    private Integer tokenCount = 0;

    /**
     * 元数据，存储附件、Agent调用、代码执行、预览引用等
     */
    @Column(columnDefinition = "JSON")
    private String metadata;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    /**
     * 消息发送者枚举
     */
    public enum MessageSender {
        user, assistant, agent
    }

    /**
     * 内容类型枚举
     */
    public enum ContentType {
        text, code, file, agent_call
    }

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
}