package com.kumhosunny.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 部门实体类
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_department")
@EntityListeners(AuditingEntityListener.class)
public class Department {

    /**
     * 部门主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "department_id")
    private Long departmentId;

    /**
     * 部门名称
     */
    @Column(name = "department_name", nullable = false, length = 50)
    private String departmentName;

    /**
     * 部门负责人id
     */
    @Column(name = "manager_id")
    private Long managerId;

    /**
     * 部门的父级id
     */
    @Column(name = "parent_id", nullable = false)
    private Long parentId = 0L;

    /**
     * 部门排序
     */
    @Column(name = "sort", nullable = false)
    private Integer sort = 999;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;

    /**
     * 子部门列表（非数据库字段）
     */
    @Transient
    private List<Department> children;

    /**
     * 父部门（非数据库字段）
     */
    @Transient
    private Department parent;

    /**
     * 部门负责人信息（非数据库字段）
     */
    @Transient
    private EmployeeEntity manager;

    /**
     * 部门员工数量（非数据库字段）
     */
    @Transient
    private Long employeeCount;

    /**
     * 部门层级深度（非数据库字段）
     */
    @Transient
    private Integer level;

    /**
     * 部门完整路径（非数据库字段）
     */
    @Transient
    private String fullPath;

    // 构造函数
    public Department() {
    }

    public Department(String departmentName, Long parentId) {
        this.departmentName = departmentName;
        this.parentId = parentId;
    }

    public Department(String departmentName, Long parentId, Long managerId) {
        this.departmentName = departmentName;
        this.parentId = parentId;
        this.managerId = managerId;
    }

    /**
     * 是否为根部门
     */
    public boolean isRoot() {
        return parentId == null || parentId == 0L;
    }

    /**
     * 是否有子部门
     */
    public boolean hasChildren() {
        return children != null && !children.isEmpty();
    }

    /**
     * 获取部门ID（兼容方法）
     */
    public Long getId() {
        return departmentId;
    }

    /**
     * 设置部门ID（兼容方法）
     */
    public void setId(Long id) {
        this.departmentId = id;
    }
} 