package com.kumhosunny.common.entity;

import lombok.Data;
import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * AI模型路由配置实体类
 */
@Entity
@Table(name = "ai_model_routes")
public class AiModelRoute {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "request_model", nullable = false, length = 64)
    private String requestModel;

    @Column(name = "target_model_id", nullable = false)
    private Long targetModelId;

    @Column(name = "priority", nullable = false)
    private Integer priority = 0;

    @Column(name = "status", nullable = false)
    private Integer status = 1; // 0-禁用，1-启用

    @Column(name = "created_time", nullable = false, updatable = false)
    private LocalDateTime createdTime;

    @Column(name = "updated_time", nullable = false)
    private LocalDateTime updatedTime;

    // 关联的目标模型信息
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "target_model_id", insertable = false, updatable = false)
    private AiModel targetModel;

    // Constructors
    public AiModelRoute() {
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRequestModel() {
        return requestModel;
    }

    public void setRequestModel(String requestModel) {
        this.requestModel = requestModel;
    }

    public Long getTargetModelId() {
        return targetModelId;
    }

    public void setTargetModelId(Long targetModelId) {
        this.targetModelId = targetModelId;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    public AiModel getTargetModel() {
        return targetModel;
    }

    public void setTargetModel(AiModel targetModel) {
        this.targetModel = targetModel;
    }

    public String getRouteName() {
        return requestModel + " -> " + (targetModel != null ? targetModel.getModelName() : "Unknown");
    }
}