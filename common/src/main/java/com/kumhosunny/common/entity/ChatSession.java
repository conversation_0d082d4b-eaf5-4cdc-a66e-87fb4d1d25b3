package com.kumhosunny.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 聊天会话实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Entity
@Table(name = "chat_sessions")
public class ChatSession {

    /**
     * 主键ID
     */
    @Id
    @Column(columnDefinition = "CHAR(36)")
    private String id;

    /**
     * 员工ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 会话标题
     */
    @Column(name = "session_title")
    private String sessionTitle;

    /**
     * 使用的模型
     */
    @Column(name = "model_used")
    private String modelUsed;

    /**
     * 会话状态
     */
    @Enumerated(EnumType.STRING)
    private SessionStatus status = SessionStatus.active;

    /**
     * 元数据，存储会话配置信息
     */
    @Column(columnDefinition = "JSON")
    private String metadata;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 关联的聊天消息列表（用于查询时带出对话记录）
     * 使用@JsonIgnore防止序列化时触发懒加载
     */
    @OneToMany(mappedBy = "sessionId", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JsonIgnore
    private List<ChatMessage> messages;

    /**
     * 获取消息列表
     */
    public List<ChatMessage> getMessages() {
        return messages;
    }

    /**
     * 设置消息列表
     */
    public void setMessages(List<ChatMessage> messages) {
        this.messages = messages;
    }

    /**
     * 设置用户ID
     */
    public ChatSession setUserId(Long userId) {
        this.userId = userId;
        return this;
    }

    /**
     * 设置会话标题
     */
    public ChatSession setSessionTitle(String sessionTitle) {
        this.sessionTitle = sessionTitle;
        return this;
    }

    /**
     * 设置使用的模型
     */
    public ChatSession setModelUsed(String modelUsed) {
        this.modelUsed = modelUsed;
        return this;
    }

    /**
     * 设置元数据
     */
    public ChatSession setMetadata(String metadata) {
        this.metadata = metadata;
        return this;
    }

    /**
     * 设置状态
     */
    public ChatSession setStatus(SessionStatus status) {
        this.status = status;
        return this;
    }

    /**
     * 设置更新时间
     */
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * 会话状态枚举
     */
    public enum SessionStatus {
        active, archived
    }

    @PrePersist
    protected void onCreate() {
        if (id == null) {
            id = UUID.randomUUID().toString();
        }
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}