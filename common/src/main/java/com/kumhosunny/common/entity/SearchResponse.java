package com.kumhosunny.common.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * 新搜索接口响应实体
 * 
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class SearchResponse {

    private String query;

    @JsonProperty("number_of_results")
    private int numberOfResults;

    private List<SearchResult> results;

    private List<String> answers;

    private List<String> corrections;

    private List<String> infoboxes;

    private List<String> suggestions;

    @JsonProperty("unresponsive_engines")
    private List<List<String>> unresponsiveEngines;

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public int getNumberOfResults() {
        return numberOfResults;
    }

    public void setNumberOfResults(int numberOfResults) {
        this.numberOfResults = numberOfResults;
    }

    public List<SearchResult> getResults() {
        return results;
    }

    public void setResults(List<SearchResult> results) {
        this.results = results;
    }

    public List<String> getAnswers() {
        return answers;
    }

    public void setAnswers(List<String> answers) {
        this.answers = answers;
    }

    public List<String> getCorrections() {
        return corrections;
    }

    public void setCorrections(List<String> corrections) {
        this.corrections = corrections;
    }

    public List<String> getInfoboxes() {
        return infoboxes;
    }

    public void setInfoboxes(List<String> infoboxes) {
        this.infoboxes = infoboxes;
    }

    public List<String> getSuggestions() {
        return suggestions;
    }

    public void setSuggestions(List<String> suggestions) {
        this.suggestions = suggestions;
    }

    public List<List<String>> getUnresponsiveEngines() {
        return unresponsiveEngines;
    }

    public void setUnresponsiveEngines(List<List<String>> unresponsiveEngines) {
        this.unresponsiveEngines = unresponsiveEngines;
    }

    /**
     * 搜索结果项
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SearchResult {
        private String title;
        private String url;
        private String content;

        @JsonProperty("publishedDate")
        private String publishedDate;

        private String engine;
        private String template;

        @JsonProperty("parsed_url")
        private List<String> parsedUrl;

        @JsonProperty("img_src")
        private String imgSrc;

        private String thumbnail;
        private String priority;
        private List<String> engines;
        private List<Integer> positions;
        private Double score;
        private String category;

        @JsonProperty("iframe_src")
        private String iframeSrc;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getPublishedDate() {
            return publishedDate;
        }

        public void setPublishedDate(String publishedDate) {
            this.publishedDate = publishedDate;
        }

        public String getEngine() {
            return engine;
        }

        public void setEngine(String engine) {
            this.engine = engine;
        }

        public String getTemplate() {
            return template;
        }

        public void setTemplate(String template) {
            this.template = template;
        }

        public List<String> getParsedUrl() {
            return parsedUrl;
        }

        public void setParsedUrl(List<String> parsedUrl) {
            this.parsedUrl = parsedUrl;
        }

        public String getImgSrc() {
            return imgSrc;
        }

        public void setImgSrc(String imgSrc) {
            this.imgSrc = imgSrc;
        }

        public String getThumbnail() {
            return thumbnail;
        }

        public void setThumbnail(String thumbnail) {
            this.thumbnail = thumbnail;
        }

        public String getPriority() {
            return priority;
        }

        public void setPriority(String priority) {
            this.priority = priority;
        }

        public List<String> getEngines() {
            return engines;
        }

        public void setEngines(List<String> engines) {
            this.engines = engines;
        }

        public List<Integer> getPositions() {
            return positions;
        }

        public void setPositions(List<Integer> positions) {
            this.positions = positions;
        }

        public Double getScore() {
            return score;
        }

        public void setScore(Double score) {
            this.score = score;
        }

        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        public String getIframeSrc() {
            return iframeSrc;
        }

        public void setIframeSrc(String iframeSrc) {
            this.iframeSrc = iframeSrc;
        }
    }
}