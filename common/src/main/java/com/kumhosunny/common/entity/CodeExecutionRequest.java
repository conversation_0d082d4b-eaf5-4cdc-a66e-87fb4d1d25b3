package com.kumhosunny.common.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 代码执行请求实体
 * 
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CodeExecutionRequest {

    @JsonProperty("language_id")
    private Integer languageId;

    @JsonProperty("source_code")
    private String sourceCode;

    @JsonProperty("stdin")
    private String stdin;

    @JsonProperty("expected_output")
    private String expectedOutput;

    public CodeExecutionRequest() {
    }

    public CodeExecutionRequest(Integer languageId, String sourceCode) {
        this.languageId = languageId;
        this.sourceCode = sourceCode;
    }

    public CodeExecutionRequest(Integer languageId, String sourceCode, String stdin) {
        this.languageId = languageId;
        this.sourceCode = sourceCode;
        this.stdin = stdin;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getStdin() {
        return stdin;
    }

    public void setStdin(String stdin) {
        this.stdin = stdin;
    }

    public String getExpectedOutput() {
        return expectedOutput;
    }

    public void setExpectedOutput(String expectedOutput) {
        this.expectedOutput = expectedOutput;
    }

    @Override
    public String toString() {
        return String.format("CodeExecutionRequest{languageId=%d, sourceCode='%s', stdin='%s'}",
                languageId, sourceCode, stdin);
    }
}