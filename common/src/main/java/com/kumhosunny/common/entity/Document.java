package com.kumhosunny.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 文档元数据实体类
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "documents")
@EntityListeners(AuditingEntityListener.class)
public class Document {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 文档标题
     */
    @Column(nullable = false)
    private String title;

    /**
     * 文档原始全文内容（可选保留）
     */
    @Column(columnDefinition = "TEXT")
    private String content;

    /**
     * 文档内容的SHA-256哈希值，用于去重
     */
    @Column(name = "content_hash", length = 64)
    private String contentHash;

    /**
     * 文档类型：个人/部门/公司
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private DocumentType type;

    /**
     * 文档创建者（用于个人文档权限）
     */
    @Column(name = "owner_id", nullable = false)
    private Long ownerId;


    @Column(name = "path")
    private String path;
    /**
     * 文件id
     */
    @Column(name = "file_id")
    private Long fileId;
    /**
     * 所属部门ID（用于部门文档权限）
     */
    @Column(name = "department_id")
    private Long departmentId;

    /**
     * 是否公司公开文档
     */
    @Column(name = "is_public", nullable = false, columnDefinition = "TINYINT(1)")
    private Boolean isPublic = false;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // 注意：关联关系可以在需要时通过Repository查询获取，避免循环依赖
    // 如需要可以通过DocumentChunkRepository和DocumentPermissionRepository查询

    /**
     * 文档类型枚举
     */
    public enum DocumentType {
        personal, department, company,legal
    }

    // 构造函数
    public Document() {
    }

    public Document(String title, String content, DocumentType type, Long ownerId) {
        this.title = title;
        this.content = content;
        this.type = type;
        this.ownerId = ownerId;
        this.isPublic = false;
    }
} 