package com.kumhosunny.common.entity;

import com.kumhosunny.common.enums.FileProcessStatus;
import lombok.Data;

import javax.persistence.*;

/**
 * @program: kum-ai-app-backend
 * @description:
 * @author: wye
 * @create: 2025-06-18 15:43
 **/

@Data
@Entity
@Table(name = "ai_files")
public class AiFiles {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 源文件名称
     */
    private String originalFileName;
    private String bucketName;
    /**
     * 文件类型
     */
    private String fileType;
    /**
     * 文件大小
     */
    private String fileSize;
    /**
     * 文件路径
     */
    private String filePath;
    private String path;
    private String uploadTime;
    private String userId;
    /**
     * 模型id
     */
    private String aiModelId;
    /**
     * 向量id
     */
    private String vectorChunks;
    /**
     * 文件处理状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "ai_status")
    private FileProcessStatus aiStatus = FileProcessStatus.UPLOADING;

    /**
     * 处理结果信息
     */
    private String aiResult;

    /**
     * 错误信息（当状态为FAILED时记录具体错误）
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    /**
     * 文件内容
     */
    private String textContent;
}
