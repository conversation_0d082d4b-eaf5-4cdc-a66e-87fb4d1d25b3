package com.kumhosunny.common.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;

/**
 * DuckDuckGo搜索结果响应实体
 * 
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DuckDuckGoResponse {

    private String code;
    private Data data;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    /**
     * 数据容器
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Data {
        private List<SearchResult> results;

        public List<SearchResult> getResults() {
            return results;
        }

        public void setResults(List<SearchResult> results) {
            this.results = results;
        }
    }

    /**
     * 搜索结果项
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SearchResult {
        private int order;
        private String title;
        private String url;
        private String description;

        public int getOrder() {
            return order;
        }

        public void setOrder(int order) {
            this.order = order;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
    }
}