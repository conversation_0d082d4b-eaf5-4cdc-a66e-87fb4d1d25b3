package com.kumhosunny.common.entity;

import java.util.List;

/**
 * 工作流列表响应实体
 */
public class WorkflowListResponse {

    /**
     * 工作流列表
     */
    private List<WorkflowInfo> workflows;

    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 激活数量
     */
    private Integer activeCount;

    /**
     * 停用数量
     */
    private Integer inactiveCount;

    /**
     * 默认构造函数
     */
    public WorkflowListResponse() {
    }

    /**
     * 全参数构造函数
     */
    public WorkflowListResponse(List<WorkflowInfo> workflows, Integer totalCount, Integer activeCount,
            Integer inactiveCount) {
        this.workflows = workflows;
        this.totalCount = totalCount;
        this.activeCount = activeCount;
        this.inactiveCount = inactiveCount;
    }

    /**
     * 简单构造函数，只包含工作流列表
     */
    public WorkflowListResponse(List<WorkflowInfo> workflows) {
        this.workflows = workflows;
        this.totalCount = workflows != null ? workflows.size() : 0;
        this.activeCount = workflows != null ? (int) workflows.stream().filter(WorkflowInfo::isActive).count() : 0;
        this.inactiveCount = totalCount - activeCount;
    }

    // Getter 和 Setter 方法
    public List<WorkflowInfo> getWorkflows() {
        return workflows;
    }

    public void setWorkflows(List<WorkflowInfo> workflows) {
        this.workflows = workflows;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getActiveCount() {
        return activeCount;
    }

    public void setActiveCount(Integer activeCount) {
        this.activeCount = activeCount;
    }

    public Integer getInactiveCount() {
        return inactiveCount;
    }

    public void setInactiveCount(Integer inactiveCount) {
        this.inactiveCount = inactiveCount;
    }

    @Override
    public String toString() {
        return String.format("WorkflowListResponse{total=%d, active=%d, inactive=%d}",
                totalCount, activeCount, inactiveCount);
    }
}