package com.kumhosunny.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 员工实体类
 *
 * <AUTHOR> 卓大
 * @Date 2021-12-09 22:57:49
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_employee")
@EntityListeners(AuditingEntityListener.class)
public class EmployeeEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "employee_id")
    private Long employeeId;

    @Column(name = "union_id")
    private String unionId;

    /**
     * 登录账号
     */
    @Column(name = "login_name")
    private String loginName;

    /**
     * 登录密码
     */
    @Column(name = "login_pwd")
    private String loginPwd;

    /**
     * 员工名称
     */
    @Column(name = "actual_name")
    private String actualName;

    /**
     * 头像
     */
    @Column(name = "avatar")
    private String avatar;

    /**
     * 性别
     */
    @Column(name = "gender")
    private Integer gender;

    /**
     * 手机号码
     */
    @Column(name = "phone")
    private String phone;

    /**
     * 邮箱
     */
    @Column(name = "email")
    private String email;

    /**
     * 部门id
     */
    @Column(name = "department_id")
    private Long departmentId;

    /**
     * 职务级别ID
     */
    @Column(name = "position_id")
    private Long positionId;

    /**
     * 是否为超级管理员: 0 不是，1是
     */
    @Column(name = "administrator_flag", nullable = false, columnDefinition = "TINYINT(1)")
    private Boolean administratorFlag = false;

    /**
     * 是否被禁用 0否1是
     */
    @Column(name = "disabled_flag", nullable = false, columnDefinition = "TINYINT(1)")
    private Boolean disabledFlag = false;

    /**
     * 是否删除0否 1是
     */
    @Column(name = "deleted_flag", nullable = false, columnDefinition = "TINYINT(1)")
    private Boolean deletedFlag = false;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    @LastModifiedDate
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;

    @CreatedDate
    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;
}