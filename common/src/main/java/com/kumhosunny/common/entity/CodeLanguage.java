package com.kumhosunny.common.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 代码执行支持的编程语言实体
 * 
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CodeLanguage {

    private Integer id;
    private String name;

    public CodeLanguage() {
    }

    public CodeLanguage(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return String.format("CodeLanguage{id=%d, name='%s'}", id, name);
    }
}