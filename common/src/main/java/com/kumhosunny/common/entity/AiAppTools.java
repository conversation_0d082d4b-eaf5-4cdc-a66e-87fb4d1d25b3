package com.kumhosunny.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.*;

/**
 * AI应用工具实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Entity
@Table(name = "ai_app_tools")
public class AiAppTools {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 应用ID
     */
    @Column(name = "app_id")
    private Integer appId;

    /**
     * 工具名称
     */
    @Column(name = "tool_name")
    private String toolName;

    /**
     * 工具类型
     */
    @Column(name = "tool_type")
    private String toolType;

    /**
     * 配置信息（JSON格式）
     */
    private String config;
}