package com.kumhosunny.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * AI应用实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Entity
@Table(name = "ai_apps")
public class AiApp {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 应用分类ID
     */
    @Column(name = "category_id")
    private Integer categoryId;

    /**
     * 应用名称
     */
    private String name;

    /**
     * 应用描述
     */
    private String description;

    /**
     * 应用头像
     */
    @Column(name = "avatar")
    private String avatar;

    /**
     * 创建者ID
     */
    private Integer createdBy;

    /**
     * 是否公开
     */
    @Column(name = "is_public")
    private Boolean isPublic;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id", insertable = false, updatable = false)
    private AiAppCategory category;
}