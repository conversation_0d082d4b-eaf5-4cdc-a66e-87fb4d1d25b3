package com.kumhosunny.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.*;

/**
 * AI应用设置实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Entity
@Table(name = "ai_app_settings")
public class AiAppSettings {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 应用ID
     */
    @Column(name = "app_id")
    private Integer appId;

    /**
     * 模型名称
     */
    private String model;

    /**
     * 系统提示词
     */
    @Column(name = "system_prompt")
    private String systemPrompt;

    /**
     * 温度参数
     */
    private Float temperature;
}