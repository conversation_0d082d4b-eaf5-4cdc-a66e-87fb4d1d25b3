package com.kumhosunny.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 文档访问权限配置实体类
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "document_permissions")
@EntityListeners(AuditingEntityListener.class)
public class DocumentPermission {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 目标文档ID
     */
    @Column(name = "document_id", nullable = false)
    private Long documentId;

    /**
     * 具体授权的用户ID（可空）
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 具体授权的部门ID（可空）
     */
    @Column(name = "department_id")
    private Long departmentId;

    /**
     * 权限类型：只读/编辑/管理员
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PermissionType permission;

    /**
     * 授权时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 关联的文档实体
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id", insertable = false, updatable = false)
    private Document document;

    /**
     * 关联的用户实体
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private EmployeeEntity user;

    /**
     * 权限类型枚举
     */
    public enum PermissionType {
        read, write, admin
    }

    // 构造函数
    public DocumentPermission() {
    }

    public DocumentPermission(Long documentId, Long userId, PermissionType permission) {
        this.documentId = documentId;
        this.userId = userId;
        this.permission = permission;
    }

    public DocumentPermission(Long documentId, Long userId, Long departmentId, PermissionType permission) {
        this.documentId = documentId;
        this.userId = userId;
        this.departmentId = departmentId;
        this.permission = permission;
    }

    /**
     * 检查是否为用户权限
     */
    public boolean isUserPermission() {
        return userId != null;
    }

    /**
     * 检查是否为部门权限
     */
    public boolean isDepartmentPermission() {
        return departmentId != null;
    }
} 