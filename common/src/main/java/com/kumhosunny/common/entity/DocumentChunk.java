package com.kumhosunny.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 文档拆分片段实体类
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "document_chunks", 
       uniqueConstraints = @UniqueConstraint(name = "uniq_document_chunk", columnNames = {"document_id", "chunk_index"}))
@EntityListeners(AuditingEntityListener.class)
public class DocumentChunk {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 关联的文档ID
     */
    @Column(name = "document_id", nullable = false)
    private Long documentId;

    /**
     * 段落编号（从0开始）
     */
    @Column(name = "chunk_index", nullable = false)
    private Integer chunkIndex;

    /**
     * 该段文本原文内容
     */
    @Column(columnDefinition = "TEXT", nullable = false)
    private String content;

    /**
     * 该段在向量库中的ID（如 Qdrant 向量ID）
     */
    @Column(name = "vector_id", length = 128)
    private String vectorId;

    /**
     * 向量嵌入状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "embedding_status", nullable = false)
    private EmbeddingStatus embeddingStatus = EmbeddingStatus.pending;

    /**
     * 向量更新时间
     */
    @Column(name = "embedding_updated_at")
    private LocalDateTime embeddingUpdatedAt;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 关联的文档实体
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id", insertable = false, updatable = false)
    private Document document;

    /**
     * 向量嵌入状态枚举
     */
    public enum EmbeddingStatus {
        pending, done, failed
    }

    // 构造函数
    public DocumentChunk() {
    }

    public DocumentChunk(Long documentId, Integer chunkIndex, String content) {
        this.documentId = documentId;
        this.chunkIndex = chunkIndex;
        this.content = content;
        this.embeddingStatus = EmbeddingStatus.pending;
    }

    /**
     * 更新向量嵌入状态
     */
    public void updateEmbeddingStatus(EmbeddingStatus status, String vectorId) {
        this.embeddingStatus = status;
        this.vectorId = vectorId;
        this.embeddingUpdatedAt = LocalDateTime.now();
    }
} 