# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=kumhosunny_ai_app
MYSQL_USER=root
MYSQL_PASSWORD=root
MYSQL_ROOT_PASSWORD=root

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379

# Qdrant 向量数据库配置
QDRANT_HOST=**************
QDRANT_PORT=6334
QDRANT_HTTP_PORT=6333
QDRANT_USE_TLS=false
QDRANT_API_KEY=
QDRANT_TIMEOUT=30000
QDRANT_DEFAULT_VECTOR_SIZE=1536
QDRANT_DEFAULT_DISTANCE=Cosine
QDRANT_DEFAULT_SEARCH_LIMIT=10

# MinIO 对象存储配置
MINIO_HOST=localhost
MINIO_PORT=9000
MINIO_CONSOLE_PORT=9001
MINIO_ACCESS_KEY=kumhosunny
MINIO_SECRET_KEY=kumhosunny123

# ElasticSearch 配置
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200
KIBANA_PORT=5601

# Pyodide 运行环境配置
PYODIDE_PORT=8080

# 应用配置
APP_ENV=development
APP_DEBUG=true
APP_PORT=8000

# AI 模型配置 (供 Java Chat 模块使用)
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_BASE_URL=https://api.openai.com/v1
CLAUDE_API_KEY=your-claude-api-key-here
GEMINI_API_KEY=your-gemini-api-key-here
OLLAMA_BASE_URL=http://localhost:11434 