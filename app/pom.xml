<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.kumhosunny</groupId>
        <artifactId>kumhosunny-ai-app</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>app</artifactId>
    <packaging>jar</packaging>
    <description>应用主模块</description>

    <dependencies>
        <!-- 内部模块依赖 -->
        <dependency>
            <groupId>com.kumhosunny</groupId>
            <artifactId>common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kumhosunny</groupId>
            <artifactId>user</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kumhosunny</groupId>
            <artifactId>chat</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kumhosunny</groupId>
            <artifactId>knowledge</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kumhosunny</groupId>
            <artifactId>tools</artifactId>
        </dependency>

        <!-- Spring Boot Starter Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>