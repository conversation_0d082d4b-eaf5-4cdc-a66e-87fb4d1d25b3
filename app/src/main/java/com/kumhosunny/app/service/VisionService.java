package com.kumhosunny.app.service;

import com.kumhosunny.common.entity.AiGeneratedContent;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 视觉相关服务接口
 */
public interface VisionService {

    /**
     * 分页获取指定用户生成的内容
     *
     * @param userId 用户ID
     * @param page   页码 (从0开始)
     * @param size   每页数量
     * @return 分页后的内容数据
     */
    Page<AiGeneratedContent> getGeneratedContentForUser(Long userId, int page, int size);

    /**
     * 分页获取所有公开的生成内容
     *
     * @param page 页码 (从0开始)
     * @param size 每页数量
     * @return 分页后的公开内容数据
     */
    Page<AiGeneratedContent> getPublicGeneratedContent(int page, int size);

    /**
     * 批量删除指定用户的生成内容（通过数据库ID）
     *
     * @param userId     用户ID
     * @param contentIds 要删除的内容ID列表
     * @return 删除的数量
     */
    int deleteGeneratedContentByIds(Long userId, List<Long> contentIds);

    /**
     * 批量删除指定用户的生成内容（通过contentId UUID）
     *
     * @param userId     用户ID
     * @param contentIds 要删除的内容UUID列表
     * @return 删除的数量
     */
    int deleteGeneratedContentByContentIds(Long userId, List<String> contentIds);
}