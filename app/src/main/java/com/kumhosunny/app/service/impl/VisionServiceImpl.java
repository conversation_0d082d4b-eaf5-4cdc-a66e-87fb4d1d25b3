package com.kumhosunny.app.service.impl;

import com.kumhosunny.app.service.VisionService;
import com.kumhosunny.common.entity.AiGeneratedContent;
import com.kumhosunny.common.repository.AiGeneratedContentRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 视觉相关服务实现
 */
@Service
public class VisionServiceImpl implements VisionService {

    private static final Logger log = LoggerFactory.getLogger(VisionServiceImpl.class);

    @Autowired
    private AiGeneratedContentRepository aiGeneratedContentRepository;

    @Override
    public Page<AiGeneratedContent> getGeneratedContentForUser(Long userId, int page, int size) {
        // 创建分页请求，并按创建时间倒序排序
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        return aiGeneratedContentRepository.findByUserId(userId, pageable);
    }

    @Override
    public Page<AiGeneratedContent> getPublicGeneratedContent(int page, int size) {
        // 创建分页请求，并按创建时间倒序排序
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        return aiGeneratedContentRepository.findByIsPublic(true, pageable);
    }

    @Override
    @Transactional
    public int deleteGeneratedContentByIds(Long userId, List<Long> contentIds) {
        log.info("虚拟删除用户 {} 的生成内容，ID列表: {}", userId, contentIds);

        if (contentIds == null || contentIds.isEmpty()) {
            return 0;
        }

        // 虚拟删除属于该用户的指定内容（将status设置为-1）
        int deletedCount = aiGeneratedContentRepository.softDeleteByUserIdAndIdIn(userId, contentIds);

        log.info("成功虚拟删除用户 {} 的 {} 个生成内容", userId, deletedCount);
        return deletedCount;
    }

    @Override
    @Transactional
    public int deleteGeneratedContentByContentIds(Long userId, List<String> contentIds) {
        log.info("虚拟删除用户 {} 的生成内容，ContentID列表: {}", userId, contentIds);

        if (contentIds == null || contentIds.isEmpty()) {
            return 0;
        }

        // 虚拟删除属于该用户的指定内容（将status设置为-1）
        int deletedCount = aiGeneratedContentRepository.softDeleteByUserIdAndContentIdIn(userId, contentIds);

        log.info("成功虚拟删除用户 {} 的 {} 个生成内容", userId, deletedCount);
        return deletedCount;
    }
}