package com.kumhosunny.app;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;

import com.kumhosunny.common.util.StartupBannerUtil;
import com.kumhosunny.common.config.SearchProperties;

/**
 * KumhoSunny AI应用主启动类
 * 
 * <AUTHOR>
 * @since 2025-06-11
 */
@SpringBootApplication(scanBasePackages = "com.kumhosunny")
@EnableJpaRepositories(basePackages = "com.kumhosunny")
@EntityScan(basePackages = "com.kumhosunny")
@EnableJpaAuditing
@EnableConfigurationProperties(SearchProperties.class)
@EnableAsync
public class KumhosunnyAiAppApplication {

    public static void main(String[] args) {
        // 启动Spring Boot应用
        ConfigurableApplicationContext context = SpringApplication.run(KumhosunnyAiAppApplication.class, args);

        // 获取环境信息
        Environment env = context.getEnvironment();

        // 打印启动成功信息
        StartupBannerUtil.printStartupSuccess(env);
    }
}