//package com.kumhosunny.app.controller;
//
//import com.kumhosunny.common.config.SearchProperties;
//import com.kumhosunny.common.entity.WorkflowInfo;
//import com.kumhosunny.common.entity.WorkflowListResponse;
//import com.kumhosunny.knowledge.plugins.KnowledgePlugin;
//import com.kumhosunny.tools.plugins.N8nPlugin;
//import com.kumhosunny.tools.plugins.SearchPlugin;
//import com.microsoft.semantickernel.Kernel;
//import com.microsoft.semantickernel.orchestration.InvocationContext;
//import com.microsoft.semantickernel.plugin.KernelPlugin;
//import com.microsoft.semantickernel.services.chatcompletion.ChatCompletionService;
//import com.microsoft.semantickernel.services.chatcompletion.ChatHistory;
//import com.microsoft.semantickernel.services.chatcompletion.ChatMessageContent;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
///**
// * AI 控制器
// * 提供 AI 相关的 API 接口
// */
//@RestController
//@RequestMapping("/api/ai")
//public class AIController {
//
//    @Autowired
//    private Kernel kernel;
//
//    @Autowired
//    private SearchProperties searchProperties;
//
//    @Autowired
//    private KnowledgePlugin knowledgePlugin;
//
//    /**
//     * 健康检查
//     */
//    @GetMapping("/health")
//    public String health() {
//        return "AI 服务正常运行！";
//    }
//
//    /**
//     * 获取所有可用插件
//     */
//    @GetMapping("/plugins")
//    public String getPlugins() {
//        try {
//            StringBuilder sb = new StringBuilder("可用的插件：\n");
//            for (KernelPlugin plugin : kernel.getPlugins()) {
//                sb.append("- ").append(plugin.getName()).append("\n");
//                plugin.getFunctions().forEach((name, function) -> {
//                    sb.append("  └─ ").append(name).append(": ").append(function.getDescription()).append("\n");
//                });
//            }
//            return sb.toString();
//        } catch (Exception e) {
//            return "错误: " + e.getMessage();
//        }
//    }
//
//    /**
//     * 测试插件直接调用
//     */
//    @GetMapping("/test/knowledge")
//    public String testKnowledge(@RequestParam String query) {
//        try {
//
//            // 直接创建插件实例进行测试
//            return knowledgePlugin.searchKnowledge(query);
//        } catch (Exception e) {
//            return "错误: " + e.getMessage();
//        }
//    }
//
//    /**
//     * 测试搜索插件
//     */
//    @GetMapping("/test/search")
//    public String testSearch() {
//        try {
//            SearchPlugin searchPlugin = new SearchPlugin(searchProperties);
//            return searchPlugin.webSearch("Spring Boot");
//        } catch (Exception e) {
//            return "错误: " + e.getMessage();
//        }
//    }
//
//    /**
//     * 测试 N8n 插件
//     */
//    @GetMapping("/test/n8n")
//    public String testN8n() {
//        try {
//            N8nPlugin n8nPlugin = new N8nPlugin();
//            return n8nPlugin.callWorkflow("test-workflow", "测试数据");
//        } catch (Exception e) {
//            return "错误: " + e.getMessage();
//        }
//    }
//
//    /**
//     * 测试获取 N8n 工作流列表（结构化对象）
//     */
//    @GetMapping("/test/n8n/workflows")
//    public WorkflowListResponse testN8nWorkflows() {
//        try {
//            N8nPlugin n8nPlugin = new N8nPlugin();
//            return n8nPlugin.getWorkflows();
//        } catch (Exception e) {
//            // 返回空的工作流列表
//            return new WorkflowListResponse(new java.util.ArrayList<>());
//        }
//    }
//
//    /**
//     * 测试获取 N8n 工作流列表（格式化显示）
//     */
//    @GetMapping("/test/n8n/workflows-formatted")
//    public String testN8nWorkflowsFormatted() {
//        try {
//            N8nPlugin n8nPlugin = new N8nPlugin();
//            return n8nPlugin.getWorkflowsFormatted();
//        } catch (Exception e) {
//            return "错误: " + e.getMessage();
//        }
//    }
//
//    /**
//     * 测试调用所有 N8n 工作流
//     */
//    @GetMapping("/test/n8n/call-all")
//    public String testN8nCallAll() {
//        try {
//            N8nPlugin n8nPlugin = new N8nPlugin();
//            return n8nPlugin.callAllWorkflows("批量测试数据");
//        } catch (Exception e) {
//            return "错误: " + e.getMessage();
//        }
//    }
//
//    /**
//     * 测试 N8n 邮件发送
//     */
//    @GetMapping("/test/n8n/email")
//    public String testN8nEmail() {
//        try {
//            N8nPlugin n8nPlugin = new N8nPlugin();
//            return n8nPlugin.sendEmail("<EMAIL>", "测试邮件", "这是一封测试邮件");
//        } catch (Exception e) {
//            return "错误: " + e.getMessage();
//        }
//    }
//
//    /**
//     * 测试 Semantic Kernel 智能天气查询
//     * 演示完整的 AI + N8n 工作流集成流程
//     */
//    @GetMapping("/test/smart-weather")
//    public String testSmartWeather(@RequestParam(defaultValue = "帮我查询最新天气") String userQuery) {
//        try {
//            StringBuilder result = new StringBuilder();
//            result.append("🤖 智能天气查询系统测试\n");
//            result.append("用户询问: ").append(userQuery).append("\n");
//            result.append("默认使用模型: deepseek-chat\n\n");
//
//            // 步骤1: 获取 N8n 工作流列表
//            result.append("📋 步骤1: 获取 N8n 工作流列表...\n");
//            N8nPlugin n8nPlugin = new N8nPlugin();
//            WorkflowListResponse workflowResponse = n8nPlugin.getWorkflows();
//
//            if (workflowResponse.getWorkflows().isEmpty()) {
//                result.append("❌ 无法获取工作流列表\n");
//                result.append("💡 请确保 N8n 服务正常运行\n");
//                return result.toString();
//            }
//
//            result.append("✅ 找到 ").append(workflowResponse.getTotalCount()).append(" 个工作流\n");
//
//            // 步骤2: 查找天气相关的工作流
//            result.append("\n🔍 步骤2: 查找天气相关的工作流...\n");
//            java.util.Optional<WorkflowInfo> weatherWorkflow = findWeatherWorkflow(workflowResponse.getWorkflows());
//
//            if (weatherWorkflow.isEmpty()) {
//                result.append("❌ 未找到天气相关的工作流\n");
//                result.append("💡 建议: 请在 N8n 中创建名称包含 'weather', '天气' 等关键词的工作流\n");
//
//                // 显示所有可用工作流供参考
//                result.append("\n📜 当前可用的工作流:\n");
//                for (WorkflowInfo wf : workflowResponse.getWorkflows()) {
//                    result.append("  - ").append(wf.getName()).append(" (ID: ").append(wf.getId()).append(")\n");
//                }
//                return result.toString();
//            }
//
//            WorkflowInfo targetWorkflow = weatherWorkflow.get();
//            result.append("✅ 找到天气工作流: ").append(targetWorkflow.getName()).append("\n");
//
//            // 步骤3: 调用天气工作流
//            result.append("\n⚡ 步骤3: 调用天气工作流...\n");
//            String weatherData = n8nPlugin.callWorkflow(targetWorkflow.getId(), userQuery);
//            result.append("✅ 工作流执行完成\n");
//
//            // 步骤4: 使用 Semantic Kernel 和 deepseek-chat 处理并生成回复
//            result.append("\n🧠 步骤4: 使用 Semantic Kernel (deepseek-chat) 智能处理结果...\n");
//            String aiResponse = processWithKernel(userQuery, weatherData);
//            result.append("✅ AI 处理完成\n\n");
//
//            result.append("📬 最终回复:\n");
//            result.append("════════════════════════════════════════\n");
//            result.append(aiResponse);
//            result.append("\n════════════════════════════════════════\n");
//
//            return result.toString();
//
//        } catch (Exception e) {
//            return "❌ 系统错误: " + e.getMessage();
//        }
//    }
//
//    /**
//     * 测试 Semantic Kernel 基础对话功能
//     */
//    @GetMapping("/test/kernel-chat")
//    public String testKernelChat(@RequestParam String message) {
//        try {
//            ChatHistory chatHistory = new ChatHistory();
//            chatHistory.addUserMessage(message);
//
//            ChatCompletionService chatService = kernel.getService(ChatCompletionService.class);
//            java.util.List<ChatMessageContent<?>> response = chatService
//                    .getChatMessageContentsAsync(chatHistory, kernel, InvocationContext.builder().build())
//                    .block();
//
//            if (response != null && !response.isEmpty()) {
//                return "🤖 AI 回复 (使用 deepseek-chat):\n" + response.get(0).getContent().toString();
//            }
//            return "❌ 无回复";
//
//        } catch (Exception e) {
//            return "❌ Kernel 对话错误: " + e.getMessage();
//        }
//    }
//
//    /**
//     * 查找天气相关的工作流
//     */
//    private java.util.Optional<WorkflowInfo> findWeatherWorkflow(java.util.List<WorkflowInfo> workflows) {
//        return workflows.stream()
//                .filter(wf -> wf.isActive() && !wf.isArchived()) // 只考虑激活且未归档的工作流
//                .filter(wf -> {
//                    String name = wf.getName().toLowerCase();
//                    return name.contains("weather") ||
//                            name.contains("天气") ||
//                            name.contains("forecast") ||
//                            name.contains("temperature") ||
//                            name.contains("温度");
//                })
//                .findFirst();
//    }
//
//    /**
//     * 使用 Semantic Kernel 处理工作流结果并生成用户友好的回复
//     */
//    private String processWithKernel(String userQuery, String workflowResult) {
//        try {
//            String prompt = String.format(
//                    "用户询问: %s\n\n" +
//                            "N8n工作流返回的数据:\n%s\n\n" +
//                            "请根据以上信息，为用户提供一个友好、详细的天气回复。如果数据中包含天气信息，请提取关键信息并用自然语言表达。如果没有有效的天气数据，请说明情况。",
//                    userQuery, workflowResult);
//
//            ChatHistory chatHistory = new ChatHistory();
//            chatHistory.addUserMessage(prompt);
//
//            ChatCompletionService chatService = kernel.getService(ChatCompletionService.class);
//            java.util.List<ChatMessageContent<?>> response = chatService
//                    .getChatMessageContentsAsync(chatHistory, kernel, InvocationContext.builder().build())
//                    .block();
//
//            if (response != null && !response.isEmpty()) {
//                return response.get(0).getContent().toString();
//            }
//            return "抱歉，AI 处理出现问题，无法生成回复。";
//
//        } catch (Exception e) {
//            return "抱歉，AI 处理过程中出现错误: " + e.getMessage() +
//                    "\n\n原始工作流数据:\n" + workflowResult;
//        }
//    }
//
//    /**
//     * 智能聊天接口 - 让 AI 自动决定是否调用插件
//     * 这是 Semantic Kernel 的核心功能：AI 自动选择和调用插件
//     */
//    @GetMapping("/chat")
//    public String smartChat(@RequestParam String message) {
//        try {
//            StringBuilder result = new StringBuilder();
//            result.append("🤖 智能助手 (支持自动插件调用)\n\n");
//            result.append("👤 用户: ").append(message).append("\n\n");
//
//            // 创建系统提示，告诉 AI 可以使用的插件
//            String systemPrompt = "你是一个智能助手，可以帮助用户完成各种任务。你可以使用以下插件来提供更好的服务：\n\n" +
//                    "📚 KnowledgePlugin - 知识库插件：\n" +
//                    "- searchKnowledge(query): 搜索知识库信息\n" +
//                    "- getRecommendations(topic): 获取主题推荐\n" +
//                    "- getKnowledgeStats(category): 获取知识统计\n\n" +
//                    "🔍 SearchPlugin - 搜索插件：\n" +
//                    "- webSearch(query): 执行网络搜索\n" +
//                    "- getSearchSuggestions(topic): 获取搜索建议\n\n" +
//                    "⚡ N8nPlugin - 工作流自动化插件：\n" +
//                    "- callWorkflow(workflow_id, input_data): 调用指定的工作流\n" +
//                    "- getWorkflowsFormatted(): 获取工作流列表\n" +
//                    "- callAllWorkflows(input_data): 调用所有工作流\n" +
//                    "- sendEmail(recipient, subject, content): 发送邮件\n\n" +
//                    "请根据用户的需求，智能选择合适的插件来帮助用户。如果用户询问：\n" +
//                    "- 关于学习、知识相关的问题 → 使用 KnowledgePlugin\n" +
//                    "- 需要搜索信息 → 使用 SearchPlugin\n" +
//                    "- 需要执行工作流、自动化任务、发送邮件 → 使用 N8nPlugin\n" +
//                    "- 询问有哪些工作流 → 使用 N8nPlugin.getWorkflowsFormatted()\n\n" +
//                    "请自然地与用户对话，并在合适的时候调用插件来提供帮助。";
//
//            // 创建聊天历史
//            ChatHistory chatHistory = new ChatHistory();
//            chatHistory.addSystemMessage(systemPrompt);
//            chatHistory.addUserMessage(message);
//
//            // 使用 Kernel 的聊天完成服务，让 AI 自动决定是否调用插件
//            ChatCompletionService chatService = kernel.getService(ChatCompletionService.class);
//
//            // 创建调用上下文，启用自动插件调用
//            InvocationContext invocationContext = InvocationContext.builder().build();
//
//            var response = chatService.getChatMessageContentsAsync(
//                    chatHistory,
//                    kernel,
//                    invocationContext).block();
//
//            if (response != null && !response.isEmpty()) {
//                result.append("🤖 助手: ").append(response.get(0).getContent().toString());
//            } else {
//                result.append("❌ 抱歉，我暂时无法回答您的问题。");
//            }
//
//            return result.toString();
//
//        } catch (Exception e) {
//            return "❌ 智能聊天错误: " + e.getMessage() +
//                    "\n\n💡 提示：AI 会根据您的问题自动选择合适的插件来帮助您。" +
//                    "\n例如：" +
//                    "\n- 「帮我搜索 Spring Boot 相关信息」→ 自动调用搜索插件" +
//                    "\n- 「查看有哪些工作流」→ 自动调用 N8n 插件" +
//                    "\n- 「帮我学习 Java」→ 自动调用知识库插件";
//        }
//    }
//
//    /**
//     * 演示 Kernel 的插件自动调用能力
//     * 显示 AI 如何根据不同类型的问题自动选择插件
//     */
//    @GetMapping("/demo/auto-plugin")
//    public String demoAutoPlugin() {
//        StringBuilder demo = new StringBuilder();
//        demo.append("🚀 Semantic Kernel 自动插件调用演示\n\n");
//
//        String[] testQueries = {
//                "帮我搜索最新的 AI 技术趋势",
//                "查看有哪些可用的工作流",
//                "推荐一些 Java 学习资源",
//                "帮我调用天气查询工作流",
//                "发送一封测试邮件给 <EMAIL>"
//        };
//
//        demo.append("以下是不同类型问题的自动插件调用示例：\n\n");
//
//        for (int i = 0; i < testQueries.length; i++) {
//            String query = testQueries[i];
//            demo.append(String.format("【示例 %d】\n", i + 1));
//            demo.append("用户询问：").append(query).append("\n");
//
//            // 预测 AI 会调用的插件
//            String expectedPlugin = predictPlugin(query);
//            demo.append("预期调用：").append(expectedPlugin).append("\n");
//            demo.append("说明：AI 会分析用户意图并自动调用合适的插件\n\n");
//        }
//
//        demo.append("💡 使用方式：\n");
//        demo.append("访问 /api/ai/chat?message=您的问题\n");
//        demo.append("AI 将自动分析并调用合适的插件来帮助您！\n\n");
//
//        demo.append("🔧 技术原理：\n");
//        demo.append("1. Semantic Kernel 将插件信息注入到 AI 的上下文中\n");
//        demo.append("2. AI 分析用户问题并决定是否需要调用插件\n");
//        demo.append("3. AI 自动选择合适的插件和参数进行调用\n");
//        demo.append("4. AI 整合插件返回的结果并生成最终回复");
//
//        return demo.toString();
//    }
//
//    /**
//     * 预测给定查询会调用的插件（用于演示）
//     */
//    private String predictPlugin(String query) {
//        String lowerQuery = query.toLowerCase();
//
//        if (lowerQuery.contains("搜索") || lowerQuery.contains("search") || lowerQuery.contains("趋势")) {
//            return "SearchPlugin.webSearch()";
//        } else if (lowerQuery.contains("工作流") || lowerQuery.contains("workflow") || lowerQuery.contains("n8n")) {
//            return "N8nPlugin.getWorkflowsFormatted()";
//        } else if (lowerQuery.contains("学习") || lowerQuery.contains("推荐") || lowerQuery.contains("知识")) {
//            return "KnowledgePlugin.getRecommendations() 或 searchKnowledge()";
//        } else if (lowerQuery.contains("调用") && lowerQuery.contains("工作流")) {
//            return "N8nPlugin.callWorkflow()";
//        } else if (lowerQuery.contains("邮件") || lowerQuery.contains("发送")) {
//            return "N8nPlugin.sendEmail()";
//        }
//
//        return "AI 会根据具体内容智能选择";
//    }
//}