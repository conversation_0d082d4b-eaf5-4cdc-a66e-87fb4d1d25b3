package com.kumhosunny.app.controller;

import com.kumhosunny.app.service.VisionService;
import com.kumhosunny.common.entity.AiGeneratedContent;
import com.kumhosunny.common.result.ApiResponse;
import com.kumhosunny.common.util.UserContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 视觉相关API控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/vision")
public class VisionController {

    private static final Logger log = LoggerFactory.getLogger(VisionController.class);

    @Autowired
    private VisionService visionService;

    @Autowired
    private UserContextUtil userContextUtil;

    /**
     * 查询当前用户生成的图像信息数据（分页）
     *
     * @param page        页码 (默认0)
     * @param size        每页数量 (默认10)
     * @param httpRequest HTTP请求，用于获取用户信息
     * @return 分页后的图像数据
     */
    @GetMapping("/generations")
    public ApiResponse<Page<AiGeneratedContent>> getMyGeneratedContent(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            HttpServletRequest httpRequest) {
        try {
            Long userId = userContextUtil.getCurrentUserId(httpRequest);
            log.info("Fetching generated content for user: {}, page: {}, size: {}", userId, page, size);
            Page<AiGeneratedContent> contentPage = visionService.getGeneratedContentForUser(userId, page, size);
            return ApiResponse.success("查询成功", contentPage);
        } catch (Exception e) {
            log.error("Error fetching generated content", e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询所有公开的图像信息数据（分页）
     *
     * @param page 页码 (默认0)
     * @param size 每页数量 (默认10)
     * @return 分页后的公开图像数据
     */
    @GetMapping("/public-generations")
    public ApiResponse<Page<AiGeneratedContent>> getPublicGeneratedContent(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            log.info("Fetching public generated content, page: {}, size: {}", page, size);
            Page<AiGeneratedContent> contentPage = visionService.getPublicGeneratedContent(page, size);
            return ApiResponse.success("查询成功", contentPage);
        } catch (Exception e) {
            log.error("Error fetching public generated content", e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除当前用户生成的图像内容
     *
     * @param contentIds  要删除的内容UUID列表
     * @param httpRequest HTTP请求，用于获取用户信息
     * @return 删除结果
     */
    @DeleteMapping("/generations")
    public ApiResponse<String> deleteGeneratedContent(
            @RequestBody List<String> contentIds,
            HttpServletRequest httpRequest) {
        try {
            Long userId = userContextUtil.getCurrentUserId(httpRequest);
            log.info("User {} attempting to delete content with ContentIDs: {}", userId, contentIds);

            if (contentIds == null || contentIds.isEmpty()) {
                return ApiResponse.error("删除失败: 未指定要删除的内容");
            }

            int deletedCount = visionService.deleteGeneratedContentByContentIds(userId, contentIds);

            if (deletedCount == 0) {
                return ApiResponse.error("删除失败: 未找到可删除的内容或您没有权限删除这些内容");
            }

            return ApiResponse.success("删除成功", String.format("成功删除 %d 个内容", deletedCount));
        } catch (Exception e) {
            log.error("Error deleting generated content for user", e);
            return ApiResponse.error("删除失败: " + e.getMessage());
        }
    }
}