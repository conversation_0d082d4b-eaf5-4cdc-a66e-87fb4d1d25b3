package com.kumhosunny.app.controller;

import com.kumhosunny.app.ai.agent.AgentFactory;
import com.kumhosunny.app.ai.agent.AgentInvokeOptions;
import com.kumhosunny.app.ai.agent.AgentResult;
import com.kumhosunny.app.ai.agent.AgentThread;
import com.kumhosunny.app.ai.agent.ChatCompletionAgent;

import com.microsoft.semantickernel.functionchoice.FunctionChoiceBehavior;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Agent 演示控制器
 * 展示按照 Microsoft Semantic Kernel 官方标准实现的 Agent API
 * 参考:
 * https://learn.microsoft.com/zh-cn/semantic-kernel/frameworks/agent/agent-functions?pivots=programming-language-java
 */
@RestController
@RequestMapping("/api/agent")
public class AgentDemoController {

    private static final Logger log = LoggerFactory.getLogger(AgentDemoController.class);

    private final AgentFactory agentFactory;

    // 线程管理（简单示例，实际应该使用持久化存储）
    private final Map<String, AgentThread> threadCache = new ConcurrentHashMap<>();

    @Autowired
    public AgentDemoController(AgentFactory agentFactory) {
        this.agentFactory = agentFactory;
    }

    /**
     * 基础 Agent 调用示例
     * 对应官方 API: agent.invokeAsync("What is the capital of France?")
     */
    @PostMapping("/invoke/simple")
    public Mono<String> simpleInvoke(@RequestParam String message) {
        log.info("Simple Agent invoke: {}", message);

        try {
            // 创建通用助手 Agent
            ChatCompletionAgent agent = agentFactory.createGeneralAssistant(
                    agentFactory.getDefaultModelId());

            // 简单调用
            return agent.invokeAsync(message)
                    .map(AgentResult::getContent)
                    .doOnSuccess(result -> log.info("Agent response: {}", result))
                    .doOnError(error -> log.error("Agent invocation failed: {}", error.getMessage()));

        } catch (Exception e) {
            log.error("Error creating agent: {}", e.getMessage(), e);
            return Mono.just("创建 Agent 失败: " + e.getMessage());
        }
    }

    /**
     * 使用线程的 Agent 调用示例
     * 对应官方 API: agent.invokeAsync("message", agentThread)
     */
    @PostMapping("/invoke/with-thread")
    public Mono<String> invokeWithThread(
            @RequestParam String message,
            @RequestParam(required = false) String threadId) {

        log.info("Agent invoke with thread: message={}, threadId={}", message, threadId);

        try {
            // 创建或获取 Agent
            ChatCompletionAgent agent = agentFactory.createGeneralAssistant(
                    agentFactory.getDefaultModelId());

            // 获取或创建线程
            AgentThread thread = getOrCreateThread(threadId);

            // 使用线程调用
            return agent.invokeAsync(message, thread)
                    .map(result -> {
                        // 保存线程状态
                        threadCache.put(result.getThread().getThreadId(), result.getThread());

                        // 返回结果，包含线程信息
                        return String.format("线程ID: %s\n消息数量: %d\n响应: %s",
                                result.getThread().getThreadId(),
                                result.getThread().getMessageCount(),
                                result.getContent());
                    })
                    .doOnError(error -> log.error("Thread-based invocation failed: {}", error.getMessage()));

        } catch (Exception e) {
            log.error("Error in thread-based invocation: {}", e.getMessage(), e);
            return Mono.just("调用失败: " + e.getMessage());
        }
    }

    /**
     * 使用选项的 Agent 调用示例
     * 对应官方 API: agent.invokeAsync("message", thread, options)
     */
    @PostMapping("/invoke/with-options")
    public Mono<String> invokeWithOptions(
            @RequestParam String message,
            @RequestParam(required = false) String threadId,
            @RequestParam(required = false) String additionalInstructions) {

        log.info("Agent invoke with options: message={}, threadId={}, additionalInstructions={}",
                message, threadId, additionalInstructions);

        try {
            // 创建 Agent
            ChatCompletionAgent agent = agentFactory.createGeneralAssistant(
                    agentFactory.getDefaultModelId());

            // 获取或创建线程
            AgentThread thread = getOrCreateThread(threadId);

            // 创建调用选项
            AgentInvokeOptions options = AgentInvokeOptions.builder().build();
//            options = AgentInvokeOptions.builder()
//                    .withAdditionalInstructions(additionalInstructions)
//                    .withFunctionChoiceBehavior(FunctionChoiceBehavior.auto(true))
//                    .build();

            // 使用完整选项调用
            return agent.invokeAsync(message, thread, options)
                    .map(result -> {
                        // 保存线程状态
                        threadCache.put(result.getThread().getThreadId(), result.getThread());

                        return String.format("Agent: %s\n线程ID: %s\n消息数量: %d\n附加指令: %s\n响应: %s",
                                result.getAgentName(),
                                result.getThread().getThreadId(),
                                result.getThread().getMessageCount(),
                                additionalInstructions != null ? additionalInstructions : "无",
                                result.getContent());
                    })
                    .doOnError(error -> log.error("Options-based invocation failed: {}", error.getMessage()));

        } catch (Exception e) {
            log.error("Error in options-based invocation: {}", e.getMessage(), e);
            return Mono.just("调用失败: " + e.getMessage());
        }
    }

    /**
     * 角色特定的 Agent 调用示例
     */
    @PostMapping("/invoke/role-specific")
    public Mono<String> invokeRoleSpecific(
            @RequestParam String message,
            @RequestParam String role,
            @RequestParam(required = false) String modelId) {

        log.info("Role-specific Agent invoke: message={}, role={}, modelId={}", message, role, modelId);

        try {
            String actualModelId = modelId != null ? modelId : agentFactory.getDefaultModelId();

            // 根据角色创建特定 Agent
            ChatCompletionAgent agent;
            switch (role.toLowerCase()) {
                case "knowledge":
                case "知识专家":
                    agent = agentFactory.createKnowledgeExpert(actualModelId);
                    break;
                case "search":
                case "搜索专家":
                    agent = agentFactory.createSearchExpert(actualModelId);
                    break;
                case "workflow":
                case "工作流专家":
                    agent = agentFactory.createWorkflowExpert(actualModelId);
                    break;
                default:
                    agent = agentFactory.createGeneralAssistant(actualModelId);
                    break;
            }

            // 验证 Agent
            if (!agentFactory.validateAgent(agent)) {
                return Mono.just("Agent 验证失败");
            }

            // 调用 Agent
            return agent.invokeAsync(message)
                    .map(result -> String.format("角色: %s\nAgent: %s\n响应: %s",
                            role,
                            result.getAgentName(),
                            result.getContent()))
                    .doOnError(error -> log.error("Role-specific invocation failed: {}", error.getMessage()));

        } catch (Exception e) {
            log.error("Error in role-specific invocation: {}", e.getMessage(), e);
            return Mono.just("调用失败: " + e.getMessage());
        }
    }

    /**
     * 获取线程历史
     */
    @GetMapping("/thread/{threadId}/history")
    public Mono<String> getThreadHistory(@PathVariable String threadId) {
        AgentThread thread = threadCache.get(threadId);

        if (thread == null) {
            return Mono.just("线程不存在: " + threadId);
        }

        StringBuilder history = new StringBuilder();
        history.append(String.format("线程ID: %s\n", thread.getThreadId()));
        history.append(String.format("消息数量: %d\n", thread.getMessageCount()));
        history.append(String.format("是否已删除: %s\n\n", thread.isDeleted()));

        // 显示前10条消息
        thread.getMessages().stream()
                .limit(10)
                .forEach(message -> {
                    history.append(String.format("[%s] %s\n",
                            message.getAuthorRole(),
                            message.getContent()));
                });

        return Mono.just(history.toString());
    }

    /**
     * 删除线程
     */
    @DeleteMapping("/thread/{threadId}")
    public Mono<String> deleteThread(@PathVariable String threadId) {
        AgentThread thread = threadCache.get(threadId);

        if (thread == null) {
            return Mono.just("线程不存在: " + threadId);
        }

        return thread.deleteAsync()
                .then(Mono.fromRunnable(() -> threadCache.remove(threadId)))
                .then(Mono.just("线程已删除: " + threadId))
                .doOnSuccess(result -> log.info("Thread deleted: {}", threadId));
    }

    /**
     * 获取 Agent 统计信息
     */
    @GetMapping("/stats")
    public Mono<String> getStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("🤖 Agent 系统统计\n\n");
        stats.append(String.format("• 缓存的 Agent 数量: %d\n", agentFactory.getCachedAgentCount()));
        stats.append(String.format("• 活动线程数量: %d\n", threadCache.size()));
        stats.append(String.format("• 默认模型: %s\n", agentFactory.getDefaultModelId()));

        stats.append("\n✅ 支持的功能:\n");
        stats.append("• ChatCompletionAgent.invokeAsync()\n");
        stats.append("• AgentThread 管理\n");
        stats.append("• AgentInvokeOptions 配置\n");
        stats.append("• FunctionChoiceBehavior 行为控制\n");
        stats.append("• 多角色 Agent 支持\n");
        stats.append("• 动态插件调用\n");

        return Mono.just(stats.toString());
    }

    /**
     * 清除缓存
     */
    @PostMapping("/cache/clear")
    public Mono<String> clearCache() {
        int agentCount = agentFactory.getCachedAgentCount();
        int threadCount = threadCache.size();

        agentFactory.clearCache();
        threadCache.clear();

        return Mono.just(String.format("缓存已清除: %d 个 Agent, %d 个线程", agentCount, threadCount));
    }

    /**
     * 获取或创建线程
     */
    private AgentThread getOrCreateThread(String threadId) {
        if (threadId != null && threadCache.containsKey(threadId)) {
            AgentThread existingThread = threadCache.get(threadId);
            if (!existingThread.isDeleted()) {
                return existingThread;
            }
        }

        // 创建新线程
        AgentThread newThread = new AgentThread();
        threadCache.put(newThread.getThreadId(), newThread);
        return newThread;
    }
}