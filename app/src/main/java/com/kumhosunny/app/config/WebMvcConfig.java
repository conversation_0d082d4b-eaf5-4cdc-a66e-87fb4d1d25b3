package com.kumhosunny.app.config;

import com.kumhosunny.user.config.TokenInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Spring MVC配置类
 * 配置静态资源处理和视图控制器
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    @Autowired
    private TokenInterceptor tokenInterceptor;
    /**
     * 配置静态资源处理
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置静态资源映射
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/static/")
                .setCachePeriod(3600); // 缓存1小时
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加Token拦截器，并设置拦截路径
        registry.addInterceptor(tokenInterceptor)
                .addPathPatterns("/**")     // 拦截所有请求
                .excludePathPatterns(       // 排除不需要拦截的路径
                        "/login",           // 登录接口
                        "/ddlogin/auth",           // 登录接口
                        "/error"
                );
    }
    /**
     * 配置视图控制器
     */
    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        // API根路径重定向到首页
        registry.addRedirectViewController("/", "/index.html");
    }
}