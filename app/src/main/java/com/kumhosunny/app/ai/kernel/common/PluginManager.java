package com.kumhosunny.app.ai.kernel.common;

import com.kumhosunny.common.config.SearchProperties;
import com.kumhosunny.knowledge.plugins.KnowledgePlugin;
import com.kumhosunny.tools.plugins.N8nPlugin;
import com.kumhosunny.tools.plugins.SearchPlugin;
import com.kumhosunny.tools.plugins.CodeSandboxPlugin;
import com.microsoft.semantickernel.plugin.KernelPlugin;
import com.microsoft.semantickernel.plugin.KernelPluginFactory;
import com.microsoft.semantickernel.semanticfunctions.KernelFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.lang.reflect.Field;
import com.microsoft.semantickernel.semanticfunctions.annotations.KernelFunctionParameter;

/**
 * 动态插件管理器
 * 使用标准的 Semantic Kernel 方式动态创建和管理插件
 */
@Component
public class PluginManager {

    private static final Logger log = LoggerFactory.getLogger(PluginManager.class);

    @Autowired
    private SearchProperties searchProperties;

    /**
     * 创建所有标准插件
     */
    public List<KernelPlugin> createAllPlugins() {
        List<KernelPlugin> plugins = new ArrayList<>();

        try {
            plugins.add(createKnowledgePlugin());
            plugins.add(createSearchPlugin());
            plugins.add(createN8nPlugin());
            plugins.add(createCodeSandboxPlugin());

            log.info("成功创建 {} 个插件", plugins.size());
        } catch (Exception e) {
            log.error("创建插件时发生错误: {}", e.getMessage(), e);
        }

        return plugins;
    }

    /**
     * 创建知识库插件
     */
    public KernelPlugin createKnowledgePlugin() {
        try {
            KnowledgePlugin instance = new KnowledgePlugin();
            return createAndLogPlugin(instance, PluginConstants.KNOWLEDGE_PLUGIN_NAME);
        } catch (Exception e) {
            log.error("创建知识库插件失败: {}", e.getMessage(), e);
            throw new RuntimeException(PluginConstants.ERROR_PLUGIN_CREATION_FAILED + ": KnowledgePlugin", e);
        }
    }

    /**
     * 创建搜索插件
     */
    public KernelPlugin createSearchPlugin() {
        try {
            SearchPlugin instance = new SearchPlugin(searchProperties);
            return createAndLogPlugin(instance, PluginConstants.SEARCH_PLUGIN_NAME);
        } catch (Exception e) {
            log.error("创建搜索插件失败: {}", e.getMessage(), e);
            throw new RuntimeException(PluginConstants.ERROR_PLUGIN_CREATION_FAILED + ": SearchPlugin", e);
        }
    }

    /**
     * 创建 N8n 工作流插件
     */
    public KernelPlugin createN8nPlugin() {
        try {
            N8nPlugin instance = new N8nPlugin();
            return createAndLogPlugin(instance, PluginConstants.N8N_PLUGIN_NAME);
        } catch (Exception e) {
            log.error("创建 N8n 插件失败: {}", e.getMessage(), e);
            throw new RuntimeException(PluginConstants.ERROR_PLUGIN_CREATION_FAILED + ": N8nPlugin", e);
        }
    }

    /**
     * 创建代码沙盒插件
     */
    public KernelPlugin createCodeSandboxPlugin() {
        try {
            CodeSandboxPlugin instance = new CodeSandboxPlugin();
            return createAndLogPlugin(instance, PluginConstants.CODE_SANDBOX_PLUGIN_NAME);
        } catch (Exception e) {
            log.error("创建代码沙盒插件失败: {}", e.getMessage(), e);
            throw new RuntimeException(PluginConstants.ERROR_PLUGIN_CREATION_FAILED + ": CodeSandboxPlugin", e);
        }
    }

    /**
     * 通用的插件创建和日志记录辅助方法
     */
    private KernelPlugin createAndLogPlugin(Object pluginInstance, String pluginName) {
        KernelPlugin plugin = KernelPluginFactory.createFromObject(pluginInstance, pluginName);
        log.debug("{} 创建成功, 包含 {} 个函数", pluginName, plugin.getFunctions().size());
        logPluginFunctions(plugin);
        return plugin;
    }

    /**
     * 动态生成系统提示词
     * 基于实际加载的插件和函数
     */
    public String generateSystemPrompt(List<KernelPlugin> plugins) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("你是一个智能助手，基于 Microsoft Semantic Kernel 框架构建。\n");
        prompt.append("你可以使用以下插件来提供更好的服务：\n\n");

        for (KernelPlugin plugin : plugins) {
            String pluginName = plugin.getName();
            String emoji = getPluginEmoji(pluginName);

            prompt.append(emoji).append(" ").append(pluginName).append("：\n");

            // 动态获取插件中的所有函数
            for (Map.Entry<String, KernelFunction<?>> entry : plugin.getFunctions().entrySet()) {
                String functionName = entry.getKey();
                KernelFunction<?> function = entry.getValue();
                String description = function.getDescription();

                if (description == null || description.trim().isEmpty()) {
                    description = "执行 " + functionName + " 操作";
                }

                prompt.append("• ").append(functionName).append(": ").append(description).append("\n");
            }
            prompt.append("\n");
        }

        prompt.append("请根据用户的需求，智能选择合适的插件来帮助用户。\n");
        prompt.append("始终保持友好、专业的服务态度。");

        return prompt.toString();
    }

    /**
     * 将插件转换为 OpenAI 工具格式
     * 动态获取函数信息而不是硬编码
     */
    public List<Map<String, Object>> convertPluginsToOpenAITools(List<KernelPlugin> plugins) {
        List<Map<String, Object>> tools = new ArrayList<>();

        for (KernelPlugin plugin : plugins) {
            String pluginName = plugin.getName();

            for (Map.Entry<String, KernelFunction<?>> entry : plugin.getFunctions().entrySet()) {
                String functionName = entry.getKey();
                KernelFunction<?> function = entry.getValue();

                Map<String, Object> tool = createToolFromFunction(plugin, functionName, function);
                if (tool != null) {
                    tools.add(tool);
                    log.debug("创建工具定义: {}.{}", pluginName, functionName);
                }
            }
        }

        log.info("成功创建 {} 个工具定义", tools.size());
        return tools;
    }

    /**
     * 从 KernelFunction 动态创建 OpenAI 工具定义
     */
    private Map<String, Object> createToolFromFunction(KernelPlugin plugin, String functionName,
            KernelFunction<?> function) {
        try {
            Map<String, Object> tool = new HashMap<>();
            tool.put("type", PluginConstants.TOOL_TYPE_FUNCTION);

            Map<String, Object> functionDef = new HashMap<>();
            functionDef.put("name", plugin.getName() + "_" + functionName);

            String description = function.getDescription();
            if (description == null || description.trim().isEmpty()) {
                description = "Function " + functionName + " from plugin " + plugin.getName();
            }
            functionDef.put("description", description);

            // 动态构建参数定义
            Map<String, Object> parameters = buildFunctionParameters(plugin, function);
            functionDef.put("parameters", parameters);

            tool.put("function", functionDef);
            return tool;

        } catch (Exception e) {
            log.warn("创建工具定义失败 {}.{}: {}", plugin.getName(), functionName, e.getMessage());
            return null;
        }
    }

    /**
     * 动态构建函数参数定义
     * 尝试从函数元数据中提取参数信息
     */
    private Map<String, Object> buildFunctionParameters(KernelPlugin plugin, KernelFunction<?> function) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", PluginConstants.PARAM_TYPE_OBJECT);

        Map<String, Object> properties = new HashMap<>();
        List<String> required = new ArrayList<>();

        boolean extractionSuccess = false;
        try {
            // 尝试通过反射获取参数信息
            extractParametersFromFunction(plugin, function, properties, required);
            extractionSuccess = true;
        } catch (Exception e) {
            log.warn("无法动态提取参数信息 for {}.{}，错误: {}",
                    plugin.getName(), function.getName(), e.getMessage());
        }

        // 只有在无法从反射中成功提取任何参数时，才应考虑添加默认值。
        // 但更好的做法是确保无参函数生成空的 properties。
        if (!extractionSuccess && properties.isEmpty()) {
            log.warn("为 {}.{} 添加默认 'input' 参数，因为反射提取失败。", plugin.getName(), function.getName());
            addDefaultParameter(properties, required);
        }

        parameters.put("properties", properties);
        // 只有在有属性时才添加 required 字段
        if (!properties.isEmpty()) {
            parameters.put("required", required);
        }
        return parameters;
    }

    /**
     * 从函数中提取参数。
     * 关键步骤：从 KernelPlugin 获取实例，然后反射其方法。
     */
    private void extractParametersFromFunction(KernelPlugin plugin, KernelFunction<?> function,
            Map<String, Object> properties,
            List<String> required) {

        Object pluginInstance = getPluginInstanceFromPlugin(plugin);

        if (pluginInstance == null) {
            // log.warn("无法从 KernelPlugin 获取插件实例 '{}'，无法提取参数。", plugin.getName());
            // 直接返回，让调用方决定如何处理（例如，使用默认参数）
            return;
        }

        java.lang.reflect.Method method = findMethodByName(pluginInstance.getClass(), function.getName());

        if (method == null) {
            log.warn("在插件 '{}' 中找不到与函数 '{}' 匹配的方法。", plugin.getName(), function.getName());
            return;
        }

        // 如果方法没有参数，直接返回空的 properties 和 required 列表
        if (method.getParameterCount() == 0) {
            log.debug("方法 '{}' 没有参数。", method.getName());
            return;
        }

        for (java.lang.reflect.Parameter param : method.getParameters()) {
            KernelFunctionParameter annotation = param.getAnnotation(KernelFunctionParameter.class);

            if (annotation != null) {
                String paramName = Objects.toString(annotation.name(), param.getName());
                String paramDesc = Objects.toString(annotation.description(), "No description");
                String paramType = getJsonSchemaType(param.getType());

                properties.put(paramName, createParameterDefinition(paramType, paramDesc));

                if (annotation.required()) {
                    required.add(paramName);
                }
            }
        }

        if (properties.isEmpty()) {
            addDefaultParameter(properties, required);
        }
    }

    /**
     * 从 KernelPlugin 获取实例。
     * 这是一个更可靠的反射实现。
     */
    private Object getPluginInstanceFromPlugin(KernelPlugin plugin) {
        try {
            Field instanceField = plugin.getClass().getDeclaredField("instance");
            instanceField.setAccessible(true);
            return instanceField.get(plugin);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            // log.warn("无法通过反射从 KernelPlugin 获取实例: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 检查是否是已知插件类型
     */
    private boolean isKnownPluginInstance(Object instance) {
        return instance instanceof com.kumhosunny.knowledge.plugins.KnowledgePlugin ||
                instance instanceof com.kumhosunny.tools.plugins.SearchPlugin ||
                instance instanceof com.kumhosunny.tools.plugins.N8nPlugin ||
                instance instanceof com.kumhosunny.tools.plugins.CodeSandboxPlugin;
    }

    /**
     * 根据函数名查找方法
     */
    private java.lang.reflect.Method findMethodByName(Class<?> clazz, String functionName) {
        java.lang.reflect.Method[] methods = clazz.getDeclaredMethods();

        for (java.lang.reflect.Method method : methods) {
            // 检查方法是否有 @DefineKernelFunction 注解
            com.microsoft.semantickernel.semanticfunctions.annotations.DefineKernelFunction annotation = method
                    .getAnnotation(
                            com.microsoft.semantickernel.semanticfunctions.annotations.DefineKernelFunction.class);

            if (annotation != null && annotation.name().equals(functionName)) {
                return method;
            }

            // 回退：如果方法名匹配
            if (method.getName().equals(functionName)) {
                return method;
            }
        }

        return null;
    }

    /**
     * 将 Java 类型转换为 JSON Schema 类型
     */
    private String getJsonSchemaType(Class<?> javaType) {
        if (javaType == String.class) {
            return PluginConstants.PARAM_TYPE_STRING;
        } else if (javaType == Integer.class || javaType == int.class ||
                javaType == Long.class || javaType == long.class) {
            return "integer";
        } else if (javaType == Double.class || javaType == double.class ||
                javaType == Float.class || javaType == float.class) {
            return "number";
        } else if (javaType == Boolean.class || javaType == boolean.class) {
            return "boolean";
        } else {
            return PluginConstants.PARAM_TYPE_STRING; // 默认为字符串
        }
    }

    /**
     * 判断参数是否是可选的
     */
    private boolean isOptionalParameter(
            com.microsoft.semantickernel.semanticfunctions.annotations.KernelFunctionParameter annotation,
            String description) {
        // 如果描述中包含"可选"、"optional"等关键词，认为是可选参数
        String lowerDesc = description.toLowerCase();
        return lowerDesc.contains("可选") || lowerDesc.contains("optional") ||
                lowerDesc.contains("(可选)") || lowerDesc.contains("（可选）");
    }

    /**
     * 添加默认参数
     */
    private void addDefaultParameter(Map<String, Object> properties, List<String> required) {
        properties.put("input", createParameterDefinition(PluginConstants.PARAM_TYPE_STRING, "函数输入参数"));
        required.add("input");
    }

    /**
     * 创建参数定义
     */
    private Map<String, Object> createParameterDefinition(String type, String description) {
        Map<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("description", description);
        return param;
    }

    /**
     * 获取函数名称
     */
    private String getFunctionName(KernelFunction<?> function) {
        try {
            // 尝试获取函数名称
            java.lang.reflect.Method getNameMethod = function.getClass().getMethod("getName");
            Object name = getNameMethod.invoke(function);
            if (name != null) {
                return name.toString();
            }
        } catch (Exception e) {
            log.trace("无法获取函数名称: {}", e.getMessage());
        }
        return "unknownFunction";
    }

    /**
     * 记录插件函数信息
     */
    private void logPluginFunctions(KernelPlugin plugin) {
        if (log.isDebugEnabled()) {
            for (String functionName : plugin.getFunctions().keySet()) {
                log.debug("  └─ 函数: {}", functionName);
            }
        }
    }

    /**
     * 获取插件图标
     */
    private String getPluginEmoji(String pluginName) {
        switch (pluginName) {
            case PluginConstants.KNOWLEDGE_PLUGIN_NAME:
                return "📚";
            case PluginConstants.SEARCH_PLUGIN_NAME:
                return "🔍";
            case PluginConstants.N8N_PLUGIN_NAME:
                return "⚡";
            case PluginConstants.CODE_SANDBOX_PLUGIN_NAME:
                return "💻";
            default:
                return "��";
        }
    }
}