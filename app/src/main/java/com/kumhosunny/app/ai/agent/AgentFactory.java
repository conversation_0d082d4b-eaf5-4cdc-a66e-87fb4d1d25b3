package com.kumhosunny.app.ai.agent;

import com.kumhosunny.app.ai.kernel.adapter.ModelRouterChatCompletionService;
import com.kumhosunny.app.ai.kernel.common.PluginManager;
import com.kumhosunny.chat.service.ModelRouterService;
import com.kumhosunny.tools.plugins.N8nPlugin;
import com.microsoft.semantickernel.Kernel;
import com.microsoft.semantickernel.functionchoice.FunctionChoiceBehavior;
import com.microsoft.semantickernel.orchestration.InvocationContext;
import com.microsoft.semantickernel.plugin.KernelPlugin;
import com.microsoft.semantickernel.services.chatcompletion.ChatCompletionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Agent 工厂类
 * 按照 Microsoft Semantic Kernel 官方标准实现
 * 参考:
 * https://learn.microsoft.com/zh-cn/semantic-kernel/frameworks/agent/agent-functions?pivots=programming-language-java
 */
@Component
public class AgentFactory {

    private static final Logger log = LoggerFactory.getLogger(AgentFactory.class);

    private final PluginManager pluginManager;
    private final ModelRouterService modelRouterService;
    private final N8nPlugin n8nPlugin;

    // Agent 缓存
    private final Map<String, ChatCompletionAgent> agentCache = new ConcurrentHashMap<>();

    @Autowired
    public AgentFactory(PluginManager pluginManager, ModelRouterService modelRouterService, N8nPlugin n8nPlugin) {
        this.pluginManager = pluginManager;
        this.modelRouterService = modelRouterService;
        this.n8nPlugin = n8nPlugin;
    }

    /**
     * 创建特定角色的 Agent
     * 按照官方工厂模式实现
     */
    public ChatCompletionAgent createSpecificAgent(String role, String modelId) {
        String cacheKey = role + "_" + modelId;

        return agentCache.computeIfAbsent(cacheKey, key -> {
            log.info("Creating new Agent for role: {}, model: {}", role, modelId);

            try {
                // 1. 创建基础 Kernel
                Kernel baseKernel = createBaseKernel(modelId);

                // 2. 克隆 Kernel 实例以允许特定于代理的插件定义
                Kernel agentKernel = cloneKernel(baseKernel);

                // 3. 根据角色添加特定插件
                addRoleSpecificPlugins(agentKernel, role);

                // 4. 创建调用上下文
                InvocationContext invocationContext = createInvocationContext(role);

                // 5. 获取工作流信息
                String workflowInfo = n8nPlugin.getWorkflowsFormatted();

                // 6. 创建 Agent
                return ChatCompletionAgent.builder()
                        .withKernel(agentKernel)
                        .withName(role + "Agent")
                        .withWorkflowInfo(workflowInfo) // 传递工作流信息
                        .withInvocationContext(invocationContext)
                        .build();

            } catch (Exception e) {
                log.error("Failed to create Agent for role {}: {}", role, e.getMessage(), e);
                throw new RuntimeException("Agent creation failed", e);
            }
        });
    }

    /**
     * 创建通用智能助手 Agent
     */
    public ChatCompletionAgent createGeneralAssistant(String modelId) {
        return createSpecificAgent("GeneralAssistant", modelId);
    }

    /**
     * 创建知识专家 Agent
     */
    public ChatCompletionAgent createKnowledgeExpert(String modelId) {
        return createSpecificAgent("KnowledgeExpert", modelId);
    }

    /**
     * 创建搜索专家 Agent
     */
    public ChatCompletionAgent createSearchExpert(String modelId) {
        return createSpecificAgent("SearchExpert", modelId);
    }

    /**
     * 创建工作流专家 Agent
     */
    public ChatCompletionAgent createWorkflowExpert(String modelId) {
        return createSpecificAgent("WorkflowExpert", modelId);
    }

    /**
     * 创建基础 Kernel
     */
    private Kernel createBaseKernel(String modelId) {
        // 创建聊天完成服务
        ChatCompletionService chatCompletionService = new ModelRouterChatCompletionService(
                modelRouterService, modelId);

        // --- 临时修改：仅加载 N8nPlugin ---
        // 1. 直接从 PluginManager 创建 N8nPlugin 实例
        List<KernelPlugin> plugins = List.of(pluginManager.createN8nPlugin());
        log.warn("注意：已临时禁用除 N8nPlugin 之外的所有插件。");

        // 2. 原来的代码被注释掉
        // List<KernelPlugin> plugins = pluginManager.createAllPlugins();
        // --- 修改结束 ---

        // 构建 Kernel
        Kernel.Builder kernelBuilder = Kernel.builder()
                .withAIService(ChatCompletionService.class, chatCompletionService);

        // 添加所有插件
        for (KernelPlugin plugin : plugins) {
            kernelBuilder.withPlugin(plugin);
        }

        return kernelBuilder.build();
    }

    /**
     * 克隆 Kernel 实例
     * 模拟官方的 kernel.Clone() 方法
     */
    private Kernel cloneKernel(Kernel baseKernel) {
        // 由于 Java 版本可能没有直接的 clone 方法，我们重新创建
        try {
            ChatCompletionService chatService = baseKernel.getService(ChatCompletionService.class);

            Kernel.Builder builder = Kernel.builder()
                    .withAIService(ChatCompletionService.class, chatService);

            // 复制所有插件
            for (KernelPlugin plugin : baseKernel.getPlugins()) {
                builder.withPlugin(plugin);
            }

            return builder.build();
        } catch (Exception e) {
            log.error("Failed to clone kernel: {}", e.getMessage(), e);
            throw new RuntimeException("Kernel cloning failed", e);
        }
    }

    /**
     * 根据角色添加特定插件
     */
    private void addRoleSpecificPlugins(Kernel kernel, String role) {
        switch (role) {
            case "KnowledgeExpert":
                // 知识专家主要使用知识库插件
                log.debug("Knowledge expert will primarily use KnowledgePlugin");
                break;

            case "SearchExpert":
                // 搜索专家主要使用搜索插件
                log.debug("Search expert will primarily use SearchPlugin");
                break;

            case "WorkflowExpert":
                // 工作流专家主要使用 N8n 插件
                log.debug("Workflow expert will primarily use N8nPlugin");
                break;

            case "GeneralAssistant":
            default:
                // 通用助手可以使用所有插件
                log.debug("General assistant can use all available plugins");
                break;
        }
    }

    /**
     * 创建角色特定的调用上下文
     */
    private InvocationContext createInvocationContext(String role) {
        InvocationContext.Builder builder = InvocationContext.builder();

        // 根据角色配置函数选择行为
        FunctionChoiceBehavior functionBehavior;
        switch (role) {
            case "KnowledgeExpert":
            case "SearchExpert":
            case "WorkflowExpert":
                functionBehavior = FunctionChoiceBehavior.auto(true);
                break;
            default:
                functionBehavior = FunctionChoiceBehavior.auto(true);
                break;
        }

        // TODO: 当官方 API 可用时，使用标准方法
        // builder.withFunctionChoiceBehavior(functionBehavior);

        return builder.build();
    }

    /**
     * 获取默认模型 ID
     */
    public String getDefaultModelId() {
        return "deepseek-ai/DeepSeek-V3"; // 可以从配置文件读取
    }

    /**
     * 清除 Agent 缓存
     */
    public void clearCache() {
        log.info("Clearing Agent cache, {} entries removed", agentCache.size());
        agentCache.clear();
    }

    /**
     * 获取缓存的 Agent 数量
     */
    public int getCachedAgentCount() {
        return agentCache.size();
    }

    /**
     * 验证 Agent 配置
     */
    public boolean validateAgent(ChatCompletionAgent agent) {
        try {
            if (agent == null) {
                log.warn("Agent is null");
                return false;
            }

            if (agent.getKernel() == null) {
                log.warn("Agent kernel is null");
                return false;
            }

            try {
                ChatCompletionService chatService = agent.getKernel().getService(ChatCompletionService.class);
                if (chatService == null) {
                    log.warn("Agent has no ChatCompletionService");
                    return false;
                }
            } catch (Exception e) {
                log.warn("Failed to get ChatCompletionService: {}", e.getMessage());
                return false;
            }

            log.debug("Agent validation passed: {}", agent.getName());
            return true;

        } catch (Exception e) {
            log.error("Agent validation failed: {}", e.getMessage(), e);
            return false;
        }
    }
}