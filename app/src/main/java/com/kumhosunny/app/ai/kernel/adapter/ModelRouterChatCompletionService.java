package com.kumhosunny.app.ai.kernel.adapter;

import com.kumhosunny.app.ai.kernel.common.PluginConstants;
import com.kumhosunny.app.ai.kernel.common.PluginManager;
import com.kumhosunny.chat.dto.ChatCompletionRequest;
import com.kumhosunny.chat.dto.ChatCompletionResponse;
import com.kumhosunny.chat.dto.ChatMessage;
import com.kumhosunny.chat.service.ModelRouterService;
import com.microsoft.semantickernel.Kernel;
import com.microsoft.semantickernel.orchestration.FunctionResultMetadata;
import com.microsoft.semantickernel.orchestration.InvocationContext;
import com.microsoft.semantickernel.services.chatcompletion.ChatCompletionService;
import com.microsoft.semantickernel.services.chatcompletion.ChatHistory;
import com.microsoft.semantickernel.services.chatcompletion.ChatMessageContent;
import com.microsoft.semantickernel.services.chatcompletion.StreamingChatContent;
import com.microsoft.semantickernel.services.chatcompletion.AuthorRole;
import com.microsoft.semantickernel.plugin.KernelPlugin;
import com.microsoft.semantickernel.semanticfunctions.KernelFunction;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Collections;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonProcessingException;

/**
 * 模型路由器聊天完成服务适配器
 * 按照 Microsoft Semantic Kernel 官方标准实现
 * 参考:
 * https://learn.microsoft.com/zh-cn/semantic-kernel/frameworks/agent/agent-functions?pivots=programming-language-java
 */
public class ModelRouterChatCompletionService implements ChatCompletionService {

    private static final Logger log = LoggerFactory.getLogger(ModelRouterChatCompletionService.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    private final ModelRouterService modelRouterService;
    private final String modelId;
    private final PluginManager pluginManager;

    /**
     * 工具调用信息类，用于规范封装工具调用信息
     */
    public static class ToolCallInfo {
        private List<String> toolNames;
        private String result;

        public ToolCallInfo() {
            this.toolNames = new ArrayList<>();
            this.result = "";
        }

        public ToolCallInfo(List<String> toolNames, String result) {
            this.toolNames = toolNames;
            this.result = result;
        }

        public List<String> getToolNames() {
            return toolNames;
        }

        public void setToolNames(List<String> toolNames) {
            this.toolNames = toolNames;
        }

        public String getResult() {
            return result;
        }

        public void setResult(String result) {
            this.result = result;
        }

        @Override
        public String toString() {
            try {
                return objectMapper.writeValueAsString(this);
            } catch (JsonProcessingException e) {
                return "{\"toolNames\":" + toolNames + ",\"result\":\"" + result + "\"}";
            }
        }
    }

    public ModelRouterChatCompletionService(ModelRouterService modelRouterService) {
        this(modelRouterService, PluginConstants.DEFAULT_MODEL_ID);
    }

    public ModelRouterChatCompletionService(ModelRouterService modelRouterService, String modelId) {
        this.modelRouterService = modelRouterService;
        this.modelId = modelId;
        this.pluginManager = new PluginManager(); // TODO: 应该通过依赖注入获取
    }

    @Override
    public Mono<List<ChatMessageContent<?>>> getChatMessageContentsAsync(
            ChatHistory chatHistory,
            Kernel kernel,
            InvocationContext invocationContext) {

        // 将 ChatHistory 转换为 ChatCompletionRequest
        ChatCompletionRequest request = convertToCompletionRequest(chatHistory, kernel, invocationContext);

        // 调用模型路由服务
        return modelRouterService.routeAndProcess(request)
                .map(response -> convertToChatMessageContents(response, kernel))
                .onErrorMap(ex -> {
                    log.error("Error processing chat completion: {}", ex.getMessage(), ex);
                    return new RuntimeException("Chat completion failed: " + ex.getMessage(), ex);
                });
    }

    @Override
    public Mono<List<ChatMessageContent<?>>> getChatMessageContentsAsync(
            String prompt,
            Kernel kernel,
            InvocationContext invocationContext) {

        ChatHistory chatHistory = new ChatHistory();
        chatHistory.addUserMessage(prompt);

        return getChatMessageContentsAsync(chatHistory, kernel, invocationContext);
    }

    @Override
    public Flux<StreamingChatContent<?>> getStreamingChatMessageContentsAsync(
            ChatHistory chatHistory,
            Kernel kernel,
            InvocationContext invocationContext) {
        log.warn("Streaming not yet implemented in Semantic Kernel for Java.");
        return Flux.error(new UnsupportedOperationException("Streaming not yet implemented."));
    }

    @Override
    public Flux<StreamingChatContent<?>> getStreamingChatMessageContentsAsync(
            String prompt,
            Kernel kernel,
            InvocationContext invocationContext) {
        log.warn("Streaming not yet implemented in Semantic Kernel for Java.");
        return Flux.error(new UnsupportedOperationException("Streaming not yet implemented."));
    }

    @Override
    public String getModelId() {
        return this.modelId;
    }

    @Override
    public String getServiceId() {
        return PluginConstants.SERVICE_ID;
    }

    /**
     * 直接调用底层服务进行流式处理
     * 这个方法绕过了 Semantic Kernel 的流式限制，直接访问 ModelRouterService
     * 
     * @param request 聊天完成请求
     * @return 流式响应块
     */
    public Flux<com.kumhosunny.chat.dto.ChatCompletionChunk> streamRequest(ChatCompletionRequest request) {
        log.info("Bypassing SK streaming restrictions and calling ModelRouterService directly.");
        return modelRouterService.routeAndStream(request);
    }

    /**
     * 按照官方标准转换 ChatHistory 为 ChatCompletionRequest
     * 使用标准的 FunctionChoiceBehavior 处理工具调用
     */
    public ChatCompletionRequest convertToCompletionRequest(
            ChatHistory chatHistory,
            Kernel kernel,
            InvocationContext invocationContext) {

        List<ChatMessage> messages = chatHistory.getMessages().stream()
                .map(this::convertSKMessageToChatMessage)
                .collect(Collectors.toList());

        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setModel(this.modelId);
        request.setMessages(messages);

        // 按照官方标准处理插件和工具调用
        if (kernel != null && shouldEnableToolCalls(invocationContext)) {
            List<Map<String, Object>> tools = convertKernelToOpenAITools(kernel);
            if (!tools.isEmpty()) {
                request.setTools(tools);
                log.debug("Added {} tools from kernel", tools.size());
            }
        }

        return request;
    }

    /**
     * 检查是否应该启用工具调用
     * 根据官方文档，检查 FunctionChoiceBehavior 配置
     */
    private boolean shouldEnableToolCalls(InvocationContext invocationContext) {
        if (invocationContext == null) {
            return false;
        }

        // 这里应该检查 invocationContext 中的 FunctionChoiceBehavior
        // 但由于我们没有直接访问权限，默认启用
        // TODO: 实现标准的 FunctionChoiceBehavior 检查
        return true;
    }

    /**
     * 使用 PluginManager 将 Kernel 转换为 OpenAI 工具格式
     */
    private List<Map<String, Object>> convertKernelToOpenAITools(Kernel kernel) {
        try {
            List<KernelPlugin> plugins = kernel.getPlugins().stream().collect(Collectors.toList());
            return pluginManager.convertPluginsToOpenAITools(plugins);
        } catch (Exception e) {
            log.warn("Error converting kernel to OpenAI tools: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 改进的工具调用处理
     * 使用标准的 Kernel 函数调用机制
     */
    private List<ChatMessageContent<?>> handleToolCalls(List<Map<String, Object>> toolCalls, Kernel kernel) {
        List<ChatMessageContent<?>> results = new ArrayList<>();

        for (Map<String, Object> toolCall : toolCalls) {
            try {
                String functionName = extractFunctionName(toolCall);
                Map<String, Object> arguments = extractFunctionArguments(toolCall);

                if (functionName != null) {
                    String result = executeKernelFunction(kernel, functionName, arguments);
                    results.add(createChatMessageContent(result));
                }

            } catch (Exception e) {
                log.error("Error processing tool call: {}", e.getMessage(), e);
                results.add(createChatMessageContent(
                        PluginConstants.ERROR_TOOL_CALL_PROCESSING_FAILED + ": " + e.getMessage()));
            }
        }

        return results.isEmpty()
                ? Collections.singletonList(createChatMessageContent("工具调用处理完成，但没有返回结果"))
                : results;
    }

    /**
     * 使用 Kernel 执行函数
     * 这是标准的 Semantic Kernel 方式
     */
    private String executeKernelFunction(Kernel kernel, String functionName, Map<String, Object> arguments) {
        if (kernel == null) {
            log.warn("Kernel is null, cannot execute function: {}", functionName);
            return "Kernel 未初始化，无法执行函数: " + functionName;
        }

        try {
            // 解析插件名和函数名
            String[] parts = functionName.split("_", 2);
            if (parts.length != 2) {
                log.warn("Invalid function name format: {}", functionName);
                return "无效的函数名格式: " + functionName;
            }

            String pluginName = parts[0];
            String actualFunctionName = parts[1];

            // 查找插件
            KernelPlugin plugin = findPlugin(kernel, pluginName);
            if (plugin == null) {
                log.warn("Plugin not found: {}", pluginName);
                return "未找到插件: " + pluginName;
            }

            // 查找函数
            KernelFunction<?> function = plugin.getFunctions().get(actualFunctionName);
            if (function == null) {
                log.warn("Function not found: {} in plugin {}", actualFunctionName, pluginName);
                return "未找到函数: " + actualFunctionName + " 在插件 " + pluginName + " 中";
            }

            // 使用标准的 Kernel 函数调用机制
            log.info("执行函数: {}.{} 参数: {}", pluginName, actualFunctionName, arguments);

            // TODO: 当 Kernel.invokeAsync 可用时，使用标准调用方式
            // return kernel.invokeAsync(plugin, function, arguments).block();

            // 临时使用反射调用插件实例的方法
            return invokePluginMethodDynamically(plugin, actualFunctionName, arguments);

        } catch (Exception e) {
            log.error("Error executing kernel function {}: {}", functionName, e.getMessage(), e);
            return PluginConstants.ERROR_FUNCTION_EXECUTION_FAILED + ": " + functionName + " - " + e.getMessage();
        }
    }

    /**
     * 查找指定名称的插件
     */
    private KernelPlugin findPlugin(Kernel kernel, String pluginName) {
        return kernel.getPlugins().stream()
                .filter(plugin -> plugin.getName().equals(pluginName))
                .findFirst()
                .orElse(null);
    }

    /**
     * 动态调用插件方法
     * 通过反射获取插件实例并调用对应方法
     */
    private String invokePluginMethodDynamically(KernelPlugin plugin, String functionName,
            Map<String, Object> arguments) {
        try {
            // 获取插件实例
            Object pluginInstance = getPluginInstance(plugin);
            if (pluginInstance == null) {
                return "无法获取插件实例: " + plugin.getName();
            }

            // 通过反射查找匹配的方法
            java.lang.reflect.Method method = findMatchingMethod(pluginInstance.getClass(), functionName, arguments);
            if (method == null) {
                return "未找到匹配的方法: " + functionName + " 在插件 " + plugin.getName();
            }

            // 准备方法参数
            Object[] methodArgs = prepareMethodArguments(method, arguments);

            // 调用方法
            method.setAccessible(true);
            Object result = method.invoke(pluginInstance, methodArgs);

            return result != null ? result.toString() : "方法执行成功，但返回值为空";

        } catch (Exception e) {
            log.error("Dynamic plugin method invocation failed: {}", e.getMessage(), e);
            return "动态方法调用失败: " + e.getMessage();
        }
    }

    /**
     * 获取插件实例
     * 通过反射从 KernelPlugin 中提取原始插件实例
     */
    private Object getPluginInstance(KernelPlugin plugin) {
        try {
            // 尝试通过反射获取插件实例
            // 不同版本的 Semantic Kernel 可能有不同的内部结构

            // 方法1: 尝试获取 instance 字段
            java.lang.reflect.Field[] fields = plugin.getClass().getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(plugin);

                // 检查是否是我们的插件实例
                if (value != null && isKnownPluginInstance(value)) {
                    return value;
                }
            }

            // 方法2: 基于插件名称创建新实例（回退方案）
            return createPluginInstanceByName(plugin.getName());

        } catch (Exception e) {
            log.warn("Failed to get plugin instance: {}", e.getMessage());
            return createPluginInstanceByName(plugin.getName());
        }
    }

    /**
     * 检查是否是已知的插件实例
     */
    private boolean isKnownPluginInstance(Object instance) {
        return instance instanceof com.kumhosunny.knowledge.plugins.KnowledgePlugin ||
                instance instanceof com.kumhosunny.tools.plugins.SearchPlugin ||
                instance instanceof com.kumhosunny.tools.plugins.N8nPlugin ||
                instance instanceof com.kumhosunny.tools.plugins.CodeSandboxPlugin;
    }

    /**
     * 根据插件名称创建插件实例（回退方案）
     */
    private Object createPluginInstanceByName(String pluginName) {
        try {
            switch (pluginName) {
                case PluginConstants.KNOWLEDGE_PLUGIN_NAME:
                    return new com.kumhosunny.knowledge.plugins.KnowledgePlugin();
                case PluginConstants.SEARCH_PLUGIN_NAME:
                    return new com.kumhosunny.tools.plugins.SearchPlugin();
                case PluginConstants.N8N_PLUGIN_NAME:
                    return new com.kumhosunny.tools.plugins.N8nPlugin();
                case PluginConstants.CODE_SANDBOX_PLUGIN_NAME:
                    return new com.kumhosunny.tools.plugins.CodeSandboxPlugin();
                default:
                    log.warn("Unknown plugin name: {}", pluginName);
                    return null;
            }
        } catch (Exception e) {
            log.error("Failed to create plugin instance for: {}", pluginName, e);
            return null;
        }
    }

    /**
     * 查找匹配的方法
     */
    private java.lang.reflect.Method findMatchingMethod(Class<?> clazz, String methodName,
            Map<String, Object> arguments) {
        java.lang.reflect.Method[] methods = clazz.getDeclaredMethods();

        log.debug("查找方法: {} 在类 {} 中", methodName, clazz.getSimpleName());

        // 忽略大小写匹配
        for (java.lang.reflect.Method method : methods) {
            if (method.getName().equalsIgnoreCase(methodName)) {
                if (isMethodParametersMatching(method, arguments)) {
                    log.debug("找到匹配的方法: {}", method.getName());
                    return method;
                }
            }
        }

        log.warn("未找到匹配的方法: {} 在类 {} 中", methodName, clazz.getSimpleName());
        return null;
    }

    /**
     * 检查方法参数是否匹配
     */
    private boolean isMethodParametersMatching(java.lang.reflect.Method method, Map<String, Object> arguments) {
        Class<?>[] paramTypes = method.getParameterTypes();

        // 简单匹配：如果参数数量相同或接近，认为匹配
        if (paramTypes.length == 0 && arguments.isEmpty()) {
            return true;
        }

        if (paramTypes.length > 0 && !arguments.isEmpty()) {
            // 检查是否所有参数都是字符串类型（大多数插件方法都是）
            for (Class<?> paramType : paramTypes) {
                if (!paramType.equals(String.class)) {
                    // 如果有非字符串参数，需要更复杂的匹配逻辑
                    log.debug("Method {} has non-string parameter: {}", method.getName(), paramType);
                }
            }
            return true;
        }

        return false;
    }

    /**
     * 准备方法参数
     */
    private Object[] prepareMethodArguments(java.lang.reflect.Method method, Map<String, Object> arguments) {
        Class<?>[] paramTypes = method.getParameterTypes();
        Object[] args = new Object[paramTypes.length];

        // 获取参数信息
        java.lang.reflect.Parameter[] parameters = method.getParameters();

        for (int i = 0; i < parameters.length; i++) {
            String paramName = getParameterName(parameters[i]);
            Class<?> paramType = paramTypes[i];

            // 尝试从 arguments 中获取对应参数值
            Object value = findArgumentValue(paramName, arguments);

            // 类型转换
            if (value != null && paramType.equals(String.class)) {
                args[i] = value.toString();
            } else if (value != null) {
                // 其他类型的转换逻辑
                args[i] = convertToType(value, paramType);
            } else {
                // 如果找不到参数值，使用默认值
                args[i] = getDefaultValueForType(paramType);
            }
        }

        return args;
    }

    /**
     * 获取参数名称，优先使用 @KernelFunctionParameter 注解
     */
    private String getParameterName(java.lang.reflect.Parameter parameter) {
        // 首先检查是否有 @KernelFunctionParameter 注解
        com.microsoft.semantickernel.semanticfunctions.annotations.KernelFunctionParameter annotation = parameter
                .getAnnotation(
                        com.microsoft.semantickernel.semanticfunctions.annotations.KernelFunctionParameter.class);

        if (annotation != null) {
            return annotation.name();
        }

        // 回退到参数名称
        return parameter.getName();
    }

    /**
     * 从参数映射中查找参数值（简化版本）
     */
    private Object findArgumentValue(String paramName, Map<String, Object> arguments) {
        // 直接匹配
        if (arguments.containsKey(paramName)) {
            return arguments.get(paramName);
        }

        // 只保留少数必要的映射，避免硬编码猜测
        Map<String, String> essentialMappings = new HashMap<>();
        // 保留一些常见的别名映射
        essentialMappings.put("sourceCode", "code"); // CodeSandbox 插件的特殊情况
        essentialMappings.put("languageName", "language");
        essentialMappings.put("languageId", "language_id");

        String mappedName = essentialMappings.get(paramName);
        if (mappedName != null && arguments.containsKey(mappedName)) {
            return arguments.get(mappedName);
        }

        // 反向映射
        for (Map.Entry<String, String> entry : essentialMappings.entrySet()) {
            if (entry.getValue().equals(paramName) && arguments.containsKey(entry.getKey())) {
                return arguments.get(entry.getKey());
            }
        }

        // 如果只有一个参数，使用第一个可用值
        if (arguments.size() == 1) {
            return arguments.values().iterator().next();
        }

        log.debug("未找到参数 {} 的值，可用参数: {}", paramName, arguments.keySet());
        return null;
    }

    /**
     * 类型转换
     */
    private Object convertToType(Object value, Class<?> targetType) {
        if (targetType.equals(String.class)) {
            return value.toString();
        }
        // 其他类型转换逻辑...
        return value;
    }

    /**
     * 获取类型的默认值
     */
    private Object getDefaultValueForType(Class<?> type) {
        if (type.equals(String.class)) {
            return "";
        }
        // 其他类型的默认值...
        return null;
    }

    /**
     * 提取函数名
     */
    private String extractFunctionName(Map<String, Object> toolCall) {
        if (toolCall.containsKey("function")) {
            Map<String, Object> functionData = (Map<String, Object>) toolCall.get("function");
            return (String) functionData.get("name");
        }
        return null;
    }

    /**
     * 提取函数参数
     */
    private Map<String, Object> extractFunctionArguments(Map<String, Object> toolCall) {
        if (toolCall.containsKey("function")) {
            Map<String, Object> functionData = (Map<String, Object>) toolCall.get("function");
            String argumentsStr = (String) functionData.get("arguments");

            if (argumentsStr != null) {
                try {
                    com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                    return mapper.readValue(argumentsStr, Map.class);
                } catch (Exception e) {
                    log.warn("Failed to parse function arguments: {}", e.getMessage());
                    return new HashMap<>();
                }
            }
        }
        return new HashMap<>();
    }

    /**
     * 转换 Semantic Kernel 消息为 ChatMessage
     */
    private ChatMessage convertSKMessageToChatMessage(ChatMessageContent<?> skMessage) {
        String role = skMessage.getAuthorRole().toString().toLowerCase();
        String content = String.valueOf(skMessage.getContent());
        return new ChatMessage(role, content);
    }

    /**
     * 将 ChatCompletionResponse 转换为 ChatMessageContent 列表
     */
    private List<ChatMessageContent<?>> convertToChatMessageContents(ChatCompletionResponse response, Kernel kernel) {
        if (response == null) {
            log.warn("Empty or null response received");
            return Collections.emptyList();
        }

        if (response.getChoices() == null || response.getChoices().isEmpty()) {
            log.warn("No choices in response");
            return Collections.emptyList();
        }

        ChatCompletionResponse.Choice choice = response.getChoices().get(0);
        ChatMessage message = choice.getMessage();

        // 检查是否有工具调用
        if (message.getToolCalls() != null && !message.getToolCalls().isEmpty()) {
            log.info("检测到工具调用，工具数量: {}", message.getToolCalls().size());
            // 处理工具调用
            List<ChatMessageContent<?>> toolResults = handleToolCalls(message.getToolCalls(), kernel);

            // 如果工具调用有结果，为第一个结果添加工具调用元数据
            if (!toolResults.isEmpty()) {
                String firstResult = toolResults.get(0).getContent().toString();
                ChatMessageContent<?> resultWithMetadata = createChatMessageContentWithToolCalls(firstResult,
                        message.getToolCalls());

                // 替换第一个结果
                List<ChatMessageContent<?>> results = new ArrayList<>();
                results.add(resultWithMetadata);
                if (toolResults.size() > 1) {
                    results.addAll(toolResults.subList(1, toolResults.size()));
                }
                return results;
            } else {
                // 如果没有工具结果，创建一个带有工具调用元数据的空结果
                return Collections
                        .singletonList(createChatMessageContentWithToolCalls("工具调用处理完成", message.getToolCalls()));
            }
        } else {
            // 普通消息响应，没有工具调用
            String content = message.getContent() != null ? message.getContent().toString() : "处理成功 - 模型: " + this.modelId;
            return Collections.singletonList(createChatMessageContent(content));
        }
    }

    /**
     * 创建 ChatMessageContent 实例
     */
    private ChatMessageContent<?> createChatMessageContent(String content) {
        return new ChatMessageContent<>(
                AuthorRole.ASSISTANT,
                content,
                this.modelId,
                content,
                StandardCharsets.UTF_8,
                new FunctionResultMetadata());
    }

    /**
     * 创建带有工具调用元数据的 ChatMessageContent 实例
     */
    private ChatMessageContent<?> createChatMessageContentWithToolCalls(String content,
            List<Map<String, Object>> toolCalls) {

        if (toolCalls != null && !toolCalls.isEmpty()) {
            // 记录工具调用信息
            List<String> toolNames = new ArrayList<>();
            for (Map<String, Object> toolCall : toolCalls) {
                String functionName = extractFunctionName(toolCall);
                if (functionName != null) {
                    toolNames.add(functionName);
                }
            }

            // 创建工具调用信息对象
            ToolCallInfo toolCallInfo = new ToolCallInfo(toolNames, content);

            // 记录工具调用信息
            log.info("工具调用信息: {}", toolCallInfo);

            // 将工具调用信息添加到内容前面
            String enhancedContent = toolCallInfo.toString();

            return new ChatMessageContent<>(
                    AuthorRole.ASSISTANT,
                    enhancedContent,
                    this.modelId,
                    enhancedContent,
                    StandardCharsets.UTF_8,
                    new FunctionResultMetadata());
        }

        return new ChatMessageContent<>(
                AuthorRole.ASSISTANT,
                content,
                this.modelId,
                content,
                StandardCharsets.UTF_8,
                new FunctionResultMetadata());
    }
}