package com.kumhosunny.app.ai.kernel;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.kumhosunny.app.ai.kernel.adapter.ModelRouterChatCompletionService;
import com.kumhosunny.chat.service.ModelRouterService;
import com.kumhosunny.common.config.SearchProperties;
import com.kumhosunny.knowledge.plugins.KnowledgePlugin;
import com.kumhosunny.tools.plugins.N8nPlugin;
import com.kumhosunny.tools.plugins.SearchPlugin;
import com.microsoft.semantickernel.Kernel;
import com.microsoft.semantickernel.plugin.KernelPlugin;
import com.microsoft.semantickernel.plugin.KernelPluginFactory;
import com.microsoft.semantickernel.services.chatcompletion.ChatCompletionService;

/**
 * Semantic Kernel 配置类
 * 配置 AI 服务和插件，使用 chat 模块的模型路由器
 */
@Configuration
public class KernelConfig {

    @Autowired
    private ModelRouterService modelRouterService;

    @Autowired
    private SearchProperties searchProperties;

    /**
     * 创建聊天完成服务 - 使用模型路由器
     */
    @Bean
    public ChatCompletionService chatCompletionService() {
        return new ModelRouterChatCompletionService(modelRouterService);
    }

    /**
     * 创建 Semantic Kernel 实例
     */
    @Bean
    public Kernel kernel(ChatCompletionService chatCompletionService) {
        // 创建插件
        KernelPlugin knowledgePlugin = KernelPluginFactory.createFromObject(new KnowledgePlugin(), "KnowledgePlugin");
        KernelPlugin searchPlugin = KernelPluginFactory.createFromObject(new SearchPlugin(searchProperties),
                "SearchPlugin");
        KernelPlugin n8nPlugin = KernelPluginFactory.createFromObject(new N8nPlugin(), "N8nPlugin");

        // 构建 Kernel
        return Kernel.builder()
                .withAIService(ChatCompletionService.class, chatCompletionService)
                 .withPlugin(knowledgePlugin)
//                 .withPlugin(searchPlugin)
                .withPlugin(n8nPlugin)
                .build();
    }
}