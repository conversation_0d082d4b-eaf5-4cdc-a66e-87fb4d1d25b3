package com.kumhosunny.app.ai.agent;

import com.microsoft.semantickernel.services.chatcompletion.ChatMessageContent;

/**
 * Agent 调用结果
 * 包含消息内容和相关的上下文信息
 */
public class AgentResult {

    private final ChatMessageContent<?> message;
    private final AgentThread thread;
    private final String agentName;

    public AgentResult(ChatMessageContent<?> message, AgentThread thread, String agentName) {
        this.message = message;
        this.thread = thread;
        this.agentName = agentName;
    }

    /**
     * 获取响应消息内容
     */
    public ChatMessageContent<?> getMessage() {
        return message;
    }

    /**
     * 获取关联的 AgentThread
     */
    public AgentThread getThread() {
        return thread;
    }

    /**
     * 获取生成此结果的 Agent 名称
     */
    public String getAgentName() {
        return agentName;
    }

    /**
     * 获取消息内容的字符串表示
     */
    public String getContent() {
        return message != null ? message.getContent().toString() : "";
    }

    @Override
    public String toString() {
        return String.format("AgentResult{agent='%s', content='%s'}",
                agentName, getContent());
    }
}