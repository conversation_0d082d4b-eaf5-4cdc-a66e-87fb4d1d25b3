package com.kumhosunny.app.ai.kernel.common;

/**
 * 插件系统常量定义
 * 只包含真正需要的配置常量，函数信息应该动态获取
 */
public final class PluginConstants {

    private PluginConstants() {
        // 工具类，禁止实例化
    }

    // 默认模型配置
    public static final String DEFAULT_MODEL_ID = "deepseek-chat";
    public static final String SERVICE_ID = "ModelRouterChatCompletionService";

    // 插件名称（用于 KernelPluginFactory）
    public static final String KNOWLEDGE_PLUGIN_NAME = "KnowledgePlugin";
    public static final String SEARCH_PLUGIN_NAME = "SearchPlugin";
    public static final String N8N_PLUGIN_NAME = "N8nPlugin";
    public static final String CODE_SANDBOX_PLUGIN_NAME = "CodeSandboxPlugin";

    // OpenAI 工具定义
    public static final String TOOL_TYPE_FUNCTION = "function";
    public static final String PARAM_TYPE_STRING = "string";
    public static final String PARAM_TYPE_OBJECT = "object";

    // 通用错误信息
    public static final String ERROR_PLUGIN_CREATION_FAILED = "创建插件失败";
    public static final String ERROR_FUNCTION_EXECUTION_FAILED = "函数执行失败";
    public static final String ERROR_TOOL_CALL_PROCESSING_FAILED = "工具调用处理失败";
    public static final String ERROR_PARAMETER_EXTRACTION_FAILED = "参数提取失败";
}