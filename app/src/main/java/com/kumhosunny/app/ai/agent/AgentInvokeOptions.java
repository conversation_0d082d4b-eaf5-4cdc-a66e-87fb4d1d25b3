package com.kumhosunny.app.ai.agent;

import com.microsoft.semantickernel.Kernel;
import com.microsoft.semantickernel.functionchoice.FunctionChoiceBehavior;
import com.microsoft.semantickernel.orchestration.InvocationContext;

import java.util.Map;

/**
 * Agent 调用选项配置
 * 按照 Microsoft Semantic Kernel 官方标准实现
 * 参考:
 * https://learn.microsoft.com/zh-cn/semantic-kernel/frameworks/agent/agent-api?pivots=programming-language-java
 */
public class AgentInvokeOptions {

    private final Kernel kernel;
    private final Map<String, Object> kernelArguments;
    private final String additionalInstructions;
    private final InvocationContext invocationContext;
    private final FunctionChoiceBehavior functionChoiceBehavior;

    private AgentInvokeOptions(Builder builder) {
        this.kernel = builder.kernel;
        this.kernelArguments = builder.kernelArguments;
        this.additionalInstructions = builder.additionalInstructions;
        this.invocationContext = builder.invocationContext;
        this.functionChoiceBehavior = builder.functionChoiceBehavior;
    }

    /**
     * 获取覆盖的 Kernel 实例
     */
    public Kernel getKernel() {
        return kernel;
    }

    /**
     * 获取覆盖的 Kernel 参数
     */
    public Map<String, Object> getKernelArguments() {
        return kernelArguments;
    }

    /**
     * 获取附加指令
     */
    public String getAdditionalInstructions() {
        return additionalInstructions;
    }

    /**
     * 获取覆盖的调用上下文
     */
    public InvocationContext getInvocationContext() {
        return invocationContext;
    }

    /**
     * 获取函数选择行为
     */
    public FunctionChoiceBehavior getFunctionChoiceBehavior() {
        return functionChoiceBehavior;
    }

    /**
     * 创建 Builder 实例
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * Builder 模式实现
     */
    public static class Builder {
        private Kernel kernel;
        private Map<String, Object> kernelArguments;
        private String additionalInstructions;
        private InvocationContext invocationContext;
        private FunctionChoiceBehavior functionChoiceBehavior;

        /**
         * 设置覆盖的 Kernel 实例
         */
        public Builder withKernel(Kernel kernel) {
            this.kernel = kernel;
            return this;
        }

        /**
         * 设置覆盖的 Kernel 参数
         */
        public Builder withKernelArguments(Map<String, Object> kernelArguments) {
            this.kernelArguments = kernelArguments;
            return this;
        }

        /**
         * 设置附加指令
         * 这些指令会被添加到原始 Agent 指令之外，仅适用于此次调用
         */
        public Builder withAdditionalInstructions(String additionalInstructions) {
            this.additionalInstructions = additionalInstructions;
            return this;
        }

        /**
         * 设置覆盖的调用上下文
         */
        public Builder withInvocationContext(InvocationContext invocationContext) {
            this.invocationContext = invocationContext;
            return this;
        }

        /**
         * 设置函数选择行为
         */
        public Builder withFunctionChoiceBehavior(FunctionChoiceBehavior functionChoiceBehavior) {
            this.functionChoiceBehavior = functionChoiceBehavior;
            return this;
        }

        /**
         * 构建 AgentInvokeOptions 实例
         */
        public AgentInvokeOptions build() {
            return new AgentInvokeOptions(this);
        }
    }

    @Override
    public String toString() {
        return String.format("AgentInvokeOptions{" +
                "hasKernel=%s, hasKernelArguments=%s, hasAdditionalInstructions=%s, " +
                "hasInvocationContext=%s, functionChoiceBehavior=%s}",
                kernel != null,
                kernelArguments != null,
                additionalInstructions != null && !additionalInstructions.isEmpty(),
                invocationContext != null,
                functionChoiceBehavior != null ? functionChoiceBehavior.toString() : "null");
    }
}