package com.kumhosunny.app.ai.agent;

import com.microsoft.semantickernel.Kernel;
import com.microsoft.semantickernel.orchestration.InvocationContext;
import com.microsoft.semantickernel.services.chatcompletion.ChatCompletionService;
import com.microsoft.semantickernel.services.chatcompletion.ChatHistory;
import com.microsoft.semantickernel.services.chatcompletion.ChatMessageContent;
import com.microsoft.semantickernel.services.chatcompletion.AuthorRole;
import reactor.core.publisher.Mono;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import com.kumhosunny.app.ai.kernel.adapter.ModelRouterChatCompletionService;
import com.kumhosunny.chat.dto.ChatCompletionRequest;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.ArrayList;

/**
 * 聊天完成 Agent 实现
 * 按照 Microsoft Semantic Kernel 官方标准实现
 * 参考:
 * https://learn.microsoft.com/zh-cn/semantic-kernel/frameworks/agent/agent-functions?pivots=programming-language-java
 */
public class ChatCompletionAgent {

    private static final Logger log = LoggerFactory.getLogger(ChatCompletionAgent.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    private final Kernel kernel;
    private final String name;
    private final String instructions;
    private final String workflowInfo;
    private final InvocationContext defaultInvocationContext;
    private final Map<String, Object> defaultArguments;

    private ChatCompletionAgent(Builder builder) {
        this.kernel = builder.kernel;
        this.name = builder.name;
        this.instructions = builder.instructions;
        this.workflowInfo = builder.workflowInfo;
        this.defaultInvocationContext = builder.invocationContext;
        this.defaultArguments = builder.arguments;

        // 验证必需的组件
        if (this.kernel == null) {
            throw new IllegalArgumentException("Kernel cannot be null");
        }

        try {
            ChatCompletionService chatService = this.kernel.getService(ChatCompletionService.class);
            if (chatService == null) {
                throw new IllegalStateException("Kernel must have a ChatCompletionService");
            }
        } catch (Exception e) {
            throw new IllegalStateException("Failed to get ChatCompletionService from Kernel: " + e.getMessage(), e);
        }

        log.info("Created ChatCompletionAgent: name='{}', plugins={}",
                name, kernel.getPlugins().size());
    }

    /**
     * 调用 Agent - 传递字符串消息
     * 根据官方 API: agent.invokeAsync("What is the capital of France?")
     */
    public Mono<AgentResult> invokeAsync(String message) {
        return invokeAsync(message, null, null);
    }

    /**
     * 调用 Agent - 传递消息和线程
     * 根据官方 API: agent.invokeAsync("message", existingAgentThread)
     */
    public Mono<AgentResult> invokeAsync(String message, AgentThread thread) {
        return invokeAsync(message, thread, null);
    }

    /**
     * 调用 Agent - 传递消息、线程和选项
     * 根据官方 API: agent.invokeAsync("message", thread, options)
     */
    public Mono<AgentResult> invokeAsync(String message, AgentThread thread, AgentInvokeOptions options) {
        if (message != null) {
            ChatMessageContent<?> userMessage = new ChatMessageContent<>(AuthorRole.USER, message);
            return invokeAsync(userMessage, thread, options);
        } else {
            return invokeAsync((ChatMessageContent<?>) null, thread, options);
        }
    }

    /**
     * 调用 Agent - 传递 ChatMessageContent、线程和选项
     */
    public Mono<AgentResult> invokeAsync(ChatMessageContent<?> message, AgentThread thread,
            AgentInvokeOptions options) {
        return invokeAsync(message != null ? List.of(message) : null, thread, options);
    }

    /**
     * 核心调用方法 - 传递消息列表、线程和选项
     * 这是所有其他重载方法的最终实现
     */
    public Mono<AgentResult> invokeAsync(List<ChatMessageContent<?>> messages, AgentThread thread,
            AgentInvokeOptions options) {
        return Mono.fromCallable(() -> {
            log.debug("Agent '{}' invokeAsync called with {} messages", name,
                    messages != null ? messages.size() : 0);

            // 1. 确定使用的线程
            AgentThread activeThread = thread != null ? thread : new AgentThread();

            // 2. 确定使用的 Kernel 和配置
            Kernel activeKernel = (options != null && options.getKernel() != null)
                    ? options.getKernel()
                    : this.kernel;

            InvocationContext activeContext = (options != null && options.getInvocationContext() != null)
                    ? options.getInvocationContext()
                    : this.defaultInvocationContext;

            // 3. 构建聊天历史
            ChatHistory chatHistory = buildChatHistory(activeThread, messages, options);

            // 4. 获取聊天完成服务
            ChatCompletionService chatService = activeKernel.getService(ChatCompletionService.class);

            // 5. 执行聊天完成
            List<ChatMessageContent<?>> response = chatService
                    .getChatMessageContentsAsync(chatHistory, activeKernel, activeContext)
                    .block();

            // 6. 处理响应
            if (response != null && !response.isEmpty()) {
                ChatMessageContent<?> responseMessage = response.get(0);

                // 将响应添加到线程历史
                activeThread.addMessage(responseMessage);

                // 创建结果
                return new AgentResult(responseMessage, activeThread, this.name);
            } else {
                throw new RuntimeException("No response received from chat completion service");
            }
        })
                .onErrorMap(ex -> {
                    log.error("Error in Agent '{}' invokeAsync: {}", name, ex.getMessage(), ex);
                    return new RuntimeException("Agent invocation failed: " + ex.getMessage(), ex);
                });
    }

    /**
     * 构建聊天历史
     */
    private ChatHistory buildChatHistory(AgentThread thread, List<ChatMessageContent<?>> newMessages,
            AgentInvokeOptions options) {
        ChatHistory chatHistory = new ChatHistory();

        // 1. 添加系统指令
        String systemInstructions = buildSystemInstructions(options);
        if (systemInstructions != null && !systemInstructions.isEmpty()) {
            chatHistory.addSystemMessage(systemInstructions);
        }

        // 2. 添加线程中的历史消息
        if (thread != null && !thread.isEmpty()) {
            for (ChatMessageContent<?> message : thread.getMessages()) {
                chatHistory.addMessage(message);
            }
        }

        // 3. 添加新消息
        if (newMessages != null) {
            for (ChatMessageContent<?> message : newMessages) {
                chatHistory.addMessage(message);
                // 同时添加到线程历史
                if (thread != null) {
                    thread.addMessage(message);
                }
            }
        }

        return chatHistory;
    }

    /**
     * 构建系统指令
     */
    private String buildSystemInstructions(AgentInvokeOptions options) {
        StringBuilder instructions = new StringBuilder();

        // 基础指令
        if (this.instructions != null && !this.instructions.isEmpty()) {
            instructions.append(this.instructions);
        }

        // 新增：附加工作流信息
        if (this.workflowInfo != null && !this.workflowInfo.isEmpty()) {
            instructions.append("\n\n");
            instructions.append(this.workflowInfo);
        }

        // 附加指令
        if (options != null && options.getAdditionalInstructions() != null &&
                !options.getAdditionalInstructions().isEmpty()) {
            if (instructions.length() > 0) {
                instructions.append("\n\n");
            }
            instructions.append("附加指令：").append(options.getAdditionalInstructions());
        }

        return instructions.toString();
    }

    /**
     * 获取 Agent 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 获取 Agent 指令
     */
    public String getInstructions() {
        return instructions;
    }

    /**
     * 获取关联的 Kernel
     */
    public Kernel getKernel() {
        return kernel;
    }

    /**
     * 获取默认调用上下文
     */
    public InvocationContext getDefaultInvocationContext() {
        return defaultInvocationContext;
    }

    /**
     * 流式调用 Agent - 支持 Server-Sent Events (SSE)
     * 按照 OpenAI Chat Completions API 标准格式返回流式数据
     * 支持工具调用的结构化输出
     * 
     * @param message    用户消息
     * @param thread     Agent 线程
     * @param options    调用选项
     * @param sseEmitter SSE 发射器，用于实时推送响应
     * @return 异步结果
     */
    public CompletableFuture<AgentResult> invokeStreamAsync(String message, AgentThread thread,
            AgentInvokeOptions options, SseEmitter sseEmitter) {

        return CompletableFuture.supplyAsync(() -> {
            AgentThread activeThread = thread != null ? thread : new AgentThread();
            String requestId = "chatcmpl-" + System.currentTimeMillis();
            long timestamp = System.currentTimeMillis() / 1000L;
            int maxLoops = 5; // 设置最大循环次数，防止无限循环

            try {
                // 1. 准备 Kernel 和初始聊天历史
                Kernel activeKernel = (options != null && options.getKernel() != null) ? options.getKernel()
                        : this.kernel;
                InvocationContext activeContext = (options != null && options.getInvocationContext() != null)
                        ? options.getInvocationContext()
                        : this.defaultInvocationContext;
                ChatCompletionService chatService = activeKernel.getService(ChatCompletionService.class);

                List<ChatMessageContent<?>> newMessages = new ArrayList<>();
                if (message != null && !message.isEmpty()) {
                    newMessages.add(new ChatMessageContent<>(AuthorRole.USER, message));
                }
                ChatHistory chatHistory = buildChatHistory(activeThread, newMessages, options);

                // 2. 进入推理-行动循环 (ReAct Loop)
                for (int loopCount = 0; loopCount < maxLoops; loopCount++) {
                    log.info("Agent ReAct Loop - 第 {}/{} 轮", loopCount + 1, maxLoops);

                    // 3. 调用模型进行思考，获取下一步行动
                    List<ChatMessageContent<?>> initialResponse = chatService
                            .getChatMessageContentsAsync(chatHistory, activeKernel, activeContext)
                            .block();

                    ChatMessageContent<?> responseMessage = (initialResponse != null && !initialResponse.isEmpty())
                            ? initialResponse.get(0)
                            : null;

                    if (responseMessage == null) {
                        throw new RuntimeException("Agent 在第 " + (loopCount + 1) + " 轮思考中没有产生响应。");
                    }

                    // 将模型的思考/回复加入历史记录
                    chatHistory.addMessage(responseMessage);
                    activeThread.addMessage(responseMessage);

                    // 4. 检查模型是否决定调用工具。
                    // 我们通过检查响应内容是否为我们自定义的、表示工具执行结果的JSON来判断。
                    String contentStr = responseMessage.getContent() != null ? responseMessage.getContent().toString()
                            : "";
                    boolean toolCallWasExecuted = contentStr.trim().startsWith("{")
                            && contentStr.contains("\"toolNames\"");

                    if (toolCallWasExecuted) {
                        // 是的，工具刚刚被执行。结果已经加入历史记录。
                        // 我们需要继续循环，让模型看到这个结果并进行下一步思考。
                        log.info("工具调用已执行，结果已添加至历史记录。继续 ReAct 循环。");
                        continue;
                    } else {
                        // 不是工具调用结果，这意味着模型已经生成了最终的文本回复。
                        // 循环结束。
                        log.info("模型已生成最终文本回复，结束 ReAct 循环。");
                        break;
                    }
                }

                // 如果循环达到最大次数仍未结束
                log.info("Agent 执行循环结束，准备生成最终流式响应。");

                // 最终步骤：使用包含所有工具调用历史的完整上下文，进行一次最终的流式调用以生成回复。
                if (!(chatService instanceof ModelRouterChatCompletionService)) {
                    throw new IllegalStateException(
                            "ChatCompletionService is not an instance of ModelRouterChatCompletionService, cannot perform direct streaming.");
                }
                ModelRouterChatCompletionService modelRouterService = (ModelRouterChatCompletionService) chatService;

                // 使用当前最终的聊天历史进行流式调用
                ChatCompletionRequest streamRequest = modelRouterService.convertToCompletionRequest(
                        chatHistory, activeKernel, activeContext);
                streamRequest.setStream(true);

                StringBuilder fullContent = new StringBuilder();

                // 直接调用底层的流式服务
                modelRouterService.streamRequest(streamRequest)
                        .doOnNext(chunk -> {
                            if (chunk != null && chunk.getChoices() != null && !chunk.getChoices().isEmpty()) {
                                String chunkContent = chunk.getChoices().get(0).getDelta().getContent();
                                if (chunkContent != null) {
                                    fullContent.append(chunkContent);
                                    try {
                                        String jsonChunk = objectMapper.writeValueAsString(chunk);
                                        sendSseData(sseEmitter, jsonChunk);
                                    } catch (com.fasterxml.jackson.core.JsonProcessingException e) {
                                        log.error("Error serializing chat chunk to JSON", e);
                                    }
                                }
                            }
                        })
                        .doOnComplete(() -> {
                            log.info("最终回复流式调用完成，总内容长度: {} 字符", fullContent.length());
                            sendOpenAIStreamEnd(sseEmitter, requestId, timestamp);
                            sseEmitter.complete();
                        })
                        .doOnError(err -> {
                            log.error("最终回复流式调用出错: {}", err.getMessage(), err);
                            sendToolErrorNotification(sseEmitter, requestId, timestamp, "Agent",
                                    "流式调用失败: " + err.getMessage());
                            sseEmitter.completeWithError(err);
                        })
                        .blockLast(); // 等待流完成

                ChatMessageContent<?> finalMessage = new ChatMessageContent<>(AuthorRole.ASSISTANT,
                        fullContent.toString());
                activeThread.addMessage(finalMessage);
                return new AgentResult(finalMessage, activeThread, this.name);

            } catch (Exception e) {
                log.error("Error in Agent '{}' invokeStreamAsync: {}", name, e.getMessage(), e);
                sendToolErrorNotification(sseEmitter, requestId, timestamp, "系统错误", "处理过程中发生错误: " + e.getMessage());
                sseEmitter.completeWithError(
                        new RuntimeException("Agent stream invocation failed: " + e.getMessage(), e));
                return new AgentResult(new ChatMessageContent<>(AuthorRole.ASSISTANT, "处理请求时出错。"), activeThread,
                        this.name);
            }
        });
    }

    /**
     * 流式调用 Agent - 简化版
     */
    public CompletableFuture<AgentResult> invokeStreamAsync(String message, SseEmitter sseEmitter) {
        return invokeStreamAsync(message, null, null, sseEmitter);
    }

    /**
     * 发送工具错误通知 - OpenAI 格式
     */
    private void sendToolErrorNotification(SseEmitter sseEmitter, String requestId, long timestamp, String toolName,
            String error) {
        if (!isEmitterActive(sseEmitter)) {
            return;
        }

        try {
            // 发送工具错误信息
            String toolErrorInfo = String.format("{\"type\":\"tool_error\",\"tool_name\":\"%s\",\"error\":\"%s\"}",
                    escapeJson(toolName), escapeJson(error));

            String deltaData = String.format(
                    "{\"id\":\"%s\",\"object\":\"chat.completion.chunk\",\"created\":%d,\"model\":\"%s\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\"%s\"},\"finish_reason\":null}]}",
                    requestId, timestamp, name, escapeJson(toolErrorInfo));

            sendSseData(sseEmitter, deltaData);
            sseEmitter.completeWithError(new RuntimeException(error));
            log.debug("Sent tool error notification: {} - {}", toolName, error);

        } catch (Exception e) {
            log.error("Error sending tool error notification: {}", e.getMessage());
        }
    }

    /**
     * 发送 OpenAI 格式的流式结束标志
     */
    private void sendOpenAIStreamEnd(SseEmitter sseEmitter, String requestId, long timestamp) {
        if (!isEmitterActive(sseEmitter)) {
            return;
        }

        try {
            String endData = String.format(
                    "{\"id\":\"%s\",\"object\":\"chat.completion.chunk\",\"created\":%d,\"model\":\"%s\",\"choices\":[{\"index\":0,\"delta\":{},\"finish_reason\":\"stop\"}]}",
                    requestId, timestamp, name);

            sendSseData(sseEmitter, endData);
            sendSseData(sseEmitter, "[DONE]");
            sseEmitter.complete();

        } catch (Exception e) {
            log.error("Error sending OpenAI stream end: {}", e.getMessage());
        }
    }

    /**
     * 发送工具开始调用事件
     */
    private void sendToolStartNotification(SseEmitter sseEmitter, String requestId, long timestamp, String toolName) {
        if (!isEmitterActive(sseEmitter)) {
            return;
        }

        try {
            // 发送工具开始调用信息
            String toolStartInfo = String.format("{\"type\":\"tool_start\",\"tool_name\":\"%s\"}",
                    escapeJson(toolName));

            String deltaData = String.format(
                    "{\"id\":\"%s\",\"object\":\"chat.completion.chunk\",\"created\":%d,\"model\":\"%s\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\"%s\"},\"finish_reason\":null}]}",
                    requestId, timestamp, name, escapeJson(toolStartInfo));

            sendSseData(sseEmitter, deltaData);
            log.debug("Sent tool start notification: {}", toolName);

        } catch (Exception e) {
            log.error("Error sending tool start notification: {}", e.getMessage());
        }
    }

    /**
     * 发送工具调用结束事件
     */
    private void sendToolEndNotification(SseEmitter sseEmitter, String requestId, long timestamp, String toolName) {
        if (!isEmitterActive(sseEmitter)) {
            return;
        }

        try {
            // 发送工具调用结束信息
            String toolEndInfo = String.format("{\"type\":\"tool_end\",\"tool_name\":\"%s\"}",
                    escapeJson(toolName));

            String deltaData = String.format(
                    "{\"id\":\"%s\",\"object\":\"chat.completion.chunk\",\"created\":%d,\"model\":\"%s\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\"%s\"},\"finish_reason\":null}]}",
                    requestId, timestamp, name, escapeJson(toolEndInfo));

            sendSseData(sseEmitter, deltaData);
            log.debug("Sent tool end notification: {}", toolName);

        } catch (Exception e) {
            log.error("Error sending tool end notification: {}", e.getMessage());
        }
    }

    /**
     * 安全地发送 SSE 数据
     */
    private void sendSseData(SseEmitter sseEmitter, String data) {
        if (!isEmitterActive(sseEmitter)) {
            return;
        }

        try {
            sseEmitter.send(SseEmitter.event().data(data));
            // log.debug("Sent SSE data: {}", data.length() > 100 ? data.substring(0, 100) +
            // "..." : data);
        } catch (Exception e) {
            log.debug("Failed to send SSE data (connection may be closed): {}", e.getMessage());
        }
    }

    /**
     * 检查 SseEmitter 是否仍然活跃
     */
    private boolean isEmitterActive(SseEmitter sseEmitter) {
        try {
            return sseEmitter != null;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * JSON 字符串转义
     */
    private String escapeJson(String str) {
        if (str == null)
            return "";
        return str.replace("\\", "\\\\")
                .replace("\"", "\\\"")
                .replace("\b", "\\b")
                .replace("\f", "\\f")
                .replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t");
    }

    /**
     * 创建 Builder 实例
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * Builder 模式实现
     */
    public static class Builder {
        public static final String DEFAULT_INSTRUCTIONS = "你是一个智能助手，可以使用工具来回答问题。\n\n" +
                "## 工具使用规则:\n" +
                "1. **评估需求**: 仔细分析用户的问题，判断是否需要使用工具。如果问题可以直接回答，就不要使用工具。\n" +
                "2. **选择工具**: 如果需要工具，请参考下面的可用工作流列表，并选择最合适的。\n" +
                "3. **调用工作流**: 使用 `N8nPlugin_callWorkflow` 函数，并提供所选工作流的 `workflowId` 和用户问题作为 `input`。\n" +
                "4. **响应格式**: 你的工具调用请求必须是严格的JSON格式。\n\n" +
                "## 可用的工作流:";

        private Kernel kernel;
        private String name;
        private String instructions = DEFAULT_INSTRUCTIONS;
        private String workflowInfo;
        private InvocationContext invocationContext;
        private Map<String, Object> arguments;

        /**
         * 设置 Kernel
         */
        public Builder withKernel(Kernel kernel) {
            this.kernel = kernel;
            return this;
        }

        /**
         * 设置 Agent 名称
         */
        public Builder withName(String name) {
            this.name = name;
            return this;
        }

        /**
         * 新增：设置工作流信息
         */
        public Builder withWorkflowInfo(String workflowInfo) {
            this.workflowInfo = workflowInfo;
            return this;
        }

        /**
         * 设置默认调用上下文
         */
        public Builder withInvocationContext(InvocationContext invocationContext) {
            this.invocationContext = invocationContext;
            return this;
        }

        /**
         * 构建 ChatCompletionAgent 实例
         */
        public ChatCompletionAgent build() {
            return new ChatCompletionAgent(this);
        }
    }

    @Override
    public String toString() {
        return String.format("ChatCompletionAgent{name='%s', plugins=%d}",
                name, kernel != null ? kernel.getPlugins().size() : 0);
    }
}