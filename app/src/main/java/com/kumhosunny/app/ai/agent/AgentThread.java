package com.kumhosunny.app.ai.agent;

import com.microsoft.semantickernel.services.chatcompletion.ChatMessageContent;
import reactor.core.publisher.Mono;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Agent 线程管理类
 * 按照 Microsoft Semantic Kernel 官方标准实现
 * 参考:
 * https://learn.microsoft.com/zh-cn/semantic-kernel/frameworks/agent/agent-api?pivots=programming-language-java
 */
public class AgentThread {

    private static final Logger log = LoggerFactory.getLogger(AgentThread.class);

    private final String threadId;
    private final List<ChatMessageContent<?>> messages;
    private boolean isDeleted = false;

    /**
     * 创建新的 AgentThread
     */
    public AgentThread() {
        this.threadId = UUID.randomUUID().toString();
        this.messages = new CopyOnWriteArrayList<>();
        log.debug("Created new AgentThread: {}", threadId);
    }

    /**
     * 使用指定 ID 创建 AgentThread
     */
    public AgentThread(String threadId) {
        this.threadId = threadId;
        this.messages = new CopyOnWriteArrayList<>();
        log.debug("Created AgentThread with ID: {}", threadId);
    }

    /**
     * 获取线程 ID
     */
    public String getThreadId() {
        return threadId;
    }

    /**
     * 获取所有消息（只读副本）
     */
    public List<ChatMessageContent<?>> getMessages() {
        return new ArrayList<>(messages);
    }

    /**
     * 添加消息到线程
     */
    public void addMessage(ChatMessageContent<?> message) {
        if (isDeleted) {
            throw new IllegalStateException("Cannot add message to deleted thread: " + threadId);
        }

        if (message != null) {
            messages.add(message);
            log.debug("Added message to thread {}: {}", threadId, message.getAuthorRole());
        }
    }

    /**
     * 添加多个消息到线程
     */
    public void addMessages(List<ChatMessageContent<?>> newMessages) {
        if (newMessages != null) {
            for (ChatMessageContent<?> message : newMessages) {
                addMessage(message);
            }
        }
    }

    /**
     * 获取消息数量
     */
    public int getMessageCount() {
        return messages.size();
    }

    /**
     * 检查线程是否为空
     */
    public boolean isEmpty() {
        return messages.isEmpty();
    }

    /**
     * 检查线程是否已被删除
     */
    public boolean isDeleted() {
        return isDeleted;
    }

    /**
     * 删除线程
     * 按照官方 API 标准实现异步删除
     */
    public Mono<Void> deleteAsync() {
        return Mono.fromRunnable(() -> {
            log.info("Deleting AgentThread: {}", threadId);
            this.isDeleted = true;
            this.messages.clear();
        });
    }

    /**
     * 创建线程的副本（用于 Agent 特定的上下文）
     */
    public AgentThread clone() {
        AgentThread cloned = new AgentThread(this.threadId + "_clone_" + System.currentTimeMillis());
        cloned.addMessages(this.messages);
        return cloned;
    }

    /**
     * 清空消息历史（保留线程）
     */
    public void clearMessages() {
        if (!isDeleted) {
            messages.clear();
            log.debug("Cleared messages for thread: {}", threadId);
        }
    }

    @Override
    public String toString() {
        return String.format("AgentThread{id='%s', messages=%d, deleted=%s}",
                threadId, messages.size(), isDeleted);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null || getClass() != obj.getClass())
            return false;
        AgentThread that = (AgentThread) obj;
        return threadId.equals(that.threadId);
    }

    @Override
    public int hashCode() {
        return threadId.hashCode();
    }
}