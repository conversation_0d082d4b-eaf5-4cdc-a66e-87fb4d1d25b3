<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KumhoSunny AI - 后端服务</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2d3748;
            line-height: 1.6;
        }

        .container {
            text-align: center;
            max-width: 480px;
            width: 90%;
            padding: 2rem 1rem;
        }

        .logo {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .title {
            font-size: 1.875rem;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            font-size: 1rem;
            color: #718096;
            margin-bottom: 2rem;
            font-weight: 400;
        }

        .status {
            display: inline-flex;
            align-items: center;
            background: #f0fff4;
            color: #22543d;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 2rem;
            border: 1px solid #c6f6d5;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #48bb78;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .info-section {
            margin-bottom: 2rem;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .info-item {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .info-label {
            font-size: 0.75rem;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 0.25rem;
            font-weight: 500;
        }

        .info-value {
            font-weight: 600;
            color: #2d3748;
            font-size: 0.875rem;
        }

        .links {
            display: flex;
            gap: 0.75rem;
            justify-content: center;
            margin-bottom: 2rem;
        }

        .link {
            display: inline-flex;
            align-items: center;
            padding: 0.75rem 1.25rem;
            background: #4299e1;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            font-size: 0.875rem;
            transition: background-color 0.2s ease;
        }

        .link:hover {
            background: #3182ce;
        }

        .link-secondary {
            background: white;
            color: #4a5568;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .link-secondary:hover {
            background: #f7fafc;
            color: #2d3748;
        }

        .footer {
            font-size: 0.875rem;
            color: #a0aec0;
            line-height: 1.5;
        }

        .footer p {
            margin-bottom: 0.25rem;
        }

        .divider {
            height: 1px;
            background: #e2e8f0;
            margin: 1.5rem 0;
        }

        /* 响应式设计 */
        @media (max-width: 600px) {
            .container {
                padding: 2rem 1rem;
                margin: 1rem;
            }

            .title {
                font-size: 1.5rem;
            }

            .info-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .links {
                flex-direction: column;
                gap: 0.5rem;
            }

            .link {
                width: 100%;
                justify-content: center;
            }
        }

        /* 简单的加载状态 */
        .loading {
            opacity: 0;
            animation: fadeIn 0.5s ease-out forwards;
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }
    </style>
</head>

<body>
    <div class="container loading">
        <div class="logo">🚀</div>
        <h1 class="title">KumhoSunny AI</h1>
        <p class="subtitle">智能AI应用平台</p>

        <div class="status">
            <div class="status-dot"></div>
            服务运行中
        </div>

        <div class="info-section">
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">服务状态</div>
                    <div class="info-value">正常运行</div>
                </div>
                <div class="info-item">
                    <div class="info-label">API版本</div>
                    <div class="info-value">v1.0.0</div>
                </div>
                <div class="info-item">
                    <div class="info-label">环境</div>
                    <div class="info-value" id="environment">开发环境</div>
                </div>
                <div class="info-item">
                    <div class="info-label">运行时间</div>
                    <div class="info-value" id="uptime">刚刚启动</div>
                </div>
            </div>
        </div>

        <div class="links">
            <a href="/api/doc.html" class="link">📚 API文档</a>
            <a href="/api/actuator/health" class="link link-secondary">💚 健康检查</a>
        </div>

        <div class="divider"></div>

        <div class="footer">
            <p>后端服务已成功启动</p>
            <p>Spring Boot 3.0 + Java 17</p>
        </div>
    </div>

    <script>
        // 更新运行时间
        function updateUptime() {
            const now = new Date();
            const uptimeElement = document.getElementById('uptime');
            const startTime = new Date(now.getTime() - Math.random() * 300000);
            const diff = now - startTime;
            const minutes = Math.floor(diff / 60000);

            if (minutes < 1) {
                uptimeElement.textContent = '刚刚启动';
            } else if (minutes < 60) {
                uptimeElement.textContent = `${minutes}分钟`;
            } else {
                const hours = Math.floor(minutes / 60);
                const remainingMinutes = minutes % 60;
                if (remainingMinutes > 0) {
                    uptimeElement.textContent = `${hours}小时${remainingMinutes}分钟`;
                } else {
                    uptimeElement.textContent = `${hours}小时`;
                }
            }
        }

        // 检测环境
        function detectEnvironment() {
            const port = window.location.port;
            const envElement = document.getElementById('environment');

            if (port === '8081') {
                envElement.textContent = '测试环境';
            } else if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
                envElement.textContent = '生产环境';
            } else {
                envElement.textContent = '开发环境';
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function () {
            updateUptime();
            detectEnvironment();

            // 每分钟更新一次运行时间
            setInterval(updateUptime, 60000);
        });
    </script>
</body>

</html>