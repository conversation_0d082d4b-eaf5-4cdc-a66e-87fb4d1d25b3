# 生产环境配置
server:
  port: 3333

# 数据库配置 - 生产环境可以通过环境变量覆盖
spring:
  datasource:
    url: ${DB_URL:jdbc:mysql://**************:3306/kumhosunny_ai?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8}
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:123456}
  data:
    redis:
      host: ${REDIS_HOST:prod-redis}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}

# 日志级别
logging:
  level:
    com.kumhosunny: INFO
    org.springframework: WARN
    root: WARN

# Qdrant向量数据库 - 生产环境配置
qdrant:
  host: ${QDRANT_HOST:qdrant-server}
  port: ${QDRANT_PORT:6334}
  use-tls: ${QDRANT_USE_TLS:true}
  api-key: ${QDRANT_API_KEY:}
  timeout: ${QDRANT_TIMEOUT:60000}

# 生产环境特定配置
app:
  jwt:
    secret: "${JWT_SECRET:69+5e0GJcBkYfhLEWwNn9jV360vuujxhIAsSZPEV0UIqscp5bJNFj6xcyNo9XzixuUCKowK/N5JktjFqxpqYA==}"
    expiration: ${JWT_EXPIRATION:86400}
  file:
    upload-path: ${FILE_UPLOAD_PATH:/data/uploads/}
    max-size: ${FILE_MAX_SIZE:10485760}
  ai:
    openai:
      api-key: ${OPENAI_API_KEY:}
      base-url: ${OPENAI_BASE_URL:https://api.openai.com}
      model: ${OPENAI_MODEL:gpt-3.5-turbo} 