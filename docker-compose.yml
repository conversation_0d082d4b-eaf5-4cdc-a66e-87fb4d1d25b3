version: '3.8'

services:
  # Qdrant 向量数据库
  kumhosunny-qdrant:
    image: qdrant/qdrant:latest
    container_name: kumhosunny-qdrant
    restart: unless-stopped
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333
      QDRANT__SERVICE__GRPC_PORT: 6334
    networks:
      - kumhosunny-network

  # MinIO 对象存储
  minio:
    image: "quay.io/minio/minio:RELEASE.2022-08-02T23-59-16Z"
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - "./minio/data1:/data1"
      - "./minio/data2:/data2"
    command: server --console-address ":9001" /data1 /data2
    environment:
      - MINIO_ROOT_USER=admin
      - MINIO_ROOT_PASSWORD=Kumhosunny9527
      #- MINIO_ACCESS_KEY=AKIAIOSFODNN7EXAMPLE
      #- MINIO_SECRET_KEY=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://minio:9000/minio/health/live" ]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - kumhosunny-network

  # ElasticSearch
  kumhosunny-elasticsearch:
    build: .
    container_name: kumhosunny-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - kumhosunny-network

  # Unstructured 开源文档处理库
  kumhosunny-unstructured:
    image: downloads.unstructured.io/unstructured-io/unstructured:latest
    container_name: kumhosunny-unstructured
    restart: unless-stopped
    working_dir: /app
    volumes:
      - unstructured_docs:/app/docs
      - unstructured_output:/app/output
    environment:
      - PYTHONUNBUFFERED=1
      - DO_NOT_TRACK=false
    command: tail -f /dev/null
    networks:
      - kumhosunny-network

  # Unstructured API 服务
  kumhosunny-unstructured-api:
    image: downloads.unstructured.io/unstructured-io/unstructured-api:latest
    container_name: kumhosunny-unstructured-api
    restart: unless-stopped
    ports:
      - "8100:8000"
    volumes:
      - unstructured_docs:/app/docs
      - unstructured_output:/app/output
      - unstructured_cache:/app/cache
    environment:
      - PORT=8000
      - HOST=0.0.0.0
      - UNSTRUCTURED_PARALLEL_MODE_ENABLED=false
      - UNSTRUCTURED_PARALLEL_MODE_THREADS=3
      - UNSTRUCTURED_PARALLEL_MODE_SPLIT_SIZE=1
      - UNSTRUCTURED_PARALLEL_RETRY_ATTEMPTS=2
      - UNSTRUCTURED_MEMORY_FREE_MINIMUM_MB=2048
      - SCARF_NO_ANALYTICS=false
      - DO_NOT_TRACK=false
    depends_on:
      - kumhosunny-unstructured
    networks:
      - kumhosunny-network

  # Kibana 可视化界面
  kumhosunny-kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: kumhosunny-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    volumes:
      - /home/<USER>/kibanaConfig:/usr/share/kibana/config
    environment:
      ELASTICSEARCH_HOSTS: http://kumhosunny-elasticsearch:9200
    depends_on:
      - kumhosunny-elasticsearch
    networks:
      - kumhosunny-network

  # SearxNG 本地搜索引擎
  kumhosunny-searxng:
    image: searxng/searxng:latest
    container_name: kumhosunny-searxng
    restart: unless-stopped
    ports:
      - "8888:8080"
    environment:
      - BASE_URL=http://localhost:8888/
    volumes:
      - /home/<USER>/searxng_settings:/etc/searxng
    networks:
      - kumhosunny-network

  # Judge0 代码沙箱 - 数据库
  kumhosunny-judge0-db:
    image: postgres:13
    restart: unless-stopped
    environment:
      POSTGRES_DB: judge0
      POSTGRES_USER: judge0
      POSTGRES_PASSWORD: judge0
    volumes:
      - judge0_db_data:/var/lib/postgresql/data
    networks:
      - kumhosunny-network

  # Judge0 代码沙箱 - 一体化服务（API + Worker）
  kumhosunny-judge0:
    image: judge0/judge0:1.13.1-extra
    restart: unless-stopped
    ports:
      - "2358:2358"
    depends_on:
      - kumhosunny-judge0-db
      - kumhosunny-judge0-redis
    environment:
      - DATABASE_URL=******************************************************
      - ENABLE_AUTH=false
      - ALLOWED_ORIGINS=*
      - MAX_WORKERS=2
      - REDIS_URL=redis://kumhosunny-judge0-redis
    networks:
      - kumhosunny-network

  # Judge0 Redis
  kumhosunny-judge0-redis:
    image: redis:6
    restart: unless-stopped
    networks:
      - kumhosunny-network

volumes:
  qdrant_data:
  minio_data:
  elasticsearch_data:
  unstructured_docs:
  unstructured_output:
  unstructured_cache:
  judge0_db_data:

networks:
  kumhosunny-network:
    driver: bridge
