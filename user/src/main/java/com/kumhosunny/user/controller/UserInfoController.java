package com.kumhosunny.user.controller;

import com.kumhosunny.common.entity.EmployeeEntity;
import com.kumhosunny.common.repository.EmployeeRepository;
import com.kumhosunny.common.result.ApiResponse;
import com.kumhosunny.user.util.JwtUtil;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 用户信息控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/user")
public class UserInfoController {

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 获取当前登录用户信息
     * 
     * @param request HTTP请求对象
     * @return 用户信息
     */
    @GetMapping("/info")
    public ApiResponse<EmployeeEntity> getCurrentUserInfo(HttpServletRequest request) {
        try {
            String token = request.getHeader("Authorization");
            if (token == null || !token.startsWith("Bearer ")) {
                return ApiResponse.error("获取用户信息失败：缺少有效的Token");
            }
            token = token.substring(7);

            Claims claims = jwtUtil.parseToken(token);
            if (claims == null) {
                return ApiResponse.error("获取用户信息失败：Token无效或已过期");
            }

            Integer userId = (Integer) claims.get("userId");

            if (userId == null) {
                return ApiResponse.error("获取用户信息失败：未找到用户ID");
            }

            // 查询用户详细信息
            Optional<EmployeeEntity> userOptional = employeeRepository.findById(Long.valueOf(userId));

            if (userOptional.isEmpty()) {
                return ApiResponse.error("用户不存在");
            }

            EmployeeEntity user = userOptional.get();
            // 不返回密码
            user.setLoginPwd(null);

            return ApiResponse.success("获取用户信息成功", user);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return ApiResponse.error("获取用户信息失败: " + e.getMessage());
        }
    }

}