package com.kumhosunny.user.controller;

import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyun.dingtalkcontact_1_0.models.GetUserHeaders;
import com.aliyun.dingtalkcontact_1_0.models.GetUserResponseBody;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenRequest;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.google.common.collect.Lists;
import com.kumhosunny.user.service.LoginDingService;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/ddlogin")
public class DingLoginController {

    @Autowired
    private LoginDingService loginService;

    @Value(value = "${dingtalk.appKey}")
    private String appKey;

    @Value(value = "${dingtalk.appSecret}")
    private String appSecret;

    private final String BASIC_URL = "https://oapi.dingtalk.com";

    @Value(value = "${dingtalk.callbackAddress}")
    private String callbackAddress;

    /**
     * 钉钉鉴权客户端
     */
    public static com.aliyun.dingtalkoauth2_1_0.Client authClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkoauth2_1_0.Client(config);
    }

    /**
     * 钉钉联系人客户端
     */
    public static com.aliyun.dingtalkcontact_1_0.Client contactClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkcontact_1_0.Client(config);
    }

    /**
     * 钉钉登录回调接口
     * 接收钉钉传递的授权码，换取用户访问令牌，再获取用户信息
     *
     * @param response HttpServletResponse
     * @param authCode 钉钉授权码
     * @throws Exception 异常
     */
    @RequestMapping(value = "/auth", method = RequestMethod.GET)
    public void dingLogin(HttpServletResponse response, @RequestParam(value = "authCode") String authCode)
            throws Exception {
        log.info("钉钉登录回调，authCode: {}", authCode);

        try {
            // 1. 获取用户访问令牌
            String accessToken = getUserAccessToken(authCode);

            // 2. 使用访问令牌获取用户信息
            String token = getUserInfo(accessToken);

            // 3. 重定向到前端回调页面，带上系统token
            response.setCharacterEncoding("UTF-8");
            response.sendRedirect(callbackAddress + "?token=" + token);
        } catch (Exception e) {
            log.error("钉钉登录处理失败", e);
            response.sendRedirect(callbackAddress + "?error="+ URLUtil.encode(e.getMessage()));
        }
    }

    /**
     * 获取用户访问令牌
     * 使用授权码换取用户访问令牌
     *
     * @param authCode 钉钉授权码
     * @return 用户访问令牌
     * @throws Exception 异常
     */
    private String getUserAccessToken(String authCode) throws Exception {
        com.aliyun.dingtalkoauth2_1_0.Client client = authClient();

        GetUserTokenRequest getUserTokenRequest = new GetUserTokenRequest()
                .setClientId(appKey) // 应用的AppKey
                .setClientSecret(appSecret) // 应用的AppSecret
                .setCode(authCode) // 授权码
                .setGrantType("authorization_code"); // 授权类型

        GetUserTokenResponse getUserTokenResponse = client.getUserToken(getUserTokenRequest);

        // 获取用户访问令牌
        String accessToken = getUserTokenResponse.getBody().getAccessToken();
        log.info("获取到钉钉用户访问令牌：{}", accessToken);

        return accessToken;
    }

    public Boolean isYanfaEmployees(String unionId) throws ApiException {
        List<Integer> deptIds = Lists.newArrayList(598614087,127624492);
        String userId = getUserId(unionId);
        String url = "https://oapi.dingtalk.com/topapi/v2/user/get?access_token=" + getToken();

        List<Integer> deptIdList = getDeptIdLists(userId);
        System.out.println(JSONUtil.toJsonStr(deptIdList));
        HttpRequest request = HttpUtil.createPost(url);
        HashMap<String, Object> param = new HashMap<>();
        param.put("userid", userId);
        request.body(JSONUtil.toJsonStr(param));
        String body = request.execute().body();
        System.out.println(body);
        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(body);
        //598614087 流程 | 127624492 研发
        if (jsonObject.getStr("errmsg").equals("ok")) {
            System.out.println(body);
            if (deptIdList.stream().anyMatch(deptIds::contains)) {
                return true;
            }
        }
        return false;
    }

    public List<Integer> getDeptIdList(Integer deptId){
        String url="https://oapi.dingtalk.com/topapi/v2/department/listsubid?access_token=" + getToken();
        HttpRequest request = HttpUtil.createPost(url);
        HashMap<String, Object> param = new HashMap<>();
        param.put("dept_id", deptId);
        request.body(JSONUtil.toJsonStr(param));
        String body = request.execute().body();
        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(body);
        return jsonObject.getJSONObject("result").getJSONArray("dept_id_list").toList(Integer.class);
    }
    public List<Integer> getDeptIdLists(String userId){
        String url="https://oapi.dingtalk.com/topapi/v2/department/listparentbyuser?access_token=" + getToken();
        HttpRequest request = HttpUtil.createPost(url);
        HashMap<String, Object> param = new HashMap<>();
        param.put("userid", userId);
        request.body(JSONUtil.toJsonStr(param));
        String body = request.execute().body();
        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(body);
        System.out.println(body);
        return jsonObject.getJSONObject("result")
                .getJSONArray("parent_list")
                .getJSONObject(0)
                .getJSONArray("parent_dept_id_list")
                .toList(Integer.class);
    }

    public String getUserId(String unionid) {
        String url = "https://oapi.dingtalk.com/topapi/user/getbyunionid?access_token=" + getToken();
        HttpRequest request = HttpUtil.createPost(url);
        HashMap<String, Object> param = new HashMap<>();
        param.put("unionid", unionid);
        request.body(JSONUtil.toJsonStr(param));
        String body = request.execute().body();
//        {"errcode":0,"errmsg":"ok","result":{"contact_type":0,"userid":"3124"},"request_id":"16lznosduek9z"}
        System.out.println(body);
        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(body);
        if (jsonObject.get("errmsg").equals("ok")) {
            return jsonObject.getJSONObject("result").getStr("userid");
        }
        return "";
    }

    /***
     * 获取请求Token
     * @return
     */
    private String getToken() {
        String reqUrl = BASIC_URL + "/gettoken";
        HttpRequest request = HttpUtil.createGet(reqUrl);
        request.form("appkey", appKey);
        request.form("appsecret", appSecret);
        request.setConnectionTimeout(30 * 1000);

        try {
            String body = request.execute().body();
            JSONObject obj = JSONUtil.parseObj(body);
            return obj.getStr("access_token");
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 获取用户信息
     * 使用访问令牌获取用户个人信息
     *
     * @param accessToken 用户访问令牌
     * @return 系统token
     * @throws Exception 异常
     */
    public String getUserInfo(String accessToken) throws Exception {
        com.aliyun.dingtalkcontact_1_0.Client client = contactClient();

        GetUserHeaders getUserHeaders = new GetUserHeaders();
        getUserHeaders.xAcsDingtalkAccessToken = accessToken;

        // 获取用户个人信息，unionId参数传"me"表示获取当前授权用户的信息
        GetUserResponseBody userInfo = client.getUserWithOptions("me", getUserHeaders, new RuntimeOptions()).getBody();

        log.info("获取到钉钉用户信息：{}", JSONUtil.toJsonStr(userInfo));
//        if (!isYanfaEmployees(userInfo.getUnionId())) {
//            throw new RuntimeException("暂无权限查看平台");
//        }
        // 调用登录服务，将钉钉用户信息转换为系统内部token
        return loginService.loginByDingDing(userInfo);
    }
}