package com.kumhosunny.user.config;

import cn.hutool.core.thread.ThreadUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.kumhosunny.common.result.Result;
import com.kumhosunny.user.service.OperateLogService;
import com.kumhosunny.user.util.JwtUtil;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

/**
 * Token验证拦截器
 */
@Slf4j
@Component
public class TokenInterceptor implements HandlerInterceptor {

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private OperateLogService operateLogService;
    
    // 用户信息在request中的属性名
    private static final String USER_ID_KEY = "userId";
    private static final String USERNAME_KEY = "username";

    List<String> exportTheWhitelistUrl = Lists.newArrayList("/");

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        //预检请求跳出
        if(request.getMethod().equals("OPTIONS")){
            return true;
        }
        for (String url : exportTheWhitelistUrl) {
            if (requestURI.contains(url)) {
                return true;
            }
        }

        // 获取请求头中的token
        String token = request.getHeader("Authorization");
        
        // 检查token是否存在
        if (token == null || token.isEmpty()) {
            setResponse(response, Result.error(401, "未授权：缺少用户token"));
            return false;
        }

        // 验证token是否有效
        try {
            if (jwtUtil.isTokenExpired(token)) {
                setResponse(response, Result.error(401, "未授权：token已过期"));
                return false;
            }
            
            // 解析token获取用户信息
            Claims claims = jwtUtil.parseToken(token);
            
            // 从claims中获取userId和username
            Integer userId = claims.get(USER_ID_KEY, Integer.class);
            String username = claims.get(USERNAME_KEY, String.class);
            
            if (userId == null) {
                setResponse(response, Result.error(401, "未授权：token中缺少用户ID信息"));
                return false;
            }
            log.info("[username] :{},访问了：{}",username,requestURI);
            // 将用户信息存储到request属性中，便于后续使用
            request.setAttribute(USER_ID_KEY, userId);
            request.setAttribute(USERNAME_KEY, username);

            // 如果需要，可以在这里添加日志记录或特定路径的处理
//            requestURI.contains("/export-by-ids") ||
            ThreadUtil.execAsync(()->{
                operateLogService.saveOperateLog(request,userId,username);
            });

            // 验证通过，继续执行
            return true;
        } catch (Exception e) {
            setResponse(response, Result.error(401, "未授权：无效的token"));
            return false;
        }
    }
    
    /**
     * 设置响应信息
     */
    private void setResponse(HttpServletResponse response, Result<?> result) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        try (PrintWriter writer = response.getWriter()) {
            writer.write(objectMapper.writeValueAsString(result));
        }
    }
} 