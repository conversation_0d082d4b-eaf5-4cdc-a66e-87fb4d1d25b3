package com.kumhosunny.user.service.impl;

import com.kumhosunny.common.entity.OperateLogEntity;
import com.kumhosunny.common.repository.OperateLogRepository;
import com.kumhosunny.user.service.OperateLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;

/**
 * @program: kum-ai-app-backend
 * @description:
 * @author: wye
 * @create: 2025-06-18 16:15
 **/
@Service
public class OperateLogServiceImpl implements OperateLogService {

    @Autowired
    private OperateLogRepository operateLogRepository;


    @Override
    public void saveOperateLog(HttpServletRequest request,Integer userId,String username) {
        OperateLogEntity operateLogEntity = new OperateLogEntity();
        operateLogEntity.setIp(request.getRemoteHost());
        operateLogEntity.setMethod(request.getMethod());
        operateLogEntity.setOperateUserId(userId.longValue());
        operateLogEntity.setOperateUserName(username);
        operateLogEntity.setUrl(request.getRequestURI());
        operateLogEntity.setCreateTime(LocalDateTime.now());
        operateLogEntity.setUpdateTime(LocalDateTime.now());
        operateLogRepository.save(operateLogEntity);
    }
}
