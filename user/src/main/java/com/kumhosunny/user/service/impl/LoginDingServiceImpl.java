package com.kumhosunny.user.service.impl;

import com.aliyun.dingtalkcontact_1_0.models.GetUserResponseBody;
import com.kumhosunny.common.entity.EmployeeEntity;
import com.kumhosunny.common.repository.EmployeeRepository;
import com.kumhosunny.user.service.LoginDingService;
import com.kumhosunny.user.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class LoginDingServiceImpl implements LoginDingService {

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 通过钉钉用户信息登录
     * 如果用户不存在则自动创建用户
     *
     * @param userInfo 钉钉用户信息
     * @return 系统token
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String loginByDingDing(GetUserResponseBody userInfo) {
        // 获取钉钉唯一标识
        String unionId = userInfo.getUnionId();

        // 根据unionId查询用户
        Optional<EmployeeEntity> userOptional = employeeRepository.findByUnionId(unionId);
        EmployeeEntity user;
        
        if (userOptional.isEmpty()) {
            // 用户不存在，创建新用户
            user = createUserFromDingInfo(userInfo);
            user = employeeRepository.save(user);
            log.info("创建新用户成功：{}", user.getActualName());
        } else {
            user = userOptional.get();
        }

        // 生成token
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", user.getEmployeeId());
        claims.put("username", user.getActualName());

        return jwtUtil.createToken(claims);
    }

    /**
     * 从钉钉用户信息创建系统用户
     *
     * @param userInfo 钉钉用户信息
     * @return 系统用户
     */
    private EmployeeEntity createUserFromDingInfo(GetUserResponseBody userInfo) {
        EmployeeEntity user = new EmployeeEntity();
        user.setUnionId(userInfo.getUnionId());
        user.setActualName(userInfo.getNick());
        user.setLoginName(userInfo.getMobile());
        user.setEmail(userInfo.getEmail());
        user.setPhone(userInfo.getMobile());
        user.setAvatar(userInfo.getAvatarUrl());
        user.setEmail(userInfo.getEmail()); // 默认启用状态
        user.setAvatar(userInfo.getAvatarUrl());
        return user;
    }



}