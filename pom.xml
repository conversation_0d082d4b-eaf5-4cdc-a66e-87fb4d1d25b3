<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.kumhosunny</groupId>
    <artifactId>kumhosunny-ai-app</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>kumhosunny-ai-app</name>
    <description>KumhoSunny AI应用 - 多模块父项目</description>

    <!-- 子模块 -->
    <modules>
        <module>common</module>
        <module>user</module>
        <module>chat</module>
        <module>knowledge</module>
        <module>tools</module>
        <module>app</module>
    </modules>

    <properties>
        <java.version>11</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.7.8</spring-boot.version>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>

        <!-- 依赖版本管理 -->
        <mysql.version>8.0.33</mysql.version>
        <redis.version>3.0.2</redis.version>
        <jwt.version>0.11.5</jwt.version>
        <fastjson.version>2.0.25</fastjson.version>
        <hutool.version>5.8.18</hutool.version>
        <knife4j.version>4.1.0</knife4j.version>
        <lombok.version>1.18.26</lombok.version>
        <semantic-kernel.version>1.4.4-RC1</semantic-kernel.version>
        <qdrant.version>1.14.0</qdrant.version>
        <grpc.version>1.60.0</grpc.version>
        <protobuf.version>3.25.1</protobuf.version>
        <oss.version>1.0.5</oss.version>
    </properties>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                    <configuration>
                        <source>11</source>
                        <target>11</target>
                        <release>11</release>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <!-- 依赖管理 -->
    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 内部模块依赖 -->
            <dependency>
                <groupId>com.kumhosunny</groupId>
                <artifactId>common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kumhosunny</groupId>
                <artifactId>user</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kumhosunny</groupId>
                <artifactId>chat</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kumhosunny</groupId>
                <artifactId>knowledge</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kumhosunny</groupId>
                <artifactId>tools</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 第三方依赖 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${redis.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!-- Qdrant 向量数据库客户端 -->
            <dependency>
                <groupId>io.qdrant</groupId>
                <artifactId>client</artifactId>
                <version>${qdrant.version}</version>
            </dependency>

            <!-- gRPC 依赖管理 -->
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-netty-shaded</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-protobuf</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-stub</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version>
            </dependency>

            <!-- Semantic Kernel 依赖 -->
            <dependency>
                <groupId>com.microsoft.semantic-kernel</groupId>
                <artifactId>semantickernel-bom</artifactId>
                <version>${semantic-kernel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.microsoft.semantic-kernel</groupId>
                <artifactId>semantickernel-api</artifactId>
                <version>${semantic-kernel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.semantic-kernel</groupId>
                <artifactId>semantickernel-aiservices-openai</artifactId>
                <version>${semantic-kernel.version}</version>
            </dependency>
            <!-- Azure OpenAI 客户端依赖 -->
            <dependency>
                <groupId>com.azure</groupId>
                <artifactId>azure-ai-openai</artifactId>
                <version>1.0.0-beta.12</version>
            </dependency>
            <dependency>
                <groupId>com.azure</groupId>
                <artifactId>azure-core</artifactId>
                <version>1.51.0</version>
            </dependency>
            <dependency>
                <groupId>com.azure</groupId>
                <artifactId>azure-core-http-netty</artifactId>
                <version>1.15.4</version>
            </dependency>
            <dependency>
                <groupId>com.pig4cloud.plugin</groupId>
                <artifactId>oss-spring-boot-starter</artifactId>
                <version>${oss.version}</version>
            </dependency>
            <!--
            https://mvnrepository.com/artifact/org.elasticsearch.client/elasticsearch-rest-high-level-client -->
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>7.17.28</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
</project>