package com.kumhosunny.knowledge.plugins;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.kumhosunny.common.dto.ContextualSearchResult;
import com.kumhosunny.common.util.LiteratureQdrantUtils;
import com.microsoft.semantickernel.semanticfunctions.annotations.DefineKernelFunction;
import com.microsoft.semantickernel.semanticfunctions.annotations.KernelFunctionParameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: kum-ai-app-backend
 * @description: 法律相关知识库
 * @author: wye
 * @create: 2025-07-15 17:21
 **/
@Slf4j
@Component
public class LegalKnowledgePlugin {

    @Autowired
    private LiteratureQdrantUtils literatureQdrantService;

    @Value("${app.literature.default-collection}")
    private String collectionName;


    @DefineKernelFunction(name = "searchLegalKnowledge", description = "搜索知识库中的相关信息")
    public String searchLegalKnowledge(
            @KernelFunctionParameter(name = "query", description = "搜索查询关键词") String query, Long userId, String type) {

        try {
            // 定义混合搜索的向量权重
            double vectorWeight = 0.5; // 权重从0.7调整为0.5，以平衡语义和关键字搜索
            int topK = 10;
            int contextSize = 5; // 获取上下文数量

            // 使用新的上下文搜索功能
            List<ContextualSearchResult> contextualResults = literatureQdrantService.hybridSearchWithContext(
                    query, collectionName, vectorWeight, topK, userId, type, contextSize);

            if (CollUtil.isEmpty(contextualResults)) {
                return "知识库未搜索到结果";
            }

            // 使用JSON结构化输出
            JSONArray resultArray = new JSONArray();
            for (ContextualSearchResult result : contextualResults) {
                JSONObject resultObj = new JSONObject();
                resultObj.set("source", result.getSource()); // 文件名作为来源
                resultObj.set("content", result.getContent()); // 文档块内容
                resultArray.add(resultObj);
            }
            return resultArray.toString();
        } catch (Exception e) {
            log.error("在知识库中搜索时出错", e);
            return "在知识库中搜索时出错: " + e.getMessage();
        }
    }

}
