package com.kumhosunny.knowledge.plugins;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.kumhosunny.common.dto.ContextualSearchResult;
import com.kumhosunny.common.util.LiteratureQdrantUtils;
import io.qdrant.client.grpc.Points;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.microsoft.semantickernel.semanticfunctions.annotations.DefineKernelFunction;
import com.microsoft.semantickernel.semanticfunctions.annotations.KernelFunctionParameter;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识库插件
 * 提供知识搜索、推荐和统计功能
 */
@Slf4j
@Component
public class KnowledgePlugin {

    @Autowired
    private LiteratureQdrantUtils literatureQdrantService;

    @Value("${app.literature.default-collection}")
    private String collectionName;

    /**
     * 搜索知识库
     * 
     * @param query 搜索查询
     * @return 搜索结果
     */
    @DefineKernelFunction(name = "searchKnowledge", description = "搜索知识库中的相关信息")
    public String searchKnowledge(
            @KernelFunctionParameter(name = "query", description = "搜索查询关键词") String query, Long userId, String type) {

        try {
            // 定义混合搜索的向量权重
            double vectorWeight = 0.5; // 权重从0.7调整为0.5，以平衡语义和关键字搜索
            int topK = 10;
            int contextSize = 5; // 获取上下文数量

            // 使用新的上下文搜索功能
            List<ContextualSearchResult> contextualResults = literatureQdrantService.hybridSearchWithContext(
                    query, collectionName, vectorWeight, topK, userId, type, contextSize);

            if (CollUtil.isEmpty(contextualResults)) {
                return "知识库未搜索到结果";
            }

            // 使用JSON结构化输出
            JSONArray resultArray = new JSONArray();
            for (ContextualSearchResult result : contextualResults) {
                JSONObject resultObj = new JSONObject();
                resultObj.set("source", result.getSource()); // 文件名作为来源
                resultObj.set("content", result.getContent()); // 文档块内容
                resultArray.add(resultObj);
            }
            return resultArray.toString();
        } catch (Exception e) {
            log.error("在知识库中搜索时出错", e);
            return "在知识库中搜索时出错: " + e.getMessage();
        }
    }

    /**
     * 根据文件ID搜索特定文档
     * 
     * @param query  搜索查询
     * @param fileId 文件ID
     * @return 搜索结果
     */
    @DefineKernelFunction(name = "searchKnowledgeByFileId", description = "在特定文件中搜索相关信息")
    public String searchKnowledgeByFileId(
            @KernelFunctionParameter(name = "query", description = "搜索查询关键词") String query,
            @KernelFunctionParameter(name = "fileId", description = "要搜索的文件ID") Long fileId) {

        try {
            int topK = 10;

            // 使用文件特定的搜索功能
            List<Points.ScoredPoint> searchResults = literatureQdrantService.searchLiteratureByFile(
                    query, collectionName, topK, fileId);

            if (CollUtil.isEmpty(searchResults)) {
                return "在指定文件中未搜索到相关结果";
            }

            // 使用JSON结构化输出
            JSONArray resultArray = new JSONArray();
            for (Points.ScoredPoint point : searchResults) {
                JSONObject resultObj = new JSONObject();
                // 从payload中获取信息
                var payload = point.getPayloadMap();
                String title = payload.get("title") != null ? payload.get("title").getStringValue() : "未知文档";
                String content = payload.get("text") != null ? payload.get("text").getStringValue() : "";

                resultObj.set("source", title); // 文件名作为来源
                resultObj.set("content", content); // 文档块内容
                resultObj.set("score", point.getScore()); // 相似度分数
                resultArray.add(resultObj);
            }
            return resultArray.toString();
        } catch (Exception e) {
            log.error("在指定文件中搜索时出错", e);
            return "在指定文件中搜索时出错: " + e.getMessage();
        }
    }

    /**
     * 获取文件的完整内容信息（用于第一次对话）
     * 
     * @param fileId 文件ID
     * @return 文件的完整内容摘要
     */
    @DefineKernelFunction(name = "getFileFullContent", description = "获取文件的完整内容信息")
    public String getFileFullContent(
            @KernelFunctionParameter(name = "fileId", description = "文件ID") Long fileId) {

        try {
            // 获取文件的所有内容块（不进行搜索过滤，获取完整内容）
            List<Points.ScoredPoint> allChunks = literatureQdrantService.searchLiteratureByFile(
                    "*", collectionName, 100, fileId); // 使用通配符获取所有内容，限制100个chunk

            if (CollUtil.isEmpty(allChunks)) {
                return "该文件暂无内容信息";
            }

            // 构建文件内容摘要
            JSONObject fileInfo = new JSONObject();
            String fileName = "未知文件";
            StringBuilder contentPreview = new StringBuilder();
            
                         // 按chunk_index排序，确保内容顺序正确
             List<Points.ScoredPoint> sortedChunks = allChunks.stream()
                     .sorted((p1, p2) -> {
                         var payload1 = p1.getPayloadMap();
                         var payload2 = p2.getPayloadMap();
                         int index1 = payload1.get("chunk_index") != null ? 
                             (int)payload1.get("chunk_index").getIntegerValue() : 0;
                         int index2 = payload2.get("chunk_index") != null ? 
                             (int)payload2.get("chunk_index").getIntegerValue() : 0;
                         return Integer.compare(index1, index2);
                     })
                     .collect(Collectors.toList());

            // 提取前几个chunk作为内容预览，获取文件名
            for (int i = 0; i < sortedChunks.size(); i++) {
                Points.ScoredPoint chunk = sortedChunks.get(i);
                var payload = chunk.getPayloadMap();
                
                if (i == 0) {
                    fileName = payload.get("title") != null ? payload.get("title").getStringValue() : "未知文件";
                }
                
                String chunkContent = payload.get("text") != null ? payload.get("text").getStringValue() : "";
                if (!chunkContent.trim().isEmpty()) {
                    contentPreview.append(chunkContent.trim()).append("\n\n");
                }
            }

            fileInfo.set("fileName", fileName);
            fileInfo.set("totalChunks", sortedChunks.size());
            fileInfo.set("contentPreview", contentPreview.toString().trim());
            fileInfo.set("previewNote", String.format("显示前%d个内容片段",
                    sortedChunks.size()));

            return fileInfo.toString();
        } catch (Exception e) {
            log.error("获取文件完整内容时出错", e);
            return "获取文件内容时出错: " + e.getMessage();
        }
    }

    /**
     * 获取知识推荐
     * 
     * @param topic 主题
     * @return 推荐内容
     */
    @DefineKernelFunction(name = "getRecommendations", description = "根据主题获取知识推荐")
    public String getRecommendations(
            @KernelFunctionParameter(name = "topic", description = "感兴趣的主题") String topic) {

        // 模拟知识推荐
        return String.format("基于主题 '%s' 的知识推荐：\n" +
                "1. 推荐阅读：《%s 实战指南》\n" +
                "2. 相关课程：%s 从入门到精通\n" +
                "3. 实践项目：%s 应用开发案例\n" +
                "4. 社区讨论：%s 技术交流群\n" +
                "推荐时间：%s",
                topic, topic, topic, topic, topic, java.time.LocalDateTime.now());
    }

    /**
     * 获取知识统计
     * 
     * @param category 分类
     * @return 统计信息
     */
    @DefineKernelFunction(name = "getKnowledgeStats", description = "获取知识库统计信息")
    public String getKnowledgeStats(
            @KernelFunctionParameter(name = "category", description = "知识分类") String category) {

        // 模拟统计信息
        return String.format("知识库统计信息 - 分类：%s\n" +
                "总文档数：1,234\n" +
                "本月新增：56\n" +
                "热门标签：%s, AI, 机器学习, 深度学习\n" +
                "平均阅读量：89次/文档\n" +
                "用户评分：4.8/5.0\n" +
                "统计时间：%s",
                category, category, java.time.LocalDateTime.now());
    }
}