package com.kumhosunny.knowledge.service;

import com.kumhosunny.common.entity.AiFiles;
import com.kumhosunny.common.enums.FileProcessStatus;

/**
 * 异步文件处理服务接口
 * 负责文件的异步解析和向量化处理
 * 
 * <AUTHOR>
 */
public interface AsyncFileProcessService {
    
    /**
     * 异步处理文件解析和向量化
     * 
     * @param fileId 文件ID
     * @param type 文档类型
     */
    void processFileAsync(Long fileId, String type);
    
    /**
     * 更新文件处理状态
     * 
     * @param fileId 文件ID
     * @param status 新状态
     */
    void updateFileStatus(Long fileId, FileProcessStatus status);
    
    /**
     * 更新文件处理状态并记录错误信息
     * 
     * @param fileId 文件ID
     * @param status 新状态
     * @param errorMessage 错误信息
     */
    void updateFileStatus(Long fileId, FileProcessStatus status, String errorMessage);
    
    /**
     * 获取文件处理状态
     * 
     * @param fileId 文件ID
     * @return 文件处理状态
     */
    FileProcessStatus getFileStatus(Long fileId);
}
