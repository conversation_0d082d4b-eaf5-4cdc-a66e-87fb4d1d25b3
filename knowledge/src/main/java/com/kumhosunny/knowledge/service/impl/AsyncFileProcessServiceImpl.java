package com.kumhosunny.knowledge.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.amazonaws.services.s3.model.S3Object;
import com.kumhosunny.common.entity.AiFiles;
import com.kumhosunny.common.entity.Document;
import com.kumhosunny.common.enums.FileProcessStatus;
import com.kumhosunny.common.enums.VectorizableFileType;
import com.kumhosunny.common.repository.AiFilesRepository;
import com.kumhosunny.common.util.HashUtils;
import com.kumhosunny.common.util.LiteratureQdrantUtils;
import com.kumhosunny.knowledge.service.AsyncFileProcessService;
import com.pig4cloud.plugin.oss.service.OssTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 异步文件处理服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class AsyncFileProcessServiceImpl implements AsyncFileProcessService {

    @Autowired
    private AiFilesRepository aiFilesRepository;

    @Autowired
    private LiteratureQdrantUtils literatureQdrantService;

    @Autowired
    private OssTemplate ossTemplate;

    @Value("${app.document.parse-url:http://192.168.170.66:8100/general/v0/general}")
    private String documentParseUrl;

    @Value("${app.qdrant.collection-name:literature_collection_bge}")
    private String collectionName;

    @Override
    @Async("taskExecutor")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void processFileAsync(Long fileId, String type) {
        log.info("开始异步处理文件: fileId={}, type={}", fileId, type);

        try {
            Optional<AiFiles> fileOptional = aiFilesRepository.findById(fileId);
            if (!fileOptional.isPresent()) {
                log.error("文件不存在: fileId={}", fileId);
                return;
            }

            AiFiles aiFiles = fileOptional.get();

            // 检查文件类型是否支持向量化
            if (ObjectUtil.isEmpty(VectorizableFileType.fromExtension(aiFiles.getFileType()))) {
                log.info("文件类型不支持向量化: fileType={}", aiFiles.getFileType());
                updateFileStatus(fileId, FileProcessStatus.COMPLETED);
                return;
            }

            // 检查是否需要解析文档
            if (StrUtil.isBlank(type)) {
                log.info("未指定文档类型，跳过解析: fileId={}", fileId);
                updateFileStatus(fileId, FileProcessStatus.COMPLETED);
                return;
            }

            // 步骤1: 解析文件
            parseFile(aiFiles, type);

        } catch (Exception e) {
            log.error("异步处理文件失败: fileId={}", fileId, e);
            updateFileStatus(fileId, FileProcessStatus.FAILED, "处理失败: " + e.getMessage());
        }
    }

    /**
     * 解析文件内容
     */
    private void parseFile(AiFiles aiFiles, String type) {
        log.info("开始解析文件: fileId={}, fileName={}", aiFiles.getId(), aiFiles.getFileName());

        try {
            // 更新状态为解析中
            updateFileStatus(aiFiles.getId(), FileProcessStatus.PARSING);

            // 创建临时文件用于解析
            File tempFile = createTempFile(aiFiles);
            if (tempFile == null) {
                throw new RuntimeException("无法创建临时文件");
            }

            try {
                // 调用Unstructured文档解析服务
                HttpRequest httpRequest = HttpUtil.createPost(documentParseUrl);
                httpRequest.charset("UTF-8");
                httpRequest.form("files", tempFile);
                // 添加Unstructured特定参数
                httpRequest.form("strategy", "hi_res"); // 或 "hi_res" 用于更高质量
                httpRequest.form("output_format", "application/json");

                String rawResponse = httpRequest.execute().body();

                if (StrUtil.isBlank(rawResponse)) {
                    throw new RuntimeException("文档解析结果为空");
                }

                log.debug("Unstructured原始响应长度: {}", rawResponse.length());

                // 解析Unstructured返回的JSON结构
                String processedContent = processUnstructuredResponse(rawResponse);

                // 保存处理后的内容
                aiFiles.setTextContent(processedContent);
                aiFilesRepository.save(aiFiles);

                // 更新状态为已解析
                updateFileStatus(aiFiles.getId(), FileProcessStatus.PARSED);

                log.info("文件解析完成: fileId={}, 原始长度={}, 处理后长度={}",
                        aiFiles.getId(), rawResponse.length(), processedContent.length());

                // 步骤2: 向量化处理 - 直接传递原始JSON用于向量化
                vectorizeFileFromUnstructured(aiFiles, type, rawResponse);

            } finally {
                // 清理临时文件
                if (tempFile.exists()) {
                    tempFile.delete();
                }
            }

        } catch (Exception e) {
            log.error("文件解析失败: fileId={}", aiFiles.getId(), e);
            updateFileStatus(aiFiles.getId(), FileProcessStatus.FAILED, "解析失败: " + e.getMessage());
        }
    }

    /**
     * 处理Unstructured返回的JSON响应
     * 提取有价值的文本内容用于存储
     */
    private String processUnstructuredResponse(String rawResponse) {
        try {
            JSONArray elements = JSONUtil.parseArray(rawResponse);
            StringBuilder cleanText = new StringBuilder();

            for (Object element : elements) {
                if (element instanceof JSONObject) {
                    JSONObject elementObj = (JSONObject) element;
                    String type = elementObj.getStr("type");
                    String text = elementObj.getStr("text");

                    // 只提取有意义的文本内容，过滤掉无用的符号
                    if (StrUtil.isNotBlank(text) && isValidTextContent(type, text)) {
                        // 根据元素类型添加适当的格式
                        if ("Title".equals(type)) {
                            cleanText.append("\n\n## ").append(text.trim()).append("\n\n");
                        } else if ("Header".equals(type)) {
                            cleanText.append("\n### ").append(text.trim()).append("\n");
                        } else if ("NarrativeText".equals(type) || "Text".equals(type)) {
                            cleanText.append(text.trim()).append("\n");
                        } else if ("ListItem".equals(type)) {
                            cleanText.append("• ").append(text.trim()).append("\n");
                        } else if ("Table".equals(type)) {
                            cleanText.append("\n[表格内容]\n").append(text.trim()).append("\n");
                        } else {
                            // 其他类型的文本也保留
                            cleanText.append(text.trim()).append(" ");
                        }
                    }
                }
            }

            return cleanText.toString().trim();

        } catch (Exception e) {
            log.error("处理Unstructured响应失败", e);
            // 如果解析失败，返回原始内容
            return rawResponse;
        }
    }

    /**
     * 判断是否为有效的文本内容
     */
    private boolean isValidTextContent(String type, String text) {
        if (StrUtil.isBlank(text)) {
            return false;
        }

        // 过滤掉无意义的内容
        String trimmedText = text.trim();

        // 过滤单个字符或符号
        if (trimmedText.length() <= 2) {
            return false;
        }

        // 过滤只包含符号的内容
        if (trimmedText.matches("^[|\\-_=+\\s]+$")) {
            return false;
        }

        // 过滤UncategorizedText中的无意义内容
        if ("UncategorizedText".equals(type) && trimmedText.matches("^[|\\s]+$")) {
            return false;
        }

        return true;
    }

    /**
     * 基于Unstructured数据进行向量化处理
     */
    private void vectorizeFileFromUnstructured(AiFiles aiFiles, String type, String unstructuredJson) {
        log.info("开始向量化文件: fileId={}, type={}", aiFiles.getId(), type);

        try {
            // 更新状态为向量化中
            updateFileStatus(aiFiles.getId(), FileProcessStatus.VECTORIZING);

            // 内容去重检查
            if (shouldCheckDuplicates(type)) {
                String contentHash = HashUtils.normalizedContentHash(unstructuredJson);
                log.debug("内容哈希: {}", contentHash);
            }

            // 处理Unstructured数据为向量化格式
            String processedContent = processUnstructuredForVectorization(unstructuredJson);
            log.debug("Unstructured数据处理完成: fileId={}, 原始长度={}, 处理后长度={}",
                    aiFiles.getId(), unstructuredJson.length(), processedContent.length());

            // 执行向量化
            Document.DocumentType documentType = Document.DocumentType.valueOf(type);
            int insertedChunks = literatureQdrantService.insertLiterature(processedContent, collectionName, aiFiles,
                    documentType);

            log.info("文档向量化完成: fileId={}, 插入段落数: {}", aiFiles.getId(), insertedChunks);

            // 清理文本内容（节省存储空间）
            aiFiles.setTextContent("");
            aiFilesRepository.save(aiFiles);

            // 更新状态为完成
            updateFileStatus(aiFiles.getId(), FileProcessStatus.COMPLETED);

        } catch (Exception e) {
            log.error("文件向量化失败: fileId={}", aiFiles.getId(), e);
            updateFileStatus(aiFiles.getId(), FileProcessStatus.FAILED, "向量化失败: " + e.getMessage());
        }
    }

    /**
     * 处理Unstructured数据为向量化格式
     * 直接使用Unstructured的元素，只做基本过滤，不进行分段
     */
    private String processUnstructuredForVectorization(String unstructuredJson) {
        try {
            JSONArray elements = JSONUtil.parseArray(unstructuredJson);
            JSONArray validElements = new JSONArray();

            for (Object element : elements) {
                if (element instanceof JSONObject) {
                    JSONObject elementObj = (JSONObject) element;
                    String text = elementObj.getStr("text");
                    String type = elementObj.getStr("type");

                    // 只做基本的有效性检查，不修改原始结构
                    if (isValidElementForVectorization(type, text)) {
                        // 直接使用原始元素，只添加时间戳
                        JSONObject vectorElement = new JSONObject();
                        vectorElement.set("text", text.trim());
                        vectorElement.set("type", type);

                        // 保留原始metadata，并添加时间戳
                        JSONObject metadata = elementObj.getJSONObject("metadata");
                        if (metadata == null) {
                            metadata = new JSONObject();
                        }
                        metadata.set("timestamp", System.currentTimeMillis() / 1000);
                        vectorElement.set("metadata", metadata);

                        validElements.add(vectorElement);
                    }
                }
            }

            log.info("Unstructured元素过滤完成: 原始元素数={}, 有效元素数={}",
                    elements.size(), validElements.size());

            return validElements.toString();

        } catch (Exception e) {
            log.error("处理Unstructured数据失败", e);
            throw new RuntimeException("处理Unstructured数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 判断元素是否适合向量化
     * 最小化过滤，只排除明显无意义的内容
     */
    private boolean isValidElementForVectorization(String type, String text) {
        if (StrUtil.isBlank(text)) {
            return false;
        }

        String trimmedText = text.trim();

        // 只过滤极短的内容（可能是符号或空白）
        if (trimmedText.length() < 3) {
            return false;
        }

        // 只过滤纯符号内容
        if (trimmedText.matches("^[|\\-_=+\\s]+$")) {
            return false;
        }

        // 其他所有内容都保留，让Unstructured的分类决定
        return true;
    }

    /**
     * 将页面元素分组为合适大小的chunks
     */
    private List<String> groupElementsIntoChunks(List<JSONObject> elements, int maxChunkSize) {
        List<String> chunks = new ArrayList<>();
        StringBuilder currentChunk = new StringBuilder();
        String currentSection = "";

        for (JSONObject element : elements) {
            String text = element.getStr("text");
            String type = element.getStr("type");

            if (StrUtil.isBlank(text))
                continue;

            // 处理标题和章节
            if ("Title".equals(type) || "Header".equals(type)) {
                // 如果当前chunk有内容，先保存
                if (currentChunk.length() > 0) {
                    chunks.add(currentChunk.toString().trim());
                    currentChunk = new StringBuilder();
                }
                currentSection = text.trim();
                currentChunk.append("## ").append(currentSection).append("\n\n");
            } else {
                // 检查添加当前文本是否会超过大小限制
                if (currentChunk.length() + text.length() > maxChunkSize && currentChunk.length() > 0) {
                    chunks.add(currentChunk.toString().trim());
                    currentChunk = new StringBuilder();

                    // 在新chunk中保留章节信息
                    if (StrUtil.isNotBlank(currentSection)) {
                        currentChunk.append("## ").append(currentSection).append("\n\n");
                    }
                }

                // 添加文本内容
                if ("ListItem".equals(type)) {
                    currentChunk.append("• ").append(text.trim()).append("\n");
                } else if ("Table".equals(type)) {
                    currentChunk.append("\n[表格]\n").append(text.trim()).append("\n\n");
                } else {
                    currentChunk.append(text.trim()).append("\n");
                }
            }
        }

        // 保存最后一个chunk
        if (currentChunk.length() > 0) {
            chunks.add(currentChunk.toString().trim());
        }

        return chunks;
    }

    /**
     * Fallback文本分块策略（当Unstructured解析失败时使用）
     */
    private String fallbackTextChunking(String content) {
        // 尝试提取纯文本
        String cleanText = extractTextFromUnstructuredJson(content);

        // 使用智能分割策略
        List<String> textChunks = smartSplitText(cleanText, 800, 100);

        // 转换为JSON数组格式
        JSONArray jsonArray = new JSONArray();
        for (int i = 0; i < textChunks.size(); i++) {
            JSONObject chunkObj = new JSONObject();
            chunkObj.set("text", textChunks.get(i));
            chunkObj.set("chunk_index", i);
            jsonArray.add(chunkObj);
        }

        return jsonArray.toString();
    }

    /**
     * 从Unstructured JSON中提取纯文本
     */
    private String extractTextFromUnstructuredJson(String unstructuredJson) {
        try {
            JSONArray elements = JSONUtil.parseArray(unstructuredJson);
            StringBuilder text = new StringBuilder();

            for (Object element : elements) {
                if (element instanceof JSONObject) {
                    JSONObject elementObj = (JSONObject) element;
                    String elementText = elementObj.getStr("text");
                    if (StrUtil.isNotBlank(elementText)) {
                        text.append(elementText.trim()).append(" ");
                    }
                }
            }

            return text.toString().trim();
        } catch (Exception e) {
            log.error("提取文本失败", e);
            return unstructuredJson;
        }
    }

    /**
     * 智能文本分割策略
     * 优先按句子分割，保持语义完整性
     *
     * @param content      原始文本
     * @param maxChunkSize 最大chunk大小
     * @param overlapSize  重叠大小（用于保持上下文连贯性）
     * @return 分割后的文本块列表
     */
    private List<String> smartSplitText(String content, int maxChunkSize, int overlapSize) {
        List<String> chunks = new ArrayList<>();
        if (StrUtil.isBlank(content)) {
            return chunks;
        }

        // 1. 首先按段落分割
        String[] paragraphs = content.split("\\n\\s*\\n");

        StringBuilder currentChunk = new StringBuilder();
        String previousChunkEnd = ""; // 用于重叠

        for (String paragraph : paragraphs) {
            paragraph = paragraph.trim();
            if (paragraph.isEmpty())
                continue;

            // 2. 如果单个段落就超过最大大小，需要进一步分割
            if (paragraph.length() > maxChunkSize) {
                // 先保存当前chunk（如果有内容）
                if (currentChunk.length() > 0) {
                    chunks.add(currentChunk.toString().trim());
                    previousChunkEnd = getChunkEnd(currentChunk.toString(), overlapSize);
                    currentChunk = new StringBuilder();
                }

                // 分割长段落
                List<String> subChunks = splitLongParagraph(paragraph, maxChunkSize, overlapSize);
                for (int i = 0; i < subChunks.size(); i++) {
                    String subChunk = subChunks.get(i);
                    if (i == 0 && !previousChunkEnd.isEmpty()) {
                        // 第一个子chunk添加重叠内容
                        chunks.add(previousChunkEnd + " " + subChunk);
                    } else {
                        chunks.add(subChunk);
                    }
                    if (i < subChunks.size() - 1) {
                        previousChunkEnd = getChunkEnd(subChunk, overlapSize);
                    }
                }
                continue;
            }

            // 3. 检查添加当前段落是否会超过大小限制
            if (currentChunk.length() + paragraph.length() + 2 > maxChunkSize && currentChunk.length() > 0) {
                // 保存当前chunk
                chunks.add(currentChunk.toString().trim());
                previousChunkEnd = getChunkEnd(currentChunk.toString(), overlapSize);

                // 开始新chunk，添加重叠内容
                currentChunk = new StringBuilder();
                if (!previousChunkEnd.isEmpty()) {
                    currentChunk.append(previousChunkEnd).append(" ");
                }
            }

            // 4. 添加当前段落
            if (currentChunk.length() > 0) {
                currentChunk.append("\\n\\n");
            }
            currentChunk.append(paragraph);
        }

        // 5. 保存最后一个chunk
        if (currentChunk.length() > 0) {
            chunks.add(currentChunk.toString().trim());
        }

        // 如果没有生成任何chunk，至少返回原始内容
        if (chunks.isEmpty() && !content.trim().isEmpty()) {
            chunks.add(content.trim());
        }

        return chunks;
    }

    /**
     * 分割长段落
     */
    private List<String> splitLongParagraph(String paragraph, int maxChunkSize, int overlapSize) {
        List<String> chunks = new ArrayList<>();

        // 按句子分割
        String[] sentences = paragraph.split("[。！？；;]");

        StringBuilder currentChunk = new StringBuilder();

        for (String sentence : sentences) {
            sentence = sentence.trim();
            if (sentence.isEmpty())
                continue;

            // 如果单个句子就超过最大大小，强制分割
            if (sentence.length() > maxChunkSize) {
                if (currentChunk.length() > 0) {
                    chunks.add(currentChunk.toString().trim());
                    currentChunk = new StringBuilder();
                }

                // 强制按字符分割
                for (int i = 0; i < sentence.length(); i += maxChunkSize - overlapSize) {
                    int end = Math.min(i + maxChunkSize, sentence.length());
                    chunks.add(sentence.substring(i, end));
                }
                continue;
            }

            // 检查添加当前句子是否会超过大小限制
            if (currentChunk.length() + sentence.length() + 1 > maxChunkSize && currentChunk.length() > 0) {
                chunks.add(currentChunk.toString().trim());
                currentChunk = new StringBuilder();
            }

            if (currentChunk.length() > 0) {
                currentChunk.append("。");
            }
            currentChunk.append(sentence);
        }

        if (currentChunk.length() > 0) {
            chunks.add(currentChunk.toString().trim());
        }

        return chunks;
    }

    /**
     * 获取chunk末尾的重叠内容
     */
    private String getChunkEnd(String chunk, int overlapSize) {
        if (chunk.length() <= overlapSize) {
            return chunk;
        }

        String end = chunk.substring(chunk.length() - overlapSize);

        // 尝试从完整的句子开始
        int lastSentenceStart = end.lastIndexOf("。");
        if (lastSentenceStart > 0 && lastSentenceStart < end.length() - 1) {
            return end.substring(lastSentenceStart + 1).trim();
        }

        return end.trim();
    }

    /**
     * 创建临时文件
     */
    private File createTempFile(AiFiles aiFiles) {
        try {
            String tempFileName = aiFiles.getFileName();
            File tempFile = new File(System.getProperty("java.io.tmpdir") + "/" + tempFileName);

            // 从OSS下载文件到临时目录
            try (S3Object s3Object = ossTemplate.getObject(aiFiles.getBucketName(), aiFiles.getFileName())) {
                FileUtil.writeFromStream(s3Object.getObjectContent(), tempFile);
                log.debug("文件下载成功: {}", tempFile.getAbsolutePath());
                return tempFile;
            }

        } catch (Exception e) {
            log.error("创建临时文件失败: fileName={}", aiFiles.getFileName(), e);
            return null;
        }
    }

    /**
     * 判断是否需要检查重复内容
     */
    private boolean shouldCheckDuplicates(String type) {
        return "department".equals(type) || "company".equals(type);
    }

    @Override
    @Transactional
    public void updateFileStatus(Long fileId, FileProcessStatus status) {
        updateFileStatus(fileId, status, null);
    }

    @Override
    @Transactional
    public void updateFileStatus(Long fileId, FileProcessStatus status, String errorMessage) {
        try {
            Optional<AiFiles> fileOptional = aiFilesRepository.findById(fileId);
            if (fileOptional.isPresent()) {
                AiFiles aiFiles = fileOptional.get();
                aiFiles.setAiStatus(status);
                if (StrUtil.isNotBlank(errorMessage)) {
                    aiFiles.setErrorMessage(errorMessage);
                }
                aiFilesRepository.save(aiFiles);
                log.debug("更新文件状态: fileId={}, status={}, error={}", fileId, status, errorMessage);
            }
        } catch (Exception e) {
            log.error("更新文件状态失败: fileId={}, status={}", fileId, status, e);
        }
    }

    @Override
    public FileProcessStatus getFileStatus(Long fileId) {
        return aiFilesRepository.findById(fileId)
                .map(AiFiles::getAiStatus)
                .orElse(null);
    }
}
