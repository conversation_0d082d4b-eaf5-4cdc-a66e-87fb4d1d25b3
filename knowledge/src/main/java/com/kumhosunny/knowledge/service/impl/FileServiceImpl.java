package com.kumhosunny.knowledge.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.amazonaws.services.s3.model.S3Object;
import com.kumhosunny.common.dto.FileDetailDto;
import com.kumhosunny.common.entity.AiFiles;
import com.kumhosunny.common.entity.Department;
import com.kumhosunny.common.entity.Document;
import com.kumhosunny.common.enums.FileProcessStatus;
import com.kumhosunny.common.enums.VectorizableFileType;
import com.kumhosunny.common.repository.AiFilesRepository;
import com.kumhosunny.common.repository.DocumentRepository;
import com.kumhosunny.common.repository.DocumentChunkRepository;
import com.kumhosunny.common.result.Result;
import com.kumhosunny.common.service.DepartmentService;
import com.kumhosunny.common.util.*;
import com.kumhosunny.knowledge.req.AiFilesReq;
import com.kumhosunny.knowledge.service.AsyncFileProcessService;
import com.kumhosunny.knowledge.service.FileService;
import com.pig4cloud.plugin.oss.OssProperties;
import com.pig4cloud.plugin.oss.service.OssTemplate;
import com.kumhosunny.common.dto.VectorRecallResultDto;
import io.qdrant.client.grpc.JsonWithInt;
import io.qdrant.client.grpc.Points;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static io.qdrant.client.ValueFactory.value;

/**
 * @program: kum-ai-app-backend
 * @description:
 * @author: wye
 * @create: 2025-06-18 13:50
 **/
@Slf4j
@Service
public class FileServiceImpl implements FileService {

    @Autowired
    private OssProperties ossProperties;

    @Autowired
    private OssTemplate ossTemplate;

    @Value("${document.parseUrl:http://**************:8100/general/v0/general}")
    private String documentParseUrl;

    @Value("${app.literature.default-collection}")
    private String collectionName;

    @Autowired
    private AiFilesRepository aiFilesRepository;

    @Autowired
    private QdrantUtils qdrantUtils;

    @Autowired
    private ElasticsearchUtil elasticsearchUtil;

    @Autowired
    private LiteratureQdrantUtils literatureQdrantService;

    @Autowired
    private OssUtil ossUtil;

    @Autowired
    private UserContextUtil userContextUtil;

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private DocumentChunkRepository documentChunkRepository;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private AsyncFileProcessService asyncFileProcessService;

    @Override
    public Result recall(Long fileId, String query) {
        try {
            List<Points.ScoredPoint> scoredPoints = literatureQdrantService.searchLiteratureByFile(query,
                    collectionName, 5, fileId);

            List<VectorRecallResultDto> recallResults = scoredPoints.stream()
                    .map(scoredPoint -> {
                        Map<String, JsonWithInt.Value> payload = scoredPoint.getPayloadMap();
                        return VectorRecallResultDto.builder()
                                .score(scoredPoint.getScore())
                                .content(payload.get("text").getStringValue())
                                .chunkIndex((int) payload.get("chunk_index").getIntegerValue())
                                .vectorId(scoredPoint.getId().getUuid())
                                .build();
                    })
                    .collect(Collectors.toList());

            return Result.success(recallResults);
        } catch (IOException | ExecutionException | InterruptedException e) {
            log.error("向量召回异常", e);
            return Result.error("向量召回异常: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result upload(HttpServletRequest request,
            AiFilesReq req) throws Exception {

        List<AiFiles> resultFileList = new ArrayList<>();

        // 异步上传：只处理文件上传到OSS，后续处理异步进行
        for (MultipartFile file : req.getFiles()) {
            try {
                // 1. 上传文件到OSS并保存基本信息
                AiFiles aiFiles = uploadFileToMinio(file, request);
                OssUtil.FileUploadResult fileUploadResult = ossUtil.uploadFile(file);
                aiFiles.setPath(fileUploadResult.getFileUrl());

                // 2. 设置初始状态为已上传
                aiFiles.setAiStatus(FileProcessStatus.UPLOADED);
                aiFilesRepository.save(aiFiles);

                // 3. 记录需要异步处理的文件信息
                boolean needAsyncProcess = ObjectUtil
                        .isNotEmpty(VectorizableFileType.fromExtension(aiFiles.getFileType()))
                        && StrUtil.isNotBlank(req.getType());

                if (!needAsyncProcess) {
                    // 不需要处理的文件直接标记为完成
                    aiFiles.setAiStatus(FileProcessStatus.COMPLETED);
                    aiFilesRepository.save(aiFiles);
                }

                // 4. 保存文件信息以便事务提交后异步处理
                if (needAsyncProcess) {
                    // 在事务提交后异步处理
                    final Long fileId = aiFiles.getId();
                    final String type = req.getType();
                    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            asyncFileProcessService.processFileAsync(fileId, type);
                            log.info("文件已提交异步处理: fileId={}, fileName={}", fileId, aiFiles.getFileName());
                        }
                    });
                }

                // 5. 返回结果时不包含文本内容（节省传输）
                aiFiles.setTextContent("");
                resultFileList.add(aiFiles);

            } catch (Exception e) {
                log.error("文件上传失败: fileName={}", file.getOriginalFilename(), e);
                throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
            }
        }

        return Result.success(resultFileList);
    }

    @Override
    public Result upload(HttpServletRequest request, MultipartFile file) throws Exception {
        // 为可向量化的文件类型自动设置默认type为personal
        String fileExtension = FileUtil.extName(file.getOriginalFilename());
        String defaultType = ObjectUtil.isNotEmpty(VectorizableFileType.fromExtension(fileExtension)) ? "personal"
                : null;

        AiFilesReq req = new AiFilesReq();
        req.setFiles(new MultipartFile[] { file });
        req.setType(defaultType);
        return this.upload(request, req);
    }

    public AiFiles uploadFileToMinio(MultipartFile file, HttpServletRequest request) {
        String fileName = IdUtil.simpleUUID() + StrUtil.DOT + FileUtil.extName(file.getOriginalFilename());
        Map<String, String> resultMap = new HashMap<>(4);
        resultMap.put("bucketName", ossProperties.getBucketName());
        resultMap.put("fileName", fileName);
        Long currentUserId = userContextUtil.getCurrentUserId(request);
        try {
            ossTemplate.putObject(ossProperties.getBucketName(), fileName, file.getContentType(),
                    file.getInputStream());
            // 文件管理数据记录,收集管理追踪文件
            return fileSave(file, fileName, currentUserId);
        } catch (Exception e) {
            log.error("上传失败", e);
            return null;
        }
    }

    /**
     * 读取文件
     *
     * @param bucket
     * @param fileName
     * @param response
     */
    public void getFile(String bucket, String fileName, HttpServletResponse response) {
        try (S3Object s3Object = ossTemplate.getObject(bucket, fileName)) {
            response.setContentType("application/octet-stream; charset=UTF-8");
            IoUtil.copy(s3Object.getObjectContent(), response.getOutputStream());
        } catch (Exception e) {
            log.error("文件读取异常: {}", e.getLocalizedMessage());
        }
    }

    /**
     * 删除文件
     *
     * @param id
     * @return
     */
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteFile(Long id) {
        Optional<AiFiles> fileOptional = aiFilesRepository.findById(id);
        if (fileOptional.isPresent()) {
            AiFiles file = fileOptional.get();
            ossTemplate.removeObject(ossProperties.getBucketName(), file.getFileName());
            aiFilesRepository.deleteById(id);
            return true;
        }
        return false;
    }

    /**
     * 文件管理数据记录,收集管理追踪文件
     *
     * @param file     上传文件格式
     * @param fileName 文件名
     */
    private AiFiles fileSave(MultipartFile file, String fileName, Long userId) {
        AiFiles aiFiles = new AiFiles();
        aiFiles.setFileName(fileName);
        aiFiles.setFilePath(ossProperties.getBucketName() + "/" + fileName);
        aiFiles.setOriginalFileName(file.getOriginalFilename());
        aiFiles.setFileSize(String.valueOf(file.getSize()));
        aiFiles.setFileType(FileUtil.extName(file.getOriginalFilename()));
        aiFiles.setBucketName(ossProperties.getBucketName());
        aiFiles.setUserId(userId.toString());
        aiFiles.setUploadTime(DateUtil.now());
        // 设置初始状态为上传中
        aiFiles.setAiStatus(FileProcessStatus.UPLOADING);
        return aiFilesRepository.save(aiFiles);
    }

    /**
     * 默认获取文件的在线地址
     *
     * @param bucket
     * @param fileName
     * @return
     */
    public String onlineFile(String bucket, String fileName) {
        return ossTemplate.getObjectURL(bucket, fileName, Duration.of(7, ChronoUnit.DAYS));
    }

    @Override
    public Page<FileDetailDto> findFileDetails(String docType, String originalFileName, Pageable pageable,
            Long currentUserId, Long departmentId) {

        // 获取用户部门权限（用于部门文档查询）
        List<Long> departmentIds = departmentService.getTopParentAndAllChildrenIds(departmentId);

        // 根据指定类型查询文件，排除图片文件
        Page<FileDetailDto> page = aiFilesRepository.findUserNonImageFiles(
                docType, // 直接传递字符串类型
                StrUtil.isNotBlank(originalFileName) ? originalFileName : null,
                pageable,
                currentUserId,
                departmentIds);

        // 补充状态信息
        page.getContent().forEach(this::enrichFileDetailWithStatus);

        return page;
    }

    /**
     * 为FileDetailDto补充状态信息
     */
    private void enrichFileDetailWithStatus(FileDetailDto fileDetail) {
        try {
            // 根据status字符串获取枚举
            FileProcessStatus status = FileProcessStatus.fromCode(fileDetail.getStatus());
            if (status != null) {
                fileDetail.setStatusDescription(status.getDescription());
                fileDetail.setIsProcessing(status.isProcessing());
                fileDetail.setIsCompleted(status.isSuccess());
                fileDetail.setIsFailed(status.isFailure());
            } else {
                fileDetail.setStatusDescription("未知状态");
                fileDetail.setIsProcessing(false);
                fileDetail.setIsCompleted(false);
                fileDetail.setIsFailed(false);
            }

            // 获取错误信息
            if (fileDetail.getFileId() != null) {
                Optional<AiFiles> aiFileOpt = aiFilesRepository.findById(fileDetail.getFileId());
                if (aiFileOpt.isPresent()) {
                    AiFiles aiFile = aiFileOpt.get();
                    fileDetail.setErrorMessage(aiFile.getErrorMessage());
                }
            }

        } catch (Exception e) {
            log.warn("补充文件状态信息失败: fileId={}", fileDetail.getFileId(), e);
            // 设置默认值
            fileDetail.setStatusDescription("状态获取失败");
            fileDetail.setIsProcessing(false);
            fileDetail.setIsCompleted(false);
            fileDetail.setIsFailed(false);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result deleteDocument(Long documentId, HttpServletRequest request) {
        try {
            // 1. 获取当前用户信息
            Long currentUserId = userContextUtil.getCurrentUserId(request);

            // 2. 查找文档
            Optional<Document> documentOpt = documentRepository.findById(documentId);
            if (!documentOpt.isPresent()) {
                return Result.error("文档不存在");
            }

            Document document = documentOpt.get();

            // 3. 检查文档是否属于当前用户
            if (!document.getOwnerId().equals(currentUserId)) {
                return Result.error("您没有权限删除该文档");
            }

            log.info("开始删除文档：ID={}, 标题={}, 用户ID={}", documentId, document.getTitle(), currentUserId);

            // 4. 删除向量库中的数据
            try {
                int deletedVectorCount = literatureQdrantService.deleteVectorsByDocumentId(documentId,
                        "literature_collection");
                log.info("成功删除向量数据，数量：{}", deletedVectorCount);
            } catch (Exception e) {
                log.error("删除向量数据时发生错误", e);
                // 继续执行，不因向量删除失败而终止整个删除过程
            }

            // 5. 删除文档分块数据
            documentChunkRepository.deleteByDocumentId(documentId);
            log.info("成功删除文档分块数据");

            // 6. 删除文档本身
            documentRepository.deleteById(documentId);
            log.info("成功删除文档记录");

            // 7. 如果有关联的文件，也删除文件记录（可选）
            if (document.getFileId() != null) {
                Optional<AiFiles> fileOpt = aiFilesRepository.findById(document.getFileId());
                if (fileOpt.isPresent()) {
                    AiFiles file = fileOpt.get();
                    // 检查是否还有其他文档引用该文件
                    List<Long> relatedDocIds = documentRepository.findIdsByFileId(document.getFileId());
                    if (relatedDocIds.isEmpty()) {
                        // 如果没有其他文档引用，删除文件记录
                        aiFilesRepository.deleteById(document.getFileId());
                        log.info("成功删除关联的文件记录：{}", file.getFileName());
                    }
                }
            }

            return Result.success("文档删除成功");

        } catch (Exception e) {
            log.error("删除文档时发生错误", e);
            return Result.error("删除文档失败：" + e.getMessage());
        }
    }

}
