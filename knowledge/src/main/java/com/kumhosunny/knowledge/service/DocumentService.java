package com.kumhosunny.knowledge.service;

import com.kumhosunny.common.entity.Document;
import com.kumhosunny.common.entity.DocumentChunk;
import com.kumhosunny.common.entity.DocumentPermission;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 文档服务接口
 * 
 * <AUTHOR>
 */
public interface DocumentService {

    /**
     * 创建文档
     */
    Document createDocument(String title, String content, Document.DocumentType type, Long ownerId, Long departmentId);

    /**
     * 根据ID获取文档
     */
    Optional<Document> getDocumentById(Long id);

    /**
     * 更新文档
     */
    Document updateDocument(Long id, String title, String content);

    /**
     * 删除文档（包括所有片段和权限）
     */
    void deleteDocument(Long id);

    /**
     * 分页查询用户可访问的文档
     */
    Page<Document> getAccessibleDocuments(Long userId, Long departmentId, Pageable pageable);

    /**
     * 搜索文档
     */
    List<Document> searchDocuments(String keyword);

    /**
     * 检查用户是否有文档访问权限
     */
    boolean hasAccess(Long documentId, Long userId, Long departmentId);

    /**
     * 检查用户是否有特定权限
     */
    boolean hasPermission(Long documentId, Long userId, Long departmentId, DocumentPermission.PermissionType permission);

    /**
     * 为文档添加用户权限
     */
    DocumentPermission grantUserPermission(Long documentId, Long userId, DocumentPermission.PermissionType permission);

    /**
     * 为文档添加部门权限
     */
    DocumentPermission grantDepartmentPermission(Long documentId, Long departmentId, DocumentPermission.PermissionType permission);

    /**
     * 撤销用户权限
     */
    void revokeUserPermission(Long documentId, Long userId);

    /**
     * 撤销部门权限
     */
    void revokeDepartmentPermission(Long documentId, Long departmentId);

    /**
     * 获取文档的所有权限配置
     */
    List<DocumentPermission> getDocumentPermissions(Long documentId);

    /**
     * 将文档拆分为片段
     */
    List<DocumentChunk> chunkDocument(Long documentId, int chunkSize);

    /**
     * 获取文档的所有片段
     */
    List<DocumentChunk> getDocumentChunks(Long documentId);

    /**
     * 向量化文档片段
     */
    void vectorizeChunks(Long documentId);

    /**
     * 根据向量ID查找片段
     */
    Optional<DocumentChunk> getChunkByVectorId(String vectorId);

    /**
     * 获取待向量化的片段
     */
    List<DocumentChunk> getPendingChunks();

    /**
     * 更新片段的向量化状态
     */
    void updateChunkEmbeddingStatus(Long chunkId, DocumentChunk.EmbeddingStatus status, String vectorId);


    /**
     * 根据内容哈希查找重复文档
     */
    List<Document> findDuplicatesByContent(String content);

    /**
     * 查找指定文档的重复项（排除自身）
     */
    List<Document> findDuplicatesById(Long documentId);

    /**
     * 获取所有重复文档组的统计信息
     */
    java.util.Map<String, Object> getDuplicateStatistics();

    /**
     * 处理重复文档（合并或删除）
     */
    void handleDuplicates(String contentHash, String action, Long keepDocumentId);

    /**
     * 计算文档内容的哈希值
     */
    String calculateContentHash(String content);
} 