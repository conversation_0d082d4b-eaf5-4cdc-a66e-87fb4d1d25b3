package com.kumhosunny.knowledge.service;

import com.kumhosunny.common.dto.ContextualSearchResult;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * 上下文搜索服务接口
 * 提供基于chunk上下文的文档搜索功能
 * 
 * <AUTHOR>
 */
public interface ContextualSearchService {

    /**
     * 带上下文的语义搜索
     * 
     * @param query 查询文本
     * @param topK 返回结果数量
     * @param userId 用户ID
     * @param type 文档类型
     * @param contextSize 上下文大小（前后各contextSize个chunk），默认为1
     * @return 包含上下文的搜索结果
     */
    List<ContextualSearchResult> searchWithContext(String query, int topK, Long userId, String type, Integer contextSize)
            throws IOException, ExecutionException, InterruptedException;

    /**
     * 带上下文的混合搜索
     * 
     * @param query 查询文本
     * @param vectorWeight 向量搜索权重 (0.0-1.0)
     * @param topK 返回结果数量
     * @param userId 用户ID
     * @param type 文档类型
     * @param contextSize 上下文大小（前后各contextSize个chunk），默认为1
     * @return 包含上下文的搜索结果
     */
    List<ContextualSearchResult> hybridSearchWithContext(String query, double vectorWeight, int topK, 
                                                         Long userId, String type, Integer contextSize)
            throws IOException, ExecutionException, InterruptedException;

    /**
     * 根据向量ID获取上下文信息
     * 
     * @param vectorId 向量ID
     * @param contextSize 上下文大小（前后各contextSize个chunk），默认为1
     * @return 包含上下文的结果
     */
    ContextualSearchResult getContextByVectorId(String vectorId, Integer contextSize);
} 