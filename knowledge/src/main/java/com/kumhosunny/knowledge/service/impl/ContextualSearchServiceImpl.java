package com.kumhosunny.knowledge.service.impl;

import com.kumhosunny.common.dto.ContextualSearchResult;
import com.kumhosunny.common.entity.DocumentChunk;
import com.kumhosunny.common.util.LiteratureQdrantUtils;
import com.kumhosunny.knowledge.service.ContextualSearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * 上下文搜索服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ContextualSearchServiceImpl implements ContextualSearchService {

    @Autowired
    private LiteratureQdrantUtils literatureQdrantUtils;

    private static final String DEFAULT_COLLECTION_NAME = "literature_collection";
    private static final Integer DEFAULT_CONTEXT_SIZE = 1;

    @Override
    public List<ContextualSearchResult> searchWithContext(String query, int topK, Long userId, String type, Integer contextSize)
            throws IOException, ExecutionException, InterruptedException {
        
        int actualContextSize = contextSize != null ? contextSize : DEFAULT_CONTEXT_SIZE;
        
        log.info("执行带上下文的语义搜索: query={}, topK={}, userId={}, type={}, contextSize={}", 
                query, topK, userId, type, actualContextSize);
        
        return literatureQdrantUtils.searchWithContext(
            query, 
            DEFAULT_COLLECTION_NAME, 
            topK, 
            userId, 
            type, 
            actualContextSize
        );
    }

    @Override
    public List<ContextualSearchResult> hybridSearchWithContext(String query, double vectorWeight, int topK, 
                                                               Long userId, String type, Integer contextSize)
            throws IOException, ExecutionException, InterruptedException {
        
        int actualContextSize = contextSize != null ? contextSize : DEFAULT_CONTEXT_SIZE;
        
        log.info("执行带上下文的混合搜索: query={}, vectorWeight={}, topK={}, userId={}, type={}, contextSize={}", 
                query, vectorWeight, topK, userId, type, actualContextSize);
        
        return literatureQdrantUtils.hybridSearchWithContext(
            query, 
            DEFAULT_COLLECTION_NAME, 
            vectorWeight, 
            topK, 
            userId, 
            type, 
            actualContextSize
        );
    }

    @Override
    public ContextualSearchResult getContextByVectorId(String vectorId, Integer contextSize) {
        int actualContextSize = contextSize != null ? contextSize : DEFAULT_CONTEXT_SIZE;
        
        log.info("根据向量ID获取上下文: vectorId={}, contextSize={}", vectorId, actualContextSize);
        
        try {
            List<DocumentChunk> contextChunks = literatureQdrantUtils.getContextChunksByVectorId(vectorId, actualContextSize);
            
            if (contextChunks.isEmpty()) {
                log.warn("未找到向量ID对应的上下文: {}", vectorId);
                return null;
            }
            
            // 找到匹配的chunk（vectorId相同的那个）
            DocumentChunk matchedChunk = contextChunks.stream()
                    .filter(chunk -> vectorId.equals(chunk.getVectorId()))
                    .findFirst()
                    .orElse(null);
            
            if (matchedChunk == null) {
                log.warn("在上下文中未找到匹配的chunk: {}", vectorId);
                return null;
            }
            
            // 创建上下文搜索结果
            return new ContextualSearchResult(matchedChunk, contextChunks, 1.0f, "");
            
        } catch (Exception e) {
            log.error("获取上下文信息时发生错误: vectorId={}, error={}", vectorId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 带上下文的语义搜索（使用默认上下文大小）
     */
    public List<ContextualSearchResult> searchWithContext(String query, int topK, Long userId, String type)
            throws IOException, ExecutionException, InterruptedException {
        return searchWithContext(query, topK, userId, type, DEFAULT_CONTEXT_SIZE);
    }

    /**
     * 带上下文的混合搜索（使用默认上下文大小）
     */
    public List<ContextualSearchResult> hybridSearchWithContext(String query, double vectorWeight, int topK, 
                                                               Long userId, String type)
            throws IOException, ExecutionException, InterruptedException {
        return hybridSearchWithContext(query, vectorWeight, topK, userId, type, DEFAULT_CONTEXT_SIZE);
    }

    /**
     * 根据向量ID获取上下文信息（使用默认上下文大小）
     */
    public ContextualSearchResult getContextByVectorId(String vectorId) {
        return getContextByVectorId(vectorId, DEFAULT_CONTEXT_SIZE);
    }
} 