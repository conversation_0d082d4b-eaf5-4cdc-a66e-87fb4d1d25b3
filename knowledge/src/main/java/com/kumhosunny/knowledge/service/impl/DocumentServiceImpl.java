package com.kumhosunny.knowledge.service.impl;

import com.kumhosunny.common.entity.Document;
import com.kumhosunny.common.entity.DocumentChunk;
import com.kumhosunny.common.entity.DocumentPermission;
import com.kumhosunny.common.repository.DocumentRepository;
import com.kumhosunny.common.repository.DocumentChunkRepository;
import com.kumhosunny.common.repository.DocumentPermissionRepository;
import com.kumhosunny.common.util.HashUtils;
import com.kumhosunny.knowledge.service.DocumentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文档服务实现类
 * 
 * <AUTHOR>
 */
@Service
@Transactional
public class DocumentServiceImpl implements DocumentService {

    private static final Logger log = LoggerFactory.getLogger(DocumentServiceImpl.class);

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private DocumentChunkRepository chunkRepository;

    @Autowired
    private DocumentPermissionRepository permissionRepository;

    @Override
    public Document createDocument(String title, String content, Document.DocumentType type, Long ownerId, Long departmentId) {
        log.info("Creating document: title={}, type={}, ownerId={}", title, type, ownerId);
        
        Document document = new Document(title, content, type, ownerId);
        if (departmentId != null) {
            document.setDepartmentId(departmentId);
        }
        
        // 计算并设置内容哈希值
        if (content != null && !content.trim().isEmpty()) {
            String contentHash = HashUtils.normalizedContentHash(content);
            document.setContentHash(contentHash);
            log.debug("Content hash calculated: {}", contentHash);
        }
        
        Document saved = documentRepository.save(document);
        log.info("Document created with ID: {}", saved.getId());
        return saved;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Document> getDocumentById(Long id) {
        return documentRepository.findById(id);
    }

    @Override
    public Document updateDocument(Long id, String title, String content) {
        log.info("Updating document: id={}, title={}", id, title);
        
        Optional<Document> docOpt = documentRepository.findById(id);
        if (docOpt.isPresent()) {
            Document document = docOpt.get();
            document.setTitle(title);
            document.setContent(content);
            
            // 重新计算内容哈希值
            if (content != null && !content.trim().isEmpty()) {
                String contentHash = HashUtils.normalizedContentHash(content);
                document.setContentHash(contentHash);
                log.debug("Content hash recalculated: {}", contentHash);
            } else {
                document.setContentHash(null);
            }
            
            document.setUpdatedAt(LocalDateTime.now());
            return documentRepository.save(document);
        }
        
        throw new RuntimeException("Document not found with ID: " + id);
    }

    @Override
    public void deleteDocument(Long id) {
        log.info("Deleting document: id={}", id);
        
        // 删除所有片段
        chunkRepository.deleteByDocumentId(id);
        
        // 删除所有权限
        permissionRepository.deleteByDocumentId(id);
        
        // 删除文档
        documentRepository.deleteById(id);
        
        log.info("Document deleted: id={}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Document> getAccessibleDocuments(Long userId, Long departmentId, Pageable pageable) {
        return documentRepository.findAccessibleDocuments(userId, departmentId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Document> searchDocuments(String keyword) {
        return documentRepository.searchByKeyword(keyword);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasAccess(Long documentId, Long userId, Long departmentId) {
        return documentRepository.hasAccess(documentId, userId, departmentId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasPermission(Long documentId, Long userId, Long departmentId, DocumentPermission.PermissionType permission) {
        return permissionRepository.hasPermission(documentId, userId, departmentId, permission);
    }

    @Override
    public DocumentPermission grantUserPermission(Long documentId, Long userId, DocumentPermission.PermissionType permission) {
        log.info("Granting user permission: documentId={}, userId={}, permission={}", documentId, userId, permission);
        
        // 检查是否已存在权限
        Optional<DocumentPermission> existing = permissionRepository.findByDocumentIdAndUserId(documentId, userId);
        if (existing.isPresent()) {
            DocumentPermission perm = existing.get();
            perm.setPermission(permission);
            return permissionRepository.save(perm);
        }
        
        DocumentPermission permission_ = new DocumentPermission(documentId, userId, permission);
        return permissionRepository.save(permission_);
    }

    @Override
    public DocumentPermission grantDepartmentPermission(Long documentId, Long departmentId, DocumentPermission.PermissionType permission) {
        log.info("Granting department permission: documentId={}, departmentId={}, permission={}", documentId, departmentId, permission);
        
        // 检查是否已存在权限
        Optional<DocumentPermission> existing = permissionRepository.findByDocumentIdAndDepartmentId(documentId, departmentId);
        if (existing.isPresent()) {
            DocumentPermission perm = existing.get();
            perm.setPermission(permission);
            return permissionRepository.save(perm);
        }
        
        DocumentPermission permission_ = new DocumentPermission();
        permission_.setDocumentId(documentId);
        permission_.setDepartmentId(departmentId);
        permission_.setPermission(permission);
        return permissionRepository.save(permission_);
    }

    @Override
    public void revokeUserPermission(Long documentId, Long userId) {
        log.info("Revoking user permission: documentId={}, userId={}", documentId, userId);
        permissionRepository.deleteByDocumentIdAndUserId(documentId, userId);
    }

    @Override
    public void revokeDepartmentPermission(Long documentId, Long departmentId) {
        log.info("Revoking department permission: documentId={}, departmentId={}", documentId, departmentId);
        permissionRepository.deleteByDocumentIdAndDepartmentId(documentId, departmentId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<DocumentPermission> getDocumentPermissions(Long documentId) {
        return permissionRepository.findByDocumentId(documentId);
    }

    @Override
    public List<DocumentChunk> chunkDocument(Long documentId, int chunkSize) {
        log.info("Chunking document: documentId={}, chunkSize={}", documentId, chunkSize);
        
        Optional<Document> docOpt = documentRepository.findById(documentId);
        if (!docOpt.isPresent()) {
            throw new RuntimeException("Document not found with ID: " + documentId);
        }
        
        Document document = docOpt.get();
        String content = document.getContent();
        if (content == null || content.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 删除现有的片段
        chunkRepository.deleteByDocumentId(documentId);
        
        // 简单的文本分割策略（可以后续优化为更智能的分割）
        List<DocumentChunk> chunks = new ArrayList<>();
        String[] sentences = content.split("[。！？\\n]");
        
        StringBuilder currentChunk = new StringBuilder();
        int chunkIndex = 0;
        
        for (String sentence : sentences) {
            if (sentence.trim().isEmpty()) continue;
            
            // 如果添加这句话会超过chunk大小，先保存当前chunk
            if (currentChunk.length() + sentence.length() > chunkSize && currentChunk.length() > 0) {
                DocumentChunk chunk = new DocumentChunk(documentId, chunkIndex++, currentChunk.toString().trim());
                chunks.add(chunkRepository.save(chunk));
                currentChunk = new StringBuilder();
            }
            
            currentChunk.append(sentence).append("。");
        }
        
        // 保存最后一个chunk
        if (currentChunk.length() > 0) {
            DocumentChunk chunk = new DocumentChunk(documentId, chunkIndex, currentChunk.toString().trim());
            chunks.add(chunkRepository.save(chunk));
        }
        
        log.info("Document chunked into {} pieces", chunks.size());
        return chunks;
    }

    @Override
    @Transactional(readOnly = true)
    public List<DocumentChunk> getDocumentChunks(Long documentId) {
        return chunkRepository.findByDocumentIdOrderByChunkIndexAsc(documentId);
    }

    @Override
    public void vectorizeChunks(Long documentId) {
        log.info("Starting vectorization for document: {}", documentId);
        
        List<DocumentChunk> chunks = chunkRepository.findByDocumentIdAndEmbeddingStatus(
            documentId, DocumentChunk.EmbeddingStatus.pending);
        
        log.info("Found {} chunks to vectorize", chunks.size());
        
        if (chunks.isEmpty()) {
            log.info("No pending chunks found for document: {}", documentId);
            return;
        }
        
        // 这里应该调用向量化服务，暂时只更新状态为pending
        // 实际的向量化逻辑应该在后台异步处理
        // 可以通过调用 LiteratureQdrantUtils 或其他向量化服务来实现
        for (DocumentChunk chunk : chunks) {
            try {
                // TODO: 调用向量化服务生成embedding并存储到Qdrant
                // 例如：literatureQdrantUtils.vectorizeChunk(chunk);
                // String vectorId = vectorService.embed(chunk.getContent());
                // chunk.updateEmbeddingStatus(DocumentChunk.EmbeddingStatus.done, vectorId);
                // chunkRepository.save(chunk);
                
                // 暂时标记为处理中
                chunk.setEmbeddingStatus(DocumentChunk.EmbeddingStatus.pending);
                log.debug("Chunk {} queued for vectorization", chunk.getId());
                
            } catch (Exception e) {
                log.error("Failed to vectorize chunk {}: {}", chunk.getId(), e.getMessage(), e);
                chunk.setEmbeddingStatus(DocumentChunk.EmbeddingStatus.failed);
                chunkRepository.save(chunk);
            }
        }
        
        log.info("Vectorization queued for {} chunks", chunks.size());
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<DocumentChunk> getChunkByVectorId(String vectorId) {
        return chunkRepository.findByVectorId(vectorId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<DocumentChunk> getPendingChunks() {
        return chunkRepository.findByEmbeddingStatusOrderByCreatedAtAsc(DocumentChunk.EmbeddingStatus.pending);
    }

    @Override
    public void updateChunkEmbeddingStatus(Long chunkId, DocumentChunk.EmbeddingStatus status, String vectorId) {
        log.info("Updating chunk embedding status: chunkId={}, status={}, vectorId={}", chunkId, status, vectorId);
        
        Optional<DocumentChunk> chunkOpt = chunkRepository.findById(chunkId);
        if (chunkOpt.isPresent()) {
            DocumentChunk chunk = chunkOpt.get();
            chunk.updateEmbeddingStatus(status, vectorId);
            chunkRepository.save(chunk);
        }
    }


    @Override
    @Transactional(readOnly = true)
    public List<Document> findDuplicatesByContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        String contentHash = HashUtils.normalizedContentHash(content);
        return documentRepository.findAllByContentHash(contentHash);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Document> findDuplicatesById(Long documentId) {
        log.info("Finding duplicates for document: {}", documentId);
        
        Optional<Document> docOpt = documentRepository.findById(documentId);
        if (!docOpt.isPresent()) {
            log.warn("Document not found: {}", documentId);
            return new ArrayList<>();
        }
        
        Document document = docOpt.get();
        if (document.getContentHash() == null) {
            log.warn("Document {} has no content hash", documentId);
            return new ArrayList<>();
        }
        
        return documentRepository.findDuplicatesByContentHash(document.getContentHash(), documentId);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getDuplicateStatistics() {
        log.info("Generating duplicate document statistics");
        
        Map<String, Object> stats = new HashMap<>();
        
        // 总文档数
        long totalDocuments = documentRepository.count();
        stats.put("totalDocuments", totalDocuments);
        
        // 重复文档数量
        long duplicateCount = documentRepository.countDuplicateDocuments();
        stats.put("duplicateDocuments", duplicateCount);
        
        // 去重后节省的存储数量
        stats.put("potentialSavings", duplicateCount);
        
        // 重复率
        double duplicateRate = totalDocuments > 0 ? (double) duplicateCount / totalDocuments * 100 : 0;
        stats.put("duplicateRate", Math.round(duplicateRate * 100.0) / 100.0);
        
        // 重复组详情
        List<Object[]> duplicateGroups = documentRepository.findDuplicateGroups();
        List<Map<String, Object>> groupDetails = duplicateGroups.stream()
                .map(group -> {
                    Map<String, Object> detail = new HashMap<>();
                    detail.put("contentHash", group[0]);
                    detail.put("count", group[1]);
                    return detail;
                })
                .collect(Collectors.toList());
        
        stats.put("duplicateGroups", groupDetails);
        stats.put("duplicateGroupCount", groupDetails.size());
        
        log.info("Duplicate statistics: total={}, duplicates={}, rate={}%", 
                totalDocuments, duplicateCount, duplicateRate);
        
        return stats;
    }

    @Override
    @Transactional
    public void handleDuplicates(String contentHash, String action, Long keepDocumentId) {
        log.info("Handling duplicates: contentHash={}, action={}, keepId={}", 
                contentHash, action, keepDocumentId);
        
        if (contentHash == null || contentHash.trim().isEmpty()) {
            throw new IllegalArgumentException("Content hash cannot be null or empty");
        }
        
        List<Document> duplicates = documentRepository.findAllByContentHash(contentHash);
        if (duplicates.size() <= 1) {
            log.info("No duplicates found for hash: {}", contentHash);
            return;
        }
        
        switch (action.toLowerCase()) {
            case "delete_all_except_first":
                handleDeleteAllExceptFirst(duplicates);
                break;
            case "delete_all_except_keep":
                handleDeleteAllExceptKeep(duplicates, keepDocumentId);
                break;
            case "merge_into_keep":
                handleMergeIntoKeep(duplicates, keepDocumentId);
                break;
            default:
                throw new IllegalArgumentException("Unsupported action: " + action);
        }
        
        log.info("Duplicate handling completed for hash: {}", contentHash);
    }

    @Override
    public String calculateContentHash(String content) {
        return HashUtils.normalizedContentHash(content);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 删除除第一个文档外的所有重复文档
     */
    private void handleDeleteAllExceptFirst(List<Document> duplicates) {
        // 按创建时间排序，保留最早的
        duplicates.sort(Comparator.comparing(Document::getCreatedAt));
        
        Document keepDocument = duplicates.get(0);
        log.info("Keeping first document: {} (created: {})", keepDocument.getId(), keepDocument.getCreatedAt());
        
        for (int i = 1; i < duplicates.size(); i++) {
            Document toDelete = duplicates.get(i);
            log.info("Deleting duplicate document: {} (created: {})", toDelete.getId(), toDelete.getCreatedAt());
            deleteDocument(toDelete.getId());
        }
    }

    /**
     * 删除除指定文档外的所有重复文档
     */
    private void handleDeleteAllExceptKeep(List<Document> duplicates, Long keepDocumentId) {
        if (keepDocumentId == null) {
            throw new IllegalArgumentException("Keep document ID cannot be null");
        }
        
        Document keepDocument = duplicates.stream()
                .filter(doc -> doc.getId().equals(keepDocumentId))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Keep document not found in duplicates: " + keepDocumentId));
        
        log.info("Keeping specified document: {}", keepDocument.getId());
        
        duplicates.stream()
                .filter(doc -> !doc.getId().equals(keepDocumentId))
                .forEach(doc -> {
                    log.info("Deleting duplicate document: {}", doc.getId());
                    deleteDocument(doc.getId());
                });
    }

    /**
     * 将所有重复文档的权限合并到指定文档，然后删除其他文档
     */
    private void handleMergeIntoKeep(List<Document> duplicates, Long keepDocumentId) {
        if (keepDocumentId == null) {
            throw new IllegalArgumentException("Keep document ID cannot be null");
        }
        
        Document keepDocument = duplicates.stream()
                .filter(doc -> doc.getId().equals(keepDocumentId))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Keep document not found in duplicates: " + keepDocumentId));
        
        log.info("Merging into document: {}", keepDocument.getId());
        
        // 收集所有其他文档的权限
        for (Document duplicate : duplicates) {
            if (!duplicate.getId().equals(keepDocumentId)) {
                // 迁移权限
                List<DocumentPermission> permissions = getDocumentPermissions(duplicate.getId());
                for (DocumentPermission permission : permissions) {
                    try {
                        if (permission.getUserId() != null) {
                            grantUserPermission(keepDocumentId, permission.getUserId(), permission.getPermission());
                        } else if (permission.getDepartmentId() != null) {
                            grantDepartmentPermission(keepDocumentId, permission.getDepartmentId(), permission.getPermission());
                        }
                    } catch (Exception e) {
                        log.warn("Failed to migrate permission from {} to {}: {}", 
                                duplicate.getId(), keepDocumentId, e.getMessage());
                    }
                }
                
                // 删除重复文档
                log.info("Deleting merged document: {}", duplicate.getId());
                deleteDocument(duplicate.getId());
            }
        }
    }
} 