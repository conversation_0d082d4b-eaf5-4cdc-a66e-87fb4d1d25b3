package com.kumhosunny.knowledge.service;

import com.kumhosunny.common.result.Result;
import com.kumhosunny.knowledge.req.AiFilesReq;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;
import com.kumhosunny.common.dto.FileDetailDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.concurrent.ExecutionException;

/**
 * @program: kum-ai-app-backend
 * @description:
 * @author: wye
 * @create: 2025-06-18 13:50
 **/
public interface FileService {

    public Result upload(HttpServletRequest request, AiFilesReq req) throws Exception;

    public Result upload(HttpServletRequest request, MultipartFile file) throws Exception;

    Page<FileDetailDto> findFileDetails(String docType, String originalFileName, Pageable pageable, Long currentUserId,
            Long departmentId);

    Result recall(Long fileId, String query);

    /**
     * 根据文档ID删除文档
     *
     * @param documentId 文档ID
     * @param request    HTTP请求对象，用于获取当前用户信息
     * @return 删除结果
     */
    Result deleteDocument(Long documentId, HttpServletRequest request);

}
