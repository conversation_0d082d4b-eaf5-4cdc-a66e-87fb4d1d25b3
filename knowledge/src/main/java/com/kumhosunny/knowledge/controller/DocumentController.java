package com.kumhosunny.knowledge.controller;

import com.kumhosunny.common.entity.Document;
import com.kumhosunny.common.entity.DocumentChunk;
import com.kumhosunny.common.entity.DocumentPermission;
import com.kumhosunny.common.result.ApiResponse;
import com.kumhosunny.common.util.UserContextUtil;
import com.kumhosunny.common.util.ThreadPoolMonitor;
import com.kumhosunny.knowledge.service.DocumentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 文档管理控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/documents")
@CrossOrigin(origins = "*")
public class DocumentController {

    private static final Logger log = LoggerFactory.getLogger(DocumentController.class);

    @Autowired
    private DocumentService documentService;

    @Autowired
    private UserContextUtil userContextUtil;

    @Autowired
    private ThreadPoolMonitor threadPoolMonitor;

    /**
     * 创建文档
     */
    @PostMapping
    public ApiResponse<Document> createDocument(@RequestBody Map<String, Object> request, HttpServletRequest httpRequest) {
        try {
            Long userId = userContextUtil.getCurrentUserId(httpRequest);
            
            String title = (String) request.get("title");
            String content = (String) request.get("content");
            String typeStr = (String) request.get("type");
            Object deptIdObj = request.get("departmentId");
            
            Document.DocumentType type = Document.DocumentType.valueOf(typeStr);
            Long departmentId = deptIdObj != null ? Long.valueOf(deptIdObj.toString()) : null;
            
            Document document = documentService.createDocument(title, content, type, userId, departmentId);
            return ApiResponse.success(document);
            
        } catch (Exception e) {
            log.error("Failed to create document", e);
            return ApiResponse.error("创建文档失败: " + e.getMessage());
        }
    }

    /**
     * 获取文档详情
     */
    @GetMapping("/{id}")
    public ApiResponse<Document> getDocument(@PathVariable Long id, HttpServletRequest httpRequest) {
        try {
            Long userId = userContextUtil.getCurrentUserId(httpRequest);
            // TODO: 获取用户部门ID
            Long departmentId = 1L; // 临时硬编码，应该从用户信息中获取
            
            // 检查访问权限
            if (!documentService.hasAccess(id, userId, departmentId)) {
                return ApiResponse.error("无权限访问该文档");
            }
            
            Optional<Document> document = documentService.getDocumentById(id);
            if (document.isPresent()) {
                return ApiResponse.success(document.get());
            } else {
                return ApiResponse.error("文档不存在");
            }
            
        } catch (Exception e) {
            log.error("Failed to get document: {}", id, e);
            return ApiResponse.error("获取文档失败: " + e.getMessage());
        }
    }

    /**
     * 更新文档
     */
    @PutMapping("/{id}")
    public ApiResponse<Document> updateDocument(@PathVariable Long id, @RequestBody Map<String, Object> request, HttpServletRequest httpRequest) {
        try {
            Long userId = userContextUtil.getCurrentUserId(httpRequest);
            Long departmentId = 1L; // 临时硬编码
            
            // 检查编辑权限
            if (!documentService.hasPermission(id, userId, departmentId, DocumentPermission.PermissionType.write)) {
                return ApiResponse.error("无权限编辑该文档");
            }
            
            String title = (String) request.get("title");
            String content = (String) request.get("content");
            
            Document document = documentService.updateDocument(id, title, content);
            return ApiResponse.success(document);
            
        } catch (Exception e) {
            log.error("Failed to update document: {}", id, e);
            return ApiResponse.error("更新文档失败: " + e.getMessage());
        }
    }

    /**
     * 删除文档
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteDocument(@PathVariable Long id, HttpServletRequest httpRequest) {
        try {
            Long userId = userContextUtil.getCurrentUserId(httpRequest);
            Long departmentId = 1L; // 临时硬编码
            
            // 检查管理员权限
            if (!documentService.hasPermission(id, userId, departmentId, DocumentPermission.PermissionType.admin)) {
                return ApiResponse.error("无权限删除该文档");
            }
            
            documentService.deleteDocument(id);
            return ApiResponse.success(null);
            
        } catch (Exception e) {
            log.error("Failed to delete document: {}", id, e);
            return ApiResponse.error("删除文档失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询可访问的文档
     */
    @GetMapping
    public ApiResponse<Page<Document>> getAccessibleDocuments(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            HttpServletRequest httpRequest) {
        try {
            Long userId = userContextUtil.getCurrentUserId(httpRequest);
            Long departmentId = 1L; // 临时硬编码
            
            Pageable pageable = PageRequest.of(page, size);
            Page<Document> documents = documentService.getAccessibleDocuments(userId, departmentId, pageable);
            return ApiResponse.success(documents);
            
        } catch (Exception e) {
            log.error("Failed to get accessible documents", e);
            return ApiResponse.error("获取文档列表失败: " + e.getMessage());
        }
    }

    /**
     * 搜索文档
     */
    @GetMapping("/search")
    public ApiResponse<List<Document>> searchDocuments(@RequestParam String keyword) {
        try {
            List<Document> documents = documentService.searchDocuments(keyword);
            return ApiResponse.success(documents);
            
        } catch (Exception e) {
            log.error("Failed to search documents with keyword: {}", keyword, e);
            return ApiResponse.error("搜索文档失败: " + e.getMessage());
        }
    }

    /**
     * 将文档拆分为片段
     */
    @PostMapping("/{id}/chunk")
    public ApiResponse<List<DocumentChunk>> chunkDocument(@PathVariable Long id, @RequestParam(defaultValue = "500") int chunkSize) {
        try {
            List<DocumentChunk> chunks = documentService.chunkDocument(id, chunkSize);
            return ApiResponse.success(chunks);
            
        } catch (Exception e) {
            log.error("Failed to chunk document: {}", id, e);
            return ApiResponse.error("文档分片失败: " + e.getMessage());
        }
    }

    /**
     * 获取文档的所有片段
     */
    @GetMapping("/{id}/chunks")
    public ApiResponse<List<DocumentChunk>> getDocumentChunks(@PathVariable Long id) {
        try {
            List<DocumentChunk> chunks = documentService.getDocumentChunks(id);
            return ApiResponse.success(chunks);
            
        } catch (Exception e) {
            log.error("Failed to get document chunks: {}", id, e);
            return ApiResponse.error("获取文档片段失败: " + e.getMessage());
        }
    }

    /**
     * 向量化文档片段
     */
    @PostMapping("/{id}/vectorize")
    public ApiResponse<Void> vectorizeDocument(@PathVariable Long id) {
        try {
            documentService.vectorizeChunks(id);
            return ApiResponse.success(null);
            
        } catch (Exception e) {
            log.error("Failed to vectorize document: {}", id, e);
            return ApiResponse.error("文档向量化失败: " + e.getMessage());
        }
    }

    /**
     * 为文档授权用户权限
     */
    @PostMapping("/{id}/permissions/users/{userId}")
    public ApiResponse<DocumentPermission> grantUserPermission(
            @PathVariable Long id,
            @PathVariable Long userId,
            @RequestParam DocumentPermission.PermissionType permission) {
        try {
            DocumentPermission perm = documentService.grantUserPermission(id, userId, permission);
            return ApiResponse.success(perm);
            
        } catch (Exception e) {
            log.error("Failed to grant user permission: documentId={}, userId={}", id, userId, e);
            return ApiResponse.error("授权失败: " + e.getMessage());
        }
    }

    /**
     * 为文档授权部门权限
     */
    @PostMapping("/{id}/permissions/departments/{departmentId}")
    public ApiResponse<DocumentPermission> grantDepartmentPermission(
            @PathVariable Long id,
            @PathVariable Long departmentId,
            @RequestParam DocumentPermission.PermissionType permission) {
        try {
            DocumentPermission perm = documentService.grantDepartmentPermission(id, departmentId, permission);
            return ApiResponse.success(perm);
            
        } catch (Exception e) {
            log.error("Failed to grant department permission: documentId={}, departmentId={}", id, departmentId, e);
            return ApiResponse.error("授权失败: " + e.getMessage());
        }
    }

    /**
     * 撤销用户权限
     */
    @DeleteMapping("/{id}/permissions/users/{userId}")
    public ApiResponse<Void> revokeUserPermission(@PathVariable Long id, @PathVariable Long userId) {
        try {
            documentService.revokeUserPermission(id, userId);
            return ApiResponse.success(null);
            
        } catch (Exception e) {
            log.error("Failed to revoke user permission: documentId={}, userId={}", id, userId, e);
            return ApiResponse.error("撤销权限失败: " + e.getMessage());
        }
    }

    /**
     * 获取文档的权限配置
     */
    @GetMapping("/{id}/permissions")
    public ApiResponse<List<DocumentPermission>> getDocumentPermissions(@PathVariable Long id) {
        try {
            List<DocumentPermission> permissions = documentService.getDocumentPermissions(id);
            return ApiResponse.success(permissions);
            
        } catch (Exception e) {
            log.error("Failed to get document permissions: {}", id, e);
            return ApiResponse.error("获取权限配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取向量化线程池状态
     */
    @GetMapping("/system/threadpool/status")
    public ApiResponse<ThreadPoolMonitor.ThreadPoolStats> getThreadPoolStatus() {
        try {
            ThreadPoolMonitor.ThreadPoolStats stats = threadPoolMonitor.getVectorizationThreadPoolStats();
            return ApiResponse.success(stats);
            
        } catch (Exception e) {
            log.error("Failed to get thread pool status", e);
            return ApiResponse.error("获取线程池状态失败: " + e.getMessage());
        }
    }

    /**
     * 根据内容查找重复文档
     */
    @PostMapping("/find-duplicates-by-content")
    public ApiResponse<List<Document>> findDuplicatesByContent(@RequestBody Map<String, String> request) {
        try {
            String content = request.get("content");
            if (content == null || content.trim().isEmpty()) {
                return ApiResponse.error("文档内容不能为空");
            }
            
            List<Document> duplicates = documentService.findDuplicatesByContent(content);
            return ApiResponse.success(duplicates);
            
        } catch (Exception e) {
            log.error("Failed to find duplicates by content", e);
            return ApiResponse.error("查找重复内容失败: " + e.getMessage());
        }
    }

    /**
     * 查找指定文档的重复项
     */
    @GetMapping("/{id}/duplicates")
    public ApiResponse<List<Document>> findDuplicatesById(@PathVariable Long id) {
        try {
            List<Document> duplicates = documentService.findDuplicatesById(id);
            return ApiResponse.success(duplicates);
            
        } catch (Exception e) {
            log.error("Failed to find duplicates for document: {}", id, e);
            return ApiResponse.error("查找文档重复项失败: " + e.getMessage());
        }
    }

    /**
     * 获取重复文档统计信息
     */
    @GetMapping("/duplicate-statistics")
    public ApiResponse<Map<String, Object>> getDuplicateStatistics() {
        try {
            Map<String, Object> statistics = documentService.getDuplicateStatistics();
            return ApiResponse.success(statistics);
            
        } catch (Exception e) {
            log.error("Failed to get duplicate statistics", e);
            return ApiResponse.error("获取重复统计失败: " + e.getMessage());
        }
    }

    /**
     * 处理重复文档
     */
    @PostMapping("/handle-duplicates")
    public ApiResponse<String> handleDuplicates(@RequestBody Map<String, Object> request) {
        try {
            String contentHash = (String) request.get("contentHash");
            String action = (String) request.get("action");
            Object keepDocumentIdObj = request.get("keepDocumentId");
            
            if (contentHash == null || contentHash.trim().isEmpty()) {
                return ApiResponse.error("内容哈希不能为空");
            }
            
            if (action == null || action.trim().isEmpty()) {
                return ApiResponse.error("操作类型不能为空");
            }
            
            Long keepDocumentId = null;
            if (keepDocumentIdObj != null) {
                if (keepDocumentIdObj instanceof Number) {
                    keepDocumentId = ((Number) keepDocumentIdObj).longValue();
                } else {
                    try {
                        keepDocumentId = Long.parseLong(keepDocumentIdObj.toString());
                    } catch (NumberFormatException e) {
                        return ApiResponse.error("保留文档ID格式错误");
                    }
                }
            }
            
            documentService.handleDuplicates(contentHash, action, keepDocumentId);
            return ApiResponse.success("重复文档处理成功");
            
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to handle duplicates", e);
            return ApiResponse.error("处理重复文档失败: " + e.getMessage());
        }
    }

    /**
     * 计算文档内容的哈希值
     */
    @PostMapping("/calculate-hash")
    public ApiResponse<Map<String, String>> calculateContentHash(@RequestBody Map<String, String> request) {
        try {
            String content = request.get("content");
            if (content == null || content.trim().isEmpty()) {
                return ApiResponse.error("文档内容不能为空");
            }
            
            String hash = documentService.calculateContentHash(content);
            Map<String, String> result = new HashMap<>();
            result.put("contentHash", hash);
            result.put("shortHash", hash != null && hash.length() >= 8 ? hash.substring(0, 8) : null);
            
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("Failed to calculate content hash", e);
            return ApiResponse.error("计算内容哈希失败: " + e.getMessage());
        }
    }
} 