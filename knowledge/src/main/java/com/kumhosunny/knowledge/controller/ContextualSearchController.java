package com.kumhosunny.knowledge.controller;

import com.kumhosunny.common.dto.ContextualSearchResult;
import com.kumhosunny.common.result.ApiResponse;
import com.kumhosunny.knowledge.service.ContextualSearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * 上下文搜索控制器
 * 提供带上下文的文档搜索API
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/contextual-search")
public class ContextualSearchController {

    @Autowired
    private ContextualSearchService contextualSearchService;

    /**
     * 带上下文的语义搜索
     * 
     * @param query 查询文本
     * @param topK 返回结果数量，默认10
     * @param userId 用户ID
     * @param type 文档类型
     * @param contextSize 上下文大小（前后各contextSize个chunk），默认1
     * @return 包含上下文的搜索结果
     */
    @GetMapping("/semantic")
    public ApiResponse<List<ContextualSearchResult>> semanticSearchWithContext(
            @RequestParam String query,
            @RequestParam(defaultValue = "10") int topK,
            @RequestParam Long userId,
            @RequestParam String type,
            @RequestParam(defaultValue = "1") Integer contextSize) {
        
        try {
            log.info("收到语义搜索请求: query={}, topK={}, userId={}, type={}, contextSize={}", 
                    query, topK, userId, type, contextSize);
            
            List<ContextualSearchResult> results = contextualSearchService.searchWithContext(
                query, topK, userId, type, contextSize);
            
            return ApiResponse.success(results);
            
        } catch (IOException | ExecutionException | InterruptedException e) {
            log.error("语义搜索发生错误: {}", e.getMessage(), e);
            return ApiResponse.error("搜索失败: " + e.getMessage());
        }
    }

    /**
     * 带上下文的混合搜索
     * 
     * @param query 查询文本
     * @param vectorWeight 向量搜索权重 (0.0-1.0)，默认0.7
     * @param topK 返回结果数量，默认10
     * @param userId 用户ID
     * @param type 文档类型
     * @param contextSize 上下文大小（前后各contextSize个chunk），默认1
     * @return 包含上下文的搜索结果
     */
    @GetMapping("/hybrid")
    public ApiResponse<List<ContextualSearchResult>> hybridSearchWithContext(
            @RequestParam String query,
            @RequestParam(defaultValue = "0.7") double vectorWeight,
            @RequestParam(defaultValue = "10") int topK,
            @RequestParam Long userId,
            @RequestParam String type,
            @RequestParam(defaultValue = "1") Integer contextSize) {
        
        try {
            log.info("收到混合搜索请求: query={}, vectorWeight={}, topK={}, userId={}, type={}, contextSize={}", 
                    query, vectorWeight, topK, userId, type, contextSize);
            
            List<ContextualSearchResult> results = contextualSearchService.hybridSearchWithContext(
                query, vectorWeight, topK, userId, type, contextSize);
            
            return ApiResponse.success(results);
            
        } catch (IOException | ExecutionException | InterruptedException e) {
            log.error("混合搜索发生错误: {}", e.getMessage(), e);
            return ApiResponse.error("搜索失败: " + e.getMessage());
        }
    }

    /**
     * 根据向量ID获取上下文信息
     * 
     * @param vectorId 向量ID
     * @param contextSize 上下文大小（前后各contextSize个chunk），默认1
     * @return 包含上下文的结果
     */
    @GetMapping("/context/{vectorId}")
    public ApiResponse<ContextualSearchResult> getContextByVectorId(
            @PathVariable String vectorId,
            @RequestParam(defaultValue = "1") Integer contextSize) {
        
        try {
            log.info("收到获取上下文请求: vectorId={}, contextSize={}", vectorId, contextSize);
            
            ContextualSearchResult result = contextualSearchService.getContextByVectorId(vectorId, contextSize);
            
            if (result == null) {
                return ApiResponse.error("未找到对应的上下文信息");
            }
            
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("获取上下文信息发生错误: {}", e.getMessage(), e);
            return ApiResponse.error("获取上下文失败: " + e.getMessage());
        }
    }

    /**
     * 批量获取向量ID的上下文信息
     * 
     * @param vectorIds 向量ID列表
     * @param contextSize 上下文大小（前后各contextSize个chunk），默认1
     * @return 包含上下文的结果列表
     */
    @PostMapping("/context/batch")
    public ApiResponse<List<ContextualSearchResult>> getBatchContextByVectorIds(
            @RequestBody List<String> vectorIds,
            @RequestParam(defaultValue = "1") Integer contextSize) {
        
        try {
            log.info("收到批量获取上下文请求: vectorIds.size={}, contextSize={}", vectorIds.size(), contextSize);
            
            List<ContextualSearchResult> results = vectorIds.stream()
                    .map(vectorId -> contextualSearchService.getContextByVectorId(vectorId, contextSize))
                    .filter(result -> result != null)
                    .collect(java.util.stream.Collectors.toList());
            
            return ApiResponse.success(results);
            
        } catch (Exception e) {
            log.error("批量获取上下文信息发生错误: {}", e.getMessage(), e);
            return ApiResponse.error("批量获取上下文失败: " + e.getMessage());
        }
    }
} 